/** @type {import('next').NextConfig} */

const nextConfig = {
  reactStrictMode: false,
  assetPrefix: "/crm",
  swcMinify: false,
  basePath: "/crm",
  staticPageGenerationTimeout: 1000,
  compiler: {
    styledComponents: true,
  },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "**",
      },
      {
        protocol: "http",
        hostname: "**",
      },
    ],
  },
  
};

export default nextConfig;
