{
  "cSpell.words": ["Datepicker", "firstname", "Grop<PERSON>", "lastname", "uuidv"],
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.detectIndentation": false,
  "editor.snippetSuggestions": "top",
  "editor.tabSize": 2,
  "editor.autoIndent": "full",
  "editor.acceptSuggestionOnCommitCharacter": true,
  "editor.autoClosingBrackets": "always",
  "editor.autoClosingQuotes": "always",
  "editor.colorDecorators": true,
  "editor.insertSpaces": true,
  "editor.wordWrap": "wordWrapColumn",
  "editor.wordWrapColumn": 240,
  "editor.formatOnPaste": false,
  "editor.formatOnSave": true,
  "files.trimFinalNewlines": true,
  "editor.codeActionsOnSave": {
    "source.fixAll": "explicit",
    "source.organizeImports": "explicit"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "vscode.typescript-language-features"
  },
  "[javascriptreact]": {
    "editor.defaultFormatter": "rvest.vs-code-prettier-eslint"
  },
  "[javascript]": {
    "editor.defaultFormatter": "vscode.typescript-language-features"
  }
  // "[typescript]": {
  //   "editor.defaultFormatter": "vscode.typescript-language-features"
  // }
}
