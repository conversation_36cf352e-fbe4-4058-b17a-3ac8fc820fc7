# Use an official Node.js runtime as a parent image
FROM node:20.10.0-alpine

# Set the working directory to /app
WORKDIR /app

# Copy package.json and package-lock.json to the working directory
COPY package*.json ./

# Copy .npmrc to the working directory
COPY .npmrc ./

# Install app dependencies
RUN npm install

# Bundle app source
COPY . .

# Build the React app
RUN npm run build

# Expose the port the app runs on
EXPOSE 3008

# Run the application
CMD ["npm", "start"]