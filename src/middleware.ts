
import { NextRequest, NextResponse } from "next/server";

async function fetchSupportedLanguages(): Promise<string[]> {
  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE}/v1/language/filter`, {
      method: "POST",
      headers: {
        "x-api-key": "1ab2c3d4e5f61ab2c3d4e5f6",
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        filters: { dynamicObject: {} },
        pagination: { limit: 50, page: 1 },
        search: "",
        sort: { field: "id", dir: "DESC" },
      }),
    });

    const data = await res.json();
    return data?.data?.data?.map((item: { code: string }) => item.code) || [];
  } catch (error) {
    console.error("Error fetching supported languages:", error);
    return [];
  }
}


export async function middleware(request: NextRequest) {
  const url = request.nextUrl.clone();
  const langSegement = url.pathname.split('/')[1];
  const lang = request.cookies.get("language")?.value;

  let supportedLang = request.cookies.get("supported_language")?.value?.split(",").map(lang => lang.trim());

  let response = NextResponse.next();
  if (!supportedLang) {
    supportedLang = await fetchSupportedLanguages();
    response.cookies.set("supported_language", supportedLang?.join(","))
  }


  if (!supportedLang.includes(langSegement) || langSegement === "crm") {
    if (!lang) {
      url.pathname = `/en/${url.pathname}/`
      response.cookies.set("language", "en")
    } else {
      url.pathname = `/${lang}/${url.pathname}/`
    }
    response = NextResponse.redirect(url);
    response.cookies.set("supported_language", supportedLang?.join(","))
    return response;
  }
  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - scene.vjson (3D scene file)
     * - config.json (configuration file)
     * - *.png (PNG image files)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|scene.vjson|config.json|.*\\.png).*)',
    "/"
  ],
};