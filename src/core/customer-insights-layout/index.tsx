import { GlobalStyle } from "@dilpesh/kgk-ui-library";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>er, Offer<PERSON>eader, TopMsg, useGetAppData } from "@magneto-it-solutions/kgk-common-library";
import 'react-toastify/dist/ReactToastify.css';
import "swiper/css";
import "swiper/css/navigation";



const CommonCustomerInsightsLayout = (props: {
    children: React.ReactNode
}) => {

    const { isAuthenticated } = useGetAppData();

    return (
        <>
            {props.children}
        </>
    )
};

export default CommonCustomerInsightsLayout;

