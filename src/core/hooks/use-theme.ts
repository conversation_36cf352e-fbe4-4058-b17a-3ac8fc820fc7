import { LibType } from "@dilpesh/kgk-ui-library";
import { getLocalStorage, LOCAL_STORAGE_KEYS, setLocalStorage } from "@magneto-it-solutions/kgk-common-library";
import { useEffect, useState } from "react";

export type ThemeType = LibType | "";

const useCurrentTheme = () => {
  const [isLoading, setIsLoading] = useState(true);
  let theme = getLocalStorage(LOCAL_STORAGE_KEYS.THEME) ?? ""
  if (theme && !["KGK", "Martin", "Entice"].includes(theme)) {
    theme = "";
  }
  const [currentTheme, setCurrentTheme] = useState<ThemeType>(theme as ThemeType);

  const fetchTheme = async () => {
    try {
      const res = await fetch(`${process.env.NEXT_PUBLIC_CMS_BASE_URL}/api/theme`, {
        method: "GET",
      });
      const data = await res.json();
      const theme = data?.data?.attributes?.theme;
      if (["KGK", "Martin", "Entice"].includes(theme)) {
        setCurrentTheme(theme as LibType);
        setLocalStorage(LOCAL_STORAGE_KEYS.THEME, theme)
      } else {
        setCurrentTheme("KGK");
      }
    } catch (error) {
      setCurrentTheme("KGK");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchTheme();
  }, []);

  return { isLoading, currentTheme };
};

export default useCurrentTheme;
