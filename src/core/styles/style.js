import { Media, bes } from '@dilpesh/kgk-ui-library';
import { createGlobalStyle, css } from 'styled-components';

const FrontendStyle = createGlobalStyle`
    ${(props) =>
    css`
            .row-5{
                margin-left: -12px;
                margin-right: -12px;
                display: flex;
                flex-flow: row wrap;
                min-width: 0;
                @media ${Media.mobile}{
                    margin-left: -6px;
                    margin-right: -6px;
                }
                & > * {
                    padding-left: 12px;
                    padding-right: 12px;
                    display: block;
                    @media ${Media.mobile}{
                        padding-left: 6px;
                        padding-right: 6px;
                    }
                }
                .column-1{
                    flex: 0 0 20%;
                    max-width: 20%;
                    @media ${Media.below_1599}{
                        flex: 0 0 25%;
                        max-width: 25%;
                    }
                    @media ${Media.tablet}{
                        flex: 0 0 33.33%;
                        max-width: 33.33%;
                    }
                    @media ${Media.ultra_mobile}{
                        flex: 0 0 50%;
                        max-width: 50%;
                    }
                }
                .column-2{
                    flex: 0 0 40%;
                    max-width: 40%;
                }
                .column-3{
                    flex: 0 0 60%;
                    max-width: 60%;
                    @media ${Media.below_1599}{
                        flex: 0 0 50%;
                        max-width: 50%;
                    }
                    @media ${Media.tablet}{
                        flex: 0 0 66.66%;
                        max-width: 66.66%;
                    }
                    @media ${Media.ultra_mobile}{
                        flex: 0 0 100%;
                        max-width: 100%;
                    }
                }
                .column-4{
                    flex: 0 0 80%;
                    max-width: 80%;
                }
                .column-5{
                    flex: 0 0 100%;
                    max-width: 100%;
                }
            }
            .hidden_button{
              background:transparent;
              border:none;
              outline:none;
              cursor:pointer;
            }
            .product_grid_column{
                &.ant-col-xxl-4{
                    // @media ${Media.above_1600}{
                        // flex: 0 0 20%;
                        // max-width: 20%;
                    // }
                    @media ${Media.above_1800}{
                        flex: 0 0 16.666666666666664%;
                        max-width: 16.666666666666664%;
                    }
                }
            }


            .it_24_besm_12, .it_24_besm_8, .icon_name_40_br{
                display: inline-flex;
                justify-content: flex-start;
                align-items: center;
            }
            .it_24_besm_12{
                svg, .img_wpr{
                    height: 24px;
                    width: 24px;
                }
                p{
                    padding-left: 12px;
                    ${bes(props?.theme?.text?.high, 500)}
                }
            }
            .it_24_besm_8{
                svg, .img_wpr{
                    height: 24px;
                    width: 24px;
                }
                p{
                    padding-left: 8px;
                    ${bes(props?.theme?.text?.high, 500)}
                }
            }
            .icon_name_40_br{
                svg, .img_wpr{
                    height: 40px;
                    width: 40px;
                    path{
                        stroke: ${(props.theme.colors.brand_high)};
                    }
                }
                p{
                    padding-left: 12px;
                    ${bes(props?.theme?.text?.high, 400)}
                }
            }
            .ant-drawer-content-wrapper{
                box-shadow: none !important;
            }
        `
  } 
`;

export default FrontendStyle;