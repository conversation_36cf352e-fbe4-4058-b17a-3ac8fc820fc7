import { bes, h3 } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const SalesmanInsightsPagesStyle = styled.div`
    ${(props) =>
        css`
            padding-top: 32px;
            padding-bottom: 64px;
            .bread_crumbs{
                margin-bottom: 24px;
            }
            .back_btn{
                display: flex;
                margin-bottom: 24px;
                a{
                    display: inline-flex;
                    align-items: center;
                    justify-content: flex-start;
                    svg{
                        height: 24px;
                        width: 24px;
                        path{
                            stroke: ${(props.theme.colors.brand_high)};
                        }
                    }
                    p{
                        width: calc(100% - 24px);
                        padding-left:8px;
                        ${bes(props?.theme?.text?.high, 500)};
                    }
                }
            }
            h1 {
                ${h3(props?.theme?.text?.high, 400)};
                margin-bottom: 40px;
            }
            .tab_main_wpr{
                margin-bottom: 32px;
            }
            
        `
    }

`