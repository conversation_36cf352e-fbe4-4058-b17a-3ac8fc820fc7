"use client"
import ThemeProvider from "@/core/theme-provider";
import { LibType } from "@dilpesh/kgk-ui-library";
import { CommonStoreProvider } from "@magneto-it-solutions/kgk-common-library";
import StyledComponentsRegistry from "../styled-registery/registry";
import ParentCommonLayout from ".";


export default function MainLayout({
    children,
    theme
}: Readonly<{
    children: React.ReactNode;
    theme: LibType
}>) {

    return (
        <StyledComponentsRegistry>
        <CommonStoreProvider
            apiConfig={{
                baseUrl: process.env.NEXT_PUBLIC_API_BASE,
                xApiKey: process.env.NEXT_PUBLIC_X_API_TOKEN,
                strapiBaseUrl: process.env.NEXT_PUBLIC_CMS_BASE_URL,
                strapiCmsToken: process.env.NEXT_PUBLIC_CMS_API_TOKEN,
                assetUrl: process.env.NEXT_PUBLIC_ASSETS_URL,
                placeHolderImage: process.env.NEXT_PUBLIC_PLACEHOLDER_IMAGE
            }}
            theme={theme}
            >
            <ThemeProvider theme={theme}>
                <ParentCommonLayout>
                    {children}
                </ParentCommonLayout>
            </ThemeProvider>
        </CommonStoreProvider>
    </StyledComponentsRegistry>
    );
}
