import { GlobalStyle } from "@dilpesh/kgk-ui-library";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Offer<PERSON>eader, TopMsg, useGetAppData } from "@magneto-it-solutions/kgk-common-library";
import 'react-toastify/dist/ReactToastify.css';
import "swiper/css";
import "swiper/css/navigation";

import FrontendStyle from "../styles/style";
import { useTheme } from "../theme-provider/module-theme-provider";


const ParentCommonLayout = (props: {
    children: React.ReactNode
}) => {

    const { isAuthenticated } = useGetAppData();
    const theme = useTheme()
    
    return (
        <>
            <OfferHeader />

            <GlobalStyle />
            <FrontendStyle />
            <Header theme={theme ?? "KGK"}/>
            {props.children}
            {/* {!isAuthenticated && <Footer />} */}
            <TopMsg />
            <CopyRight />
        </>
    )
};

export default ParentCommonLayout;