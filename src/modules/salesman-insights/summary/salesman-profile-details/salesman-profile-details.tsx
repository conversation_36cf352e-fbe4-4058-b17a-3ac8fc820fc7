import React from 'react'
import { Col, Row } from "antd";
import { customerInfo, salesmanProfile, userMemo } from '../utils/constant';
import SalesmanProfile from '../salesman-profile/salesman-profile';
import Customers from '../customers/customers';
import CustomerMemo from '../customer-memo/customer-memo';
import { SalesmanProfileDetailsStyle } from './salesman-profile-details-style';

const SalesmanProfileDetails = () => {
    return (
        <SalesmanProfileDetailsStyle>
            <div className="customer_profile_wpr">
                <Row gutter={{ md: 24, lg: 32, xl: 52, xxl: 72 }}>
                    <Col md={12} xl={12} xxl={8}>
                        <SalesmanProfile
                            data={salesmanProfile}
                        />
                    </Col>
                    <Col md={12} xl={12} xxl={8}>
                        <Customers
                            data={customerInfo}
                        />
                    </Col>
                    <Col md={24} xl={12} xxl={8}>
                        <CustomerMemo
                            data={userMemo}
                        />
                    </Col>
                </Row>
            </div>
        </SalesmanProfileDetailsStyle>
    )
}

export default SalesmanProfileDetails
