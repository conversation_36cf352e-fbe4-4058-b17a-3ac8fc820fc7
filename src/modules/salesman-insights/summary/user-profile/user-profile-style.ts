import { Media, b2xs, bs } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const UserProfilestyle = styled.div`
  ${(props) =>
    css`
      .customer_info_wpr {
        padding: 40px;
        background-color: ${(props) => props?.theme?.colors?.brand_low};
        display: flex;
        align-items: flex-start;
        justify-content: flex-start;
        margin-bottom: 64px;
        @media ${Media.desktop} {
          margin-bottom: 52px;
        }
        @media ${Media.tablet} {
          margin-bottom: 48px;
          padding: 32px;
        }
        .cus_img_wpr {
          width: 160px;
          height: 160px;
          border-radius: 100%;
          overflow: hidden;
          @media ${Media.tablet} {
            width: 120px;
            height: 120px;
          }
        }
        .cus_content_wpr {
          width: calc(100% - 160px);
          padding-left: 40px;
          padding-top: 1px;
          padding-bottom: 1px;
          overflow: hidden;
          @media ${Media.tablet} {
            width: calc(100% - 120px);
            padding-left: 32px;
          }
          .top_cus_wpr {
            .this_row {
              display: flex;
              margin-left: -48px;
              margin-right: -48px;
              margin-bottom: -24px;
              flex-wrap: wrap;
              @media ${Media.below_1799} {
                margin-left: -32px;
                margin-right: -32px;
              }
              @media ${Media.below_1599} {
                margin-left: -24px;
                margin-right: -24px;
              }
              @media ${Media.tablet} {
                margin-left: -12px;
                margin-right: -12px;
              }
              & > * {
                padding-left: 48px;
                padding-right: 48px;
                margin-bottom: 24px;
                @media ${Media.below_1799} {
                  padding-left: 32px;
                  padding-right: 32px;
                }
                @media ${Media.below_1599} {
                  padding-left: 24px;
                  padding-right: 24px;
                }
                @media ${Media.tablet} {
                  padding-left: 12px;
                  padding-right: 12px;
                }
                @media ${Media.below_1399} {
                  &:nth-child(1),
                  &:nth-child(2),
                  &:nth-child(3) {
                    width: 33.33%;
                  }
                  &:nth-child(4) {
                    width: 100%;
                  }
                }
                /* @media ${Media.desktop}{
                                    &:nth-child(1), &:nth-child(2), &:nth-child(3){
                                        width: 33.33%;
                                    }
                                    &:nth-child(4){
                                        width: 66.66%;
                                    }
                                } */
                @media ${Media.tablet} {
                  &:nth-child(1),
                  &:nth-child(2),
                  &:nth-child(3) {
                    width: 50%;
                  }
                }
              }
            }
            .inner_info {
              position: relative;
              svg,
              img {
                width: 16px;
                height: 16px;
                position: absolute;
                top: 2px;
                left: 0;
                path {
                  stroke: ${props.theme.colors.brand_high};
                }
              }
              .icon_details {
                padding-left: 24px;
                p {
                  ${b2xs(props?.theme?.text?.high, 400, false)};
                  margin-bottom: 8px;
                }
                span {
                  display: block;
                  ${bs(props?.theme?.text?.high, 500)};
                }
              }
            }
          }
          .bottom_cus_wpr {
            margin-top: 24px;
            border-top: 1px solid ${(props) => props?.theme?.line?.light};
            padding-top: 24px;
            .this_row {
              display: flex;
              margin-left: -48px;
              margin-right: -48px;
              margin-bottom: -24px;
              flex-wrap: wrap;
              @media ${Media.below_1799} {
                margin-left: -32px;
                margin-right: -32px;
              }
              @media ${Media.below_1599} {
                margin-left: -24px;
                margin-right: -24px;
              }
              @media ${Media.tablet} {
                margin-left: -12px;
                margin-right: -12px;
              }
              & > * {
                padding-left: 48px;
                padding-right: 48px;
                margin-bottom: 24px;
                max-width: 50%;
                @media ${Media.below_1799} {
                  padding-left: 32px;
                  padding-right: 32px;
                }
                @media ${Media.below_1599} {
                  padding-left: 24px;
                  padding-right: 24px;
                }
                @media ${Media.tablet} {
                  padding-left: 12px;
                  padding-right: 12px;
                }
              }
            }
            .inner_info {
              position: relative;
              svg,
              img {
                width: 16px;
                height: 16px;
                position: absolute;
                top: 2px;
                left: 0;
                path {
                  stroke: ${props.theme.colors.brand_high};
                }
              }
              .icon_details {
                padding-left: 24px;
                p {
                  ${b2xs(props?.theme?.text?.high, 400, false)};
                  margin-bottom: 8px;
                }
                span {
                  display: block;
                  ${bs(props?.theme?.text?.high, 500)};
                  margin-bottom: 4px;
                  &:last-child {
                    margin-bottom: 0;
                  }
                }
              }
            }
          }
        }
      }
    `}
`;
