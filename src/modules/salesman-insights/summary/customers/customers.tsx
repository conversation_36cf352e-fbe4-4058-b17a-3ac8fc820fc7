import React from 'react'
import { CustomerStyle } from './customers-style'
import { UserImg } from '@/assets/icons/collection/UserImg'

const Customers = (props) => {
    return (
        <CustomerStyle>
            <div className="top_profile_table">
                <table>
                    <thead>
                        <tr>
                            <th>Top 5 customers</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        {
                            props.data.map((item) => (
                                <tr>
                                    <td>
                                        <div className="icon_name_24_8_bes" >
                                            <UserImg />
                                            <p>{item?.name}</p>
                                        </div>
                                    </td>
                                    <td>{item?.amount}</td>
                                </tr>
                            ))
                        }

                    </tbody>
                </table>
            </div>
        </CustomerStyle>
    )
}

export default Customers
