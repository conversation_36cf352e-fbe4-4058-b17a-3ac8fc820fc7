import React from 'react'
import { items } from '../../orders/utils/constant';
import { AddressContactStyle } from './address-contact-style';



const AddressContact = (props) => {
    return (
        <AddressContactStyle>
            <div className="address_contact_wpr">
                <h2>Address & contact</h2>
                <div className="account_address_contact_inner">
                    <div className="account_address_contact_table">
                        <table>
                            <thead>
                                <tr>
                                    <th>Address type</th>
                                    <th>Address</th>
                                    <th>Phone</th>
                                    <th>Mobile</th>
                                    <th>Email</th>
                                </tr>
                            </thead>
                            <tbody>
                                {
                                    props.BillAddress.map((item) => (
                                        <tr>
                                            <td>{item?.AddressType}</td>
                                            <td>{item?.Address}</td>
                                            <td>{item?.Phone}</td>
                                            <td>{item?.Mobile}</td>
                                            <td>{item?.Email}</td>
                                        </tr>
                                    ))
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </AddressContactStyle>

    )
}

export default AddressContact
