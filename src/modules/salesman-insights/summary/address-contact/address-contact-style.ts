import { bes, bs, h4 } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const AddressContactStyle = styled.div`
  ${(props) =>
    css`
      .address_contact_wpr {
        & > h2 {
          ${h4(props?.theme?.text?.high, 400)};
          margin-bottom: 24px;
        }
        .account_address_contact_inner {
          .account_address_contact_table {
            overflow: auto;
            table {
              width: 100%;
              border-collapse: separate;
              border-spacing: 0;
              padding: 0;
              thead {
                tr {
                  th {
                    ${bs(props?.theme?.text?.high, 500)};
                    padding-top: 10px;
                    padding-bottom: 10px;
                    padding-left: 12px;
                    padding-right: 12px;
                    background-color: ${(props) =>
                      props?.theme?.colors?.brand_low};
                    text-align: left;
                    white-space: nowrap;
                    &:last-child {
                      padding-right: 16px;
                    }
                    &:first-child {
                      padding-left: 16px;
                    }
                  }
                }
              }
              tbody {
                tr {
                  &:first-child {
                    td {
                      padding-top: 14px;
                    }
                  }
                  &:last-child {
                    td {
                      padding-bottom: 0;
                    }
                  }
                  td {
                    ${bes(props?.theme?.text?.mid, 400)};
                    padding-top: 8px;
                    padding-bottom: 8px;
                    padding-left: 12px;
                    padding-right: 12px;
                    text-align: left;
                    &:nth-child(2) {
                      min-width: 200px;
                    }
                    &:nth-child(3),
                    &:nth-child(4) {
                      white-space: nowrap;
                    }
                    &:last-child {
                      padding-right: 16px;
                    }
                    &:first-child {
                      padding-left: 16px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    `}
`;
