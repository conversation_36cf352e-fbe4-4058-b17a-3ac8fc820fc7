"use client"

import { Bill<PERSON>ddress, Memo, Order, SalesType } from "./utils/constant";
import UserProfile from "./user-profile/user-profile";
import SalesmanProfileDetails from "./salesman-profile-details/salesman-profile-details";
import AccountSummary from "./account-summary/account-summary";
import AddressContact from "./address-contact/address-contact";

const SalesmanInsightsSummary = () => {
    return (
        <div className="container first_view_content">
            <UserProfile />
            <SalesmanProfileDetails />
            <AccountSummary
                Order={Order}
                SalesType={SalesType}
                Memo={Memo}
            />
            <AddressContact
                BillAddress={BillAddress}
            />
        </div>

    )
}

export default SalesmanInsightsSummary
