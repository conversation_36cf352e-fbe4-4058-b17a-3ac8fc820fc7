import { bes, bs } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const CustomerMemoStyle = styled.div`
  ${(props) =>
    css`
      .credit_memo_wpr{
                    table{
                        width: 100%;
                        border-collapse: separate;
                        border-spacing: 0;
                        padding: 0;
                        thead{
                            tr{
                                th{
                                    ${bs(props?.theme?.text?.high, 500)};
                                    padding-top: 10px;
                                    padding-bottom: 10px;
                                    padding-left: 12px;
                                    padding-right: 12px;
                                    background-color:${(props) =>
                                      props?.theme?.colors?.brand_low};
                                    text-align:right;
                                    &:last-child{
                                        padding-right: 16px;
                                    }
                                    &:first-child{
                                        text-align:left;
                                        padding-left: 16px;
                                    }
                                }
                            }
                        }
                        tbody{
                            tr{
                                &:first-child{
                                td{
                                    padding-top: 14px;
                                }
                                }
                                td{
                                    ${bes(props?.theme?.text?.high, 500)};
                                    padding-top: 8px;
                                    padding-bottom: 8px;
                                    padding-left: 12px;
                                    padding-right: 12px;
                                    text-align:right;
                                    &:first-child{
                                        text-align:left;
                                        padding-left: 16px;
                                    }
                                }
                            }
                        } 
                        .icon_name_24_8_bes{
                            display: inline-flex;
                            align-items: center;
                            img, svg{
                                width: 24px;
                                height: 24px;
                            }
                            p{
                                padding-left: 8px;
                                width: calc(100% - 24px);
                                ${bes(props?.theme?.text?.high, 400)};
                            }
                        }
                    }
                }
            }
    `}
`;
