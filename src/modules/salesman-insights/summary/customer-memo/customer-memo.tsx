import React from 'react'
import { CustomerMemoStyle } from './customer-memo-style'
import { UserImg } from '@/assets/icons/collection/UserImg'

const CustomerMemo = (props) => {
    return (
        <CustomerMemoStyle>
            <div className="credit_memo_wpr">
                <table>
                    <thead>
                        <tr>
                            <th>Target data</th>
                            <th>Location</th>
                            <th>Dec</th>
                        </tr>
                    </thead>
                    <tbody>

                        {
                            props.data.map((item) => (
                                <tr>
                                    <td>
                                        <div className="icon_name_24_8_bes">
                                            <UserImg />
                                            <p>{item?.username}</p>
                                        </div>
                                    </td>
                                    <td>{item?.location}</td>
                                    <td>{item?.dec}</td>
                                </tr>
                            ))
                        }
                    </tbody>
                </table>
            </div>
        </CustomerMemoStyle>
    )
}

export default CustomerMemo
