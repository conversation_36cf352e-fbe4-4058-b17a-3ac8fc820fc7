import React from 'react'
import { SalesmanProfilestyle } from './salesman-profile-style'
import { JackImg } from '@/assets/icons/collection/JackImg'

const SalesmanProfile = (props) => {
    return (
        <SalesmanProfilestyle>
        <div className="customer_profile_table">
            <table>
                <thead>
                    <tr>
                        <th>Profile</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    {
                        props.data.map((item) => (
                            <tr>
                                <td>{item?.lable}</td>
                                <td>{item?.value}</td>
                            </tr>
                        ))
                    }
                    <tr>
                        <td>Reporting</td>
                        <td>
                            <div className="icon_name_24_8_bes">
                                <JackImg />
                                <p>John <PERSON></p>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        </SalesmanProfilestyle>
    )
}

export default SalesmanProfile
