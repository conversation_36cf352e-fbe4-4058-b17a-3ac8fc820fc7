import { IconButton, Tab } from '@mui/material';
import { ShapeTabStyle } from './shape-tab-style';

const ShapeTab = (props: any) => {
    return (
        <ShapeTabStyle
            value={props.value}
            onChange={props.onChange}
            variant="scrollable"
            scrollButtons="auto"
            allowScrollButtonsMobile={true}
            aria-label="scrollable auto tabs"
            ScrollButtonComponent={(props) => {
                if (
                    props.direction === "left" &&
                    !props.disabled
                ) {
                    return (
                        <IconButton {...props} className="left_icon">
                            <svg viewBox="0 0 24 24" fill="none">
                                <path d="M15 18L9 12L15 6" stroke="#083458" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                            </svg>
                        </IconButton>
                    );
                } else if (
                    props.direction === "right" &&
                    !props.disabled
                ) {
                    return (
                        <IconButton {...props} className="right_icon">
                            <svg viewBox="0 0 24 24" fill="none">
                                <path d="M9 18L15 12L9 6" stroke="#083458" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                            </svg>
                        </IconButton>
                    );
                } else {
                    return null;
                }
            }}
        >
            {props.data?.map((items: any, index: number) => (
                <Tab
                    key={index}
                    label={<div className='shape_select_box'>{items.icon}<p>{items.name}</p></div>}
                />
            ))}
        </ShapeTabStyle>
    )
}

export default ShapeTab
