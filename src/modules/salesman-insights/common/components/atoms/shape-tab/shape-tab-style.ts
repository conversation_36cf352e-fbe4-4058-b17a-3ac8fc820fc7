
import { Media, bes } from "@dilpesh/kgk-ui-library";
import { Tabs } from "@mui/material";
import styled, { css } from "styled-components";

export const ShapeTabStyle = styled(Tabs)`
    ${(props) =>
        css`
            &.MuiTabs-root {
                min-height: unset;
                position: relative;
                overflow: visible;
                display: flex;
                justify-content: center;
                align-items: center;
                .MuiTabs-scroller{
                    flex: 1;
                }
                .left_icon, .right_icon{
                    height: 24px;
                    width: 24px;
                    padding: 0;
                    border-radius: 0;
                    background-color: transparent;
                    svg{
                        height: inherit;
                        width: inherit;
                        path{
                        stroke: ${(props.theme.colors.brand_high)};
                        }
                    }
                    .MuiTouchRipple-root{
                        display: none;
                    }
                }
                .left_icon{
                    margin-right: 8px;
                }
                .right_icon{
                    margin-left: 8px;
                }
            }
            .MuiTab-textColorPrimary {
                padding: 0;
                flex-direction: column;
                min-height: unset;
                max-width: unset;
                min-width: unset;
                margin-right: 24px;
                .shape_select_box{
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    flex-direction: column;
                    svg{
                        height: 40px;
                        width: 40px;
                        border-radius: 4px;
                        path{
                            stroke: ${(props.theme.text.mid)};
                        }
                    }
                    img{
                        height: 40px;
                        width: 40px;
                    }
                    p{
                        margin-top: 8px;
                        white-space: nowrap;
                        text-transform: capitalize;
                        ${bes(props?.theme?.text?.mid, 400)};
                    }
                }
                &.Mui-selected {
                    .shape_select_box{
                        svg{
                            path{
                                stroke: ${(props.theme.colors.brand_high)};
                            }
                        }
                        p{
                            color: ${(props.theme.colors.brand_high)};
                        }
                    }
                }
                .MuiTouchRipple-root {
                    display: none;
                }
                &:last-child {
                    margin-right: 0;
                }
            }
            .MuiTabScrollButton-horizontal {
                width: 24px;
                height: 24px;
                @media ${Media.tablet}{
                    display: none;
                }
                svg{
                    height: 24px;
                    width: 24px;
                }
            }
            .MuiTabs-indicator {
                display: none;
            }
        `
    }
`