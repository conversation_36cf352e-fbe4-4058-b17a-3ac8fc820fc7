"use client"
import { ActionDropdown, DataTable, DataTableExpand, TableFooter, TableHeader, TableStatus } from "@dilpesh/kgk-ui-library";
import { useRef } from "react";

import { ActionIcon, FilterLineIcon } from "@/assets/icons";
import { PageLimitOptions, useGetOrderData, useGetTranslation, usePagination, usePayloadFilter } from "@magneto-it-solutions/kgk-common-library";
import { actionitemList, items, stoneCardData, stoneCardHead, stoneInnerData, stoneInnerTableHead } from "./utils/constant";
import { SalesmanInsightsOrdersstyle } from "./utils/style";


const SalesmanInsightsOrders = ({ params: { id } }) => {
  const { data, isLoading } = useGetOrderData(id);
  const { page, limit, changePage, changeLimit, changeSorting, sorting } = usePagination('order-data');
  const { changeSearch, selectedFilter, handleFilterChange, clearFilters, applyFilters } = usePayloadFilter('order-data');
  const { translation } = useGetTranslation();

  const coloumnDefStoneCard: Object = {
    orderNumber: {
      renderChildren: (item: any) => {
        return (
          <div className='bes_500_wpr'>
            <p>{item.orderNumber}</p>
          </div>
        );
      },
    },
    salesman: {
      renderChildren: (item: any) => {
        return (
          <div className='icon_name_24_bes'>
            <div className="img_wpr">
              <img src={item.salesmanIcon} alt="" />
            </div>
            <p>{item.salesman}</p>
          </div>
        );
      },
    },
    type: {
      renderChildren: (item: any) => {
        return (
          <TableStatus className="active">{item.type}</TableStatus>
        );
      },
    },
    status: {
      renderChildren: (item: any) => {
        return (
          <TableStatus className="inprogress">{item.status}</TableStatus>
        );
      },
    },
    action: {
      renderChildren: (item: any) => {
        return (
          <div className='action_wpr'>
            <ActionDropdown items={items} actionList={actionitemList} actionIcon={<ActionIcon />} className="mx-auto" />
          </div>
        );
      },
    },
  };


  const ref: any = useRef();


  const collapsibleData = () => {
    return (
      <td colSpan={stoneCardHead.length + 2} className='stone_card_expanded_td'>
        <DataTable
          isMultiselect={false}
          column={stoneInnerTableHead}
          coloumnDef={coloumnDefStoneInner}
          data={stoneInnerData}
        />
      </td>
    )
  }


  const coloumnDefStoneInner: Object = {
    item: {
      renderChildren: (item: any) => {
        return (
          <div className='icon_name_24_bes_56'>
            <div className="img_wpr">
              <img src={item.itemIcon} alt="" />
            </div>
            <p>{item.item}</p>
          </div>
        );
      },
    },
  };

  return (
    <SalesmanInsightsOrdersstyle>
      <div className="container first_view_content">
        <div className="table_main_wpr">
          <TableHeader
            searchPlaceholder="Search orders"
            borderBtnText="Filter"
            borderBtnIcon={<FilterLineIcon />}
            isSearch={true}
          />
          <DataTableExpand
            isMultiselect={false}
            column={stoneCardHead}
            coloumnDef={coloumnDefStoneCard}
            data={stoneCardData}
            collapsibleData={collapsibleData}
            expandIconPosition=""
            bodyClass="body_pt_12"
            ref={ref}
            fixedPosition="right"
          />
          <TableFooter
            skeleton={isLoading}
            currentPage={page}
            currentPageSize={limit}
            sortOptions={PageLimitOptions}
            recordsTotal={data?.data?.filteredRecords}
            setCurrentPage={changePage}
            setCurrentPageSize={changeLimit}
            showingText={translation.Showing}
            showLabel={translation.Show}
            OfText={translation.Of}
          />
        </div>
      </div>
    </SalesmanInsightsOrdersstyle>
  )
}

export default SalesmanInsightsOrders
