import { Media, b2xs, bes } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const SalesmanInsightsOrdersstyle = styled.div`
    ${(props) =>
        css`
            .table_main_wpr{
                .bes_500_wpr{
                    p{
                        display: block;
                        ${bes(props?.theme?.text?.high, 500)}
                    }
                }
                .icon_name_24_bes{
                    display: flex;
                    justify-content: flex-start;
                    align-items: center;
                    svg, .img_wpr{
                        height: 24px;
                        width: 24px;
                    }
                    p{
                        padding-left: 8px;
                        ${bes(props?.theme?.text?.high, 400)}
                        width: calc(100% - 24px);
                    }
                }
                .icon_name_24_bes_56{
                    display: flex;
                    justify-content: flex-start;
                    align-items: center;
                    svg, .img_wpr{
                        height: 56px;
                        width: 56px;
                        @media ${Media.tablet}{
                            height: 48px;
                            width: 48px;
                        }
                    }
                    p{
                        padding-left: 16px;
                        ${bes(props?.theme?.text?.high, 400)}
                        width: calc(100% - 56px);
                        @media ${Media.tablet}{
                            width: calc(100% - 48px);
                            padding-left: 12px;
                        }
                    }
                }
                .stone_card_expanded_td{
                    padding-left: 24px;
                    padding-right: 24px;
                    table{
                        thead{
                            tr{
                                th{
                                    padding-top: 4px;
                                    padding-bottom: 12px;
                                    border-bottom: 1px solid ${(props) => props?.theme?.line?.light};
                                    p{
                                        ${b2xs(props.theme.text.high, 500)}
                                    }
                                    &:first-child{
                                        padding-left: 0;
                                    }
                                    &:last-child{
                                        padding-right: 0;
                                        text-align: right;
                                    }
                                }
                            }
                        }
                        tbody{
                            tr{
                                td{
                                    background-color: transparent !important;
                                    border-bottom:0 !important;
                                    padding-top: 8px;
                                    padding-bottom: 8px;
                                    &:first-child{
                                        padding-left: 0;
                                    }
                                    &:last-child{
                                        padding-right: 0;
                                        text-align: right;
                                    }
                                }
                                &:first-child{
                                    td{
                                        padding-top: 16px;
                                    }
                                }
                                &:last-child{
                                    td{
                                        padding-bottom: 4px;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            
        `
    }

`