import { ViewIcon } from "@/assets/icons";
import { Skeleton } from "@dilpesh/kgk-ui-library";
import { getSessionItem, SESSION_KEYS } from "@magneto-it-solutions/kgk-common-library";
import { format } from "date-fns";

export const coloumnDefAssetsListing = (
  isLoading: boolean,
  onAddProposalOrder: (item: any) => void
): Record<string, any> => ({
  proposal_number: {
    renderChildren: (item: any) =>
      isLoading ? <Skeleton width="100px" /> : <p>{item.proposal_number ?? '-'}</p>,
  },
  customer_name: {
    renderChildren: (item: any) =>
      isLoading ? <Skeleton width="100px" /> : <p>{item.customer_name ?? '-'}</p>,
  },
  total_quantity: {
    renderChildren: (item: any) =>
      isLoading ? <Skeleton width="100px" /> : <p>{item.total_quantity ?? '-'}</p>,
  },
  total_amount: {
    renderChildren: (item: any) =>
      isLoading ? (
        <Skeleton width="100px" />
      ) : (
        <p>
          {item.total_amount
            ? getSessionItem(SESSION_KEYS.CURRENCY) + item.total_amount
            : '-'}
        </p>
      ),
  },
  shipping_date: {
    renderChildren: (item: any) =>
      isLoading ? (
        <Skeleton width="100px" />
      ) : item.shipping_date ? (
        <p>
          {format(
            new Date(item.shipping_date),
            getSessionItem(SESSION_KEYS.DATE_FORMAT) || 'dd MMM yyyy, h:mm a'
          )}
        </p>
      ) : (
        '-'
      ),
  },
  status: {
    renderChildren: (item: any) =>
      isLoading ? <Skeleton width="100px" /> : <p>{item.status ?? '-'}</p>,
  },
  so_number: {
    renderChildren: (item: any) =>
      isLoading ? <Skeleton width="100px" /> : <p>{item.so_number ?? '-'}</p>,
  },
  customer_po_number: {
    renderChildren: (item: any) =>
      isLoading ? <Skeleton width="100px" /> : <p>{item.customer_po_number ?? '-'}</p>,
  },
  created_on: {
    renderChildren: (item: any) =>
      isLoading ? (
        <Skeleton width="100px" />
      ) : item.created_on ? (
        <p>
          {format(
            new Date(item.created_on),
            getSessionItem(SESSION_KEYS.DATE_FORMAT) || 'dd MMM yyyy, h:mm a'
          )}
        </p>
      ) : (
        '-'
      ),
  },
  created_by: {
    renderChildren: (item: any) =>
      isLoading ? <Skeleton width="100px" /> : <p>{item.created_by ?? '-'}</p>,
  },
  view: {
    renderChildren: (item: any) =>
      isLoading ? (
        <Skeleton width="24px" />
      ) : (
        <div className="action_wpr">
          <a
            href="#"
            onClick={(e) => {
              e.preventDefault();
              onAddProposalOrder(item);
            }}
          >
            <ViewIcon />
          </a>
        </div>
      ),
  },
});