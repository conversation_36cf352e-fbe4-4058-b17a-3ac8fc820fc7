"use client";
import { FilterLineIcon } from "@/assets/icons";
import { DataTable, FilterDropdown, TableFooter, TableHeader } from "@dilpesh/kgk-ui-library";
import { PageLimitOptions, SalesmanInsightType, useGetProposalData, useGetSalesmanFilterListQuery, useGetTranslation, usePagination, usePayloadFilter } from "@magneto-it-solutions/kgk-common-library";
import { useState } from "react";
import { default as CustomerInsightsProposalOrderModel } from "../modal/proposal-order-model/proposal-order-modal";
import { assetsListingHead } from "./utils/constant";
import { SalesmanInsightsProposalstyle } from "./utils/style";
import { coloumnDefAssetsListing } from "./utils/utility";

const SalesmanInsightsProposal = ({ params: { id } }) => {
  const { data, isLoading } = useGetProposalData(id);
  const { page, limit, changePage, changeLimit, changeSorting, sorting } = usePagination('proposal-data');
  const { changeSearch, selectedFilter, handleFilterChange, clearFilters, applyFilters } = usePayloadFilter('proposal-data');
  const { data: FilteredList } = useGetSalesmanFilterListQuery(SalesmanInsightType.PROPOSAL);
  const [isFilterView, setIsFilterView] = useState<boolean>(false);
  const { translation } = useGetTranslation();
  const [sipoModalOpen, setSipoModalOpen] = useState(false);
  const [selectedProposalId, setSelectedProposalId] = useState(null);

  const onAddProposalOrder = async (proposal: any) => {
    setSelectedProposalId(proposal._id);
    setSipoModalOpen(true)
  };

  return (
    <>
      <SalesmanInsightsProposalstyle>
        <div className="container first_view_content">
          <div className="table_main_wpr">
            <TableHeader
              searchPlaceholder={translation?.search_proposal}
              borderBtnText={translation?.filter}
              borderBtnIcon={<FilterLineIcon />}
              isSearch={true}
              borderBtnClick={() => setIsFilterView(!isFilterView)}
              onSearchChange={(e: any) => changeSearch(e.target.value)}
            />
            <FilterDropdown
              open={isFilterView}
              data={FilteredList?.data?.filters}
              onChangeFilter={handleFilterChange}
              selectedFilter={selectedFilter}
              onApplyFilter={applyFilters}
              onClearFilter={clearFilters}
              fetchOptions={{}}
              filterText={translation.Filters}
              clearAllBtnText={translation.ClearAll}
              applyBtntext={translation.Apply}
            />
            <DataTable
              isMultiselect={false}
              skeleton={isLoading}
              column={assetsListingHead}
              coloumnDef={coloumnDefAssetsListing(isLoading, onAddProposalOrder)}
              data={data?.data?.data}
              allowedSortingKeys={FilteredList?.data?.sort_fields}
              handleSortingChange={changeSorting}
              sorting={sorting}
            />
            <TableFooter
              skeleton={isLoading}
              currentPage={page}
              currentPageSize={limit}
              sortOptions={PageLimitOptions}
              recordsTotal={data?.data?.filteredRecords}
              setCurrentPage={changePage}
              setCurrentPageSize={changeLimit}
              showingText={translation.Showing}
              showLabel={translation.Show}
              OfText={translation.Of}
            />
          </div>
        </div>
      </SalesmanInsightsProposalstyle>
      {sipoModalOpen && <CustomerInsightsProposalOrderModel
        onHide={() => { setSipoModalOpen(false), setSelectedProposalId(null) }}
        show={sipoModalOpen}
        approveBtnEvent={() => { }}
        rejectBtnEvent={() => { setSipoModalOpen(false), setSelectedProposalId(null) }}
        id={selectedProposalId}
      />}
    </>
  );
};

export default SalesmanInsightsProposal;
