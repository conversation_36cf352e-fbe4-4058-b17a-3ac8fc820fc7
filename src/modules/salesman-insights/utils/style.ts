import { Media, bes, h3 } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const SalesmanInsightsStyle = styled.div`
  ${(props) =>
    css`
      padding-top: 32px;
      padding-bottom: 64px;
      @media ${Media.ultra_mobile} {
        padding-top: 24px;
        padding-bottom: 40px;
      }
      @media ${Media.mobile} {
        padding-top: 16px;
        padding-bottom: 32px;
      }
      .bread_crumbs {
        margin-bottom: 24px;
        @media ${Media.mobile} {
          margin-bottom: 16px;
        }
      }
      h1 {
        ${h3(props?.theme?.text?.high, 400)};
        margin-bottom: 40px;
        @media ${Media.ultra_mobile} {
          margin-bottom: 32px;
        }
        @media ${Media.mobile} {
          margin-bottom: 24px;
        }
      }
      .table_main_wpr {
        .data_table {
          margin-top: 24px;
        }
        .icon_name_24_8_bes {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          img,
          svg {
            width: 24px;
            height: 24px;
          }
          p {
            padding-left: 8px;
            width: calc(100% - 24px);
            ${bes(props?.theme?.text?.high, 400)};
            cursor: pointer;
          }
        }
      }
    `}
`;
