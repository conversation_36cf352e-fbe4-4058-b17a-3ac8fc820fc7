export const breadCrumbsValue1 = [
    {
        link: 'Home',
        title: 'Home'
    },
    {
        link: 'CRM-portal',
        title: 'CRM portal'
    },
    {
        title: 'Salesman insights'
    },
];

export const assetsListingHead = [
  { label: 'Name', key: 'name' },
  { label: 'Customer Code', key: 'customer_code' },
  { label: 'Salesman', key: 'head_salesman' },
  { label: 'Status', key: 'status' },
  { label: 'Target', key: 'target' },
  { label: 'FG Sales', key: 'fg_sales' },
  { label: 'RM Sales', key: 'rm_sales' },
  { label: 'Total', key: 'total' },
  { label: 'Open Order', key: 'open_order' }
];


export const assetsListingData = [
    {
        name: 'Albert S. Smyth Co., Inc.-Annapolis',
        nameIcon: '/crm/assets/img/user.png',
        customerType: 'Customer group',
        customerCode: '01EC2487',
        salesman: '<PERSON>',
        businessType: 'Diamond',
        status: 'Active',
        target: '77,815.00',
        fgSales: '22,322.00',
        rmSales: '15,365.00',
        total: '22,322.00',
        openOrder: '29,370.00',
        dropId: '1'
    },
    {
        name: '<PERSON> S. Smyth Co., Inc.-Annapolis',
        nameIcon: '/crm/assets/img/user.png',
        customerType: 'Customer group',
        customerCode: '01EC2487',
        salesman: 'Angela Guenther',
        businessType: 'Diamond',
        status: 'Active',
        target: '77,815.00',
        fgSales: '22,322.00',
        rmSales: '15,365.00',
        total: '22,322.00',
        openOrder: '29,370.00',
        dropId: '1'
    },
    {
        name: 'Albert S. Smyth Co., Inc.-Annapolis',
        nameIcon: '/crm/assets/img/user.png',
        customerType: 'Customer group',
        customerCode: '01EC2487',
        salesman: 'Angela Guenther',
        businessType: 'Diamond',
        status: 'Active',
        target: '77,815.00',
        fgSales: '22,322.00',
        rmSales: '15,365.00',
        total: '22,322.00',
        openOrder: '29,370.00',
        dropId: '1'
    },
    {
        name: 'Albert S. Smyth Co., Inc.-Annapolis',
        nameIcon: '/crm/assets/img/user.png',
        customerType: 'Customer group',
        customerCode: '01EC2487',
        salesman: 'Angela Guenther',
        businessType: 'Diamond',
        status: 'Active',
        target: '77,815.00',
        fgSales: '22,322.00',
        rmSales: '15,365.00',
        total: '22,322.00',
        openOrder: '29,370.00',
        dropId: '1'
    },
    {
        name: 'Albert S. Smyth Co., Inc.-Annapolis',
        nameIcon: '/crm/assets/img/user.png',
        customerType: 'Customer group',
        customerCode: '01EC2487',
        salesman: 'Angela Guenther',
        businessType: 'Diamond',
        status: 'Active',
        target: '77,815.00',
        fgSales: '22,322.00',
        rmSales: '15,365.00',
        total: '22,322.00',
        openOrder: '29,370.00',
        dropId: '1'
    },
    {
        name: 'Albert S. Smyth Co., Inc.-Annapolis',
        nameIcon: '/crm/assets/img/user.png',
        customerType: 'Customer group',
        customerCode: '01EC2487',
        salesman: 'Angela Guenther',
        businessType: 'Diamond',
        status: 'Active',
        target: '77,815.00',
        fgSales: '22,322.00',
        rmSales: '15,365.00',
        total: '22,322.00',
        openOrder: '29,370.00',
        dropId: '1'
    },
];

export const mainActionList = [
    {
        key: 1,
        value: 'Compare',
        class: 'black'
    },
    {
        key: 4,
        value: 'Reserve',
        class: 'black'
    },
];

