interface ColumnDefProps {
  isLoading: boolean;
  onPrintInvoive: (id: string | number) => void;
}
import { CheckIcon, PrintIcon } from "@/assets/icons";
import { Skeleton } from "@dilpesh/kgk-ui-library";
import { getSessionItem, SESSION_KEYS } from "@magneto-it-solutions/kgk-common-library";
import { format } from "date-fns";
export const coloumnDefStoneInner: Record<string, any> = {
  ContractNo: {
    renderChildren: (item: any) => item?.ContractNo || '-',
  },
  StyleNo: {
    renderChildren: (item: any) => item?.StyleNo || '-',
  },
  Description: {
    renderChildren: (item: any) => item?.Description || '-',
  },
  JID: {
    renderChildren: (item: any) => item?.JID || '-',
  },
  Qty: {
    renderChildren: (item: any) => item?.Qty ?? '-',
  },
  Rate: {
    renderChildren: (item: any) => item?.Rate ?? '-',
  },
  Amount: {
    renderChildren: (item: any) => item?.Amount ?? '-',
  },
};


export const getColoumnDefStoneCard = ({
  isLoading,
  onPrintInvoive,
}: ColumnDefProps) => ({
  invoice_number: {
    renderChildren: (item: any) =>
      isLoading ? <Skeleton width="100px" /> :
        item?.invoice_number ?
          <div className='icon_16_bes'>
            <p>{item.invoice_number}</p>
            <CheckIcon />
          </div> : '-',
  },
  reference_number: {
    renderChildren: (item: any) =>
      isLoading ? <Skeleton width="100px" /> : (item.reference_number ?? '-'),
  },
  date: {
    renderChildren: (item: any) =>
      isLoading ? <Skeleton width="100px" /> : item.date ? format(new Date(item.date), getSessionItem(SESSION_KEYS.DATE_FORMAT) || "dd MMM yyyy, h:mm a") : '-',
  },
  customer_code: {
    renderChildren: (item: any) =>
      isLoading ? <Skeleton width="100px" /> : (item.customer_code ?? '-'),
  },
  quantity: {
    renderChildren: (item: any) =>
      isLoading ? <Skeleton width="100px" /> : (item.quantity ?? '-'),
  },
  gross_amount: {
    renderChildren: (item: any) =>
      isLoading ? <Skeleton width="100px" /> : (item.gross_amount ?? '-'),
  },
  transaction_type: {
    renderChildren: (item: any) =>
      isLoading ? <Skeleton width="100px" /> : (item.transaction_type ?? '-'),
  },
  action: {
    renderChildren: (item: any) =>
      isLoading ? (
        <Skeleton width="24px" />
      ) : (
        <div className="action_wpr">
          <a href="javascript:void(0)"
            onClick={() => onPrintInvoive(item)}
          >
            <PrintIcon />
          </a>
        </div>
      ),
  },
});