import { CheckIcon } from "@/assets/icons";

// Function to generate table headers with translation
export const getStoneCardHead = (translation: any) => [
  {
    label: translation?.invoice_number || 'Invoice number',
    key: 'invoice_number'
  },
  {
    label: translation?.reference_number || 'Reference number',
    key: 'reference_number'
  },
  {
    label: translation?.date || 'Date',
    key: 'date'
  },
  {
    label: translation?.customer_id || 'Customer Id',
    key: 'customer_code'
  },
  {
    label: translation?.quantity || 'Quantity',
    key: 'quantity'
  },
  {
    label: translation?.gross_amount || 'Gross amount',
    key: 'gross_amount'
  },
  {
    label: translation?.transaction_type || 'Transaction type',
    key: 'transaction_type'
  },
  {
    label: translation?.action || 'Action',
    key: 'action',
    class: 'action'
  }
];

// Legacy export for backward compatibility (will be removed)
export const stoneCardHead = [
  {
    label: 'Invoice number',
    key: 'invoice_number'
  },
  {
    label: 'Reference number',
    key: 'reference_number'
  },
  {
    label: 'Date',
    key: 'date'
  },
  {
    label: 'Customer Id',
    key: 'customer_code'
  },
  {
    label: 'Quantity',
    key: 'quantity'
  },
  {
    label: 'Gross amount',
    key: 'gross_amount'
  },
  {
    label: 'Transaction type',
    key: 'transaction_type'
  },
  {
    label: 'Action',
    key: 'action',
    class: 'action'
  }
];


export const stoneCardData = [
  {
    invoiceImg: <CheckIcon />,
    invoiceNumber: 'DSC572271',
    referenceNumber: 'Vegas Proposal/AG',
    date: '23 Mar, 2023 - 10:00 PM',
    customerId: '01EC2487',
    quantity: '5',
    grossAmount: '22,322.00',
    transactionType: 'Memo',
    action: 'Print'
  },
  {
    invoiceImg: <CheckIcon />,
    invoiceNumber: 'DSC572271',
    referenceNumber: 'Vegas Proposal/AG',
    date: '23 Mar, 2023 - 10:00 PM',
    customerId: '01EC2487',
    quantity: '5',
    grossAmount: '22,322.00',
    transactionType: 'Memo',
    action: 'Print'
  },
  {
    invoiceImg: <CheckIcon />,
    invoiceNumber: 'DSC572271',
    referenceNumber: 'Vegas Proposal/AG',
    date: '23 Mar, 2023 - 10:00 PM',
    customerId: '01EC2487',
    quantity: '5',
    grossAmount: '22,322.00',
    transactionType: 'Memo',
    action: 'Print'
  },
  {
    invoiceImg: <CheckIcon />,
    invoiceNumber: 'DSC572271',
    referenceNumber: 'Vegas Proposal/AG',
    date: '23 Mar, 2023 - 10:00 PM',
    customerId: '01EC2487',
    quantity: '5',
    grossAmount: '22,322.00',
    transactionType: 'Memo',
    action: 'Print'
  },
  {
    invoiceImg: <CheckIcon />,
    invoiceNumber: 'DSC572271',
    referenceNumber: 'Vegas Proposal/AG',
    date: '23 Mar, 2023 - 10:00 PM',
    customerId: '01EC2487',
    quantity: '5',
    grossAmount: '22,322.00',
    transactionType: 'Memo',
    action: 'Print'
  },
  {
    invoiceImg: <CheckIcon />,
    invoiceNumber: 'DSC572271',
    referenceNumber: 'Vegas Proposal/AG',
    date: '23 Mar, 2023 - 10:00 PM',
    customerId: '01EC2487',
    quantity: '5',
    grossAmount: '22,322.00',
    transactionType: 'Memo',
    action: 'Print'
  },

];

// Function to generate inner table headers with translation
export const getStoneInnerTableHead = (translation: any) => [
  { label: translation?.contract_no || 'Contract No', key: 'ContractNo' },
  { label: translation?.style_no || 'Style no', key: 'StyleNo' },
  { label: translation?.description || 'Description', key: 'Description' },
  { label: translation?.jid || 'JID', key: 'JID' },
  { label: translation?.quantity || 'Quantity', key: 'Qty' },
  { label: translation?.rate_r || 'Rate R', key: 'Rate' },
  { label: translation?.amount_r || 'Amount R', key: 'Amount' },
];

// Legacy export for backward compatibility (will be removed)
export const stoneInnerTableHead = [
  { label: 'Contract No', key: 'ContractNo' },
  { label: 'Style no', key: 'StyleNo' },
  { label: 'Description', key: 'Description' },
  { label: 'JID', key: 'JID' },
  { label: 'Quantity', key: 'Qty' },
  { label: 'Rate R', key: 'Rate' },
  { label: 'Amount R', key: 'Amount' },
];


export const stoneInnerData = [
  {
    jobNumber: 'MF/098522',
    styleNo: 'DWBSP1Q-1.00-107524',
    description: ' ',
    jid: '23K0441L',
    quantity: '1',
    rateR: '$2,300.00',
    amountR: '$1,943.00',
  },

];
