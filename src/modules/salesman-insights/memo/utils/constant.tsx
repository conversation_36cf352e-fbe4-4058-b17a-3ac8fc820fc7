import { CheckIcon } from "@/assets/icons";

export const stoneCardHead = [
  {
    label: 'Invoice number',
    key: 'invoice_number'
  },
  {
    label: 'Reference number',
    key: 'reference_number'
  },
  {
    label: 'Date',
    key: 'date'
  },
  {
    label: 'Customer Id',
    key: 'customer_code'
  },
  {
    label: 'Quantity',
    key: 'quantity'
  },
  {
    label: 'Gross amount',
    key: 'gross_amount'
  },
  {
    label: 'Transaction type',
    key: 'transaction_type'
  },
  {
    label: 'Action',
    key: 'action',
    class: 'action'
  }
];


export const stoneCardData = [
  {
    invoiceImg: <CheckIcon />,
    invoiceNumber: 'DSC572271',
    referenceNumber: 'Vegas Proposal/AG',
    date: '23 Mar, 2023 - 10:00 PM',
    customerId: '01EC2487',
    quantity: '5',
    grossAmount: '22,322.00',
    transactionType: 'Memo',
    action: 'Print'
  },
  {
    invoiceImg: <CheckIcon />,
    invoiceNumber: 'DSC572271',
    referenceNumber: 'Vegas Proposal/AG',
    date: '23 Mar, 2023 - 10:00 PM',
    customerId: '01EC2487',
    quantity: '5',
    grossAmount: '22,322.00',
    transactionType: 'Memo',
    action: 'Print'
  },
  {
    invoiceImg: <CheckIcon />,
    invoiceNumber: 'DSC572271',
    referenceNumber: 'Vegas Proposal/AG',
    date: '23 Mar, 2023 - 10:00 PM',
    customerId: '01EC2487',
    quantity: '5',
    grossAmount: '22,322.00',
    transactionType: 'Memo',
    action: 'Print'
  },
  {
    invoiceImg: <CheckIcon />,
    invoiceNumber: 'DSC572271',
    referenceNumber: 'Vegas Proposal/AG',
    date: '23 Mar, 2023 - 10:00 PM',
    customerId: '01EC2487',
    quantity: '5',
    grossAmount: '22,322.00',
    transactionType: 'Memo',
    action: 'Print'
  },
  {
    invoiceImg: <CheckIcon />,
    invoiceNumber: 'DSC572271',
    referenceNumber: 'Vegas Proposal/AG',
    date: '23 Mar, 2023 - 10:00 PM',
    customerId: '01EC2487',
    quantity: '5',
    grossAmount: '22,322.00',
    transactionType: 'Memo',
    action: 'Print'
  },
  {
    invoiceImg: <CheckIcon />,
    invoiceNumber: 'DSC572271',
    referenceNumber: 'Vegas Proposal/AG',
    date: '23 Mar, 2023 - 10:00 PM',
    customerId: '01EC2487',
    quantity: '5',
    grossAmount: '22,322.00',
    transactionType: 'Memo',
    action: 'Print'
  },

];

export const stoneInnerTableHead = [
  { label: 'Contract No', key: 'ContractNo' },
  { label: 'Style no', key: 'StyleNo' },
  { label: 'Description', key: 'Description' },
  { label: 'JID', key: 'JID' },
  { label: 'Quantity', key: 'Qty' },
  { label: 'Rate R', key: 'Rate' },
  { label: 'Amount R', key: 'Amount' },
];


export const stoneInnerData = [
  {
    jobNumber: 'MF/098522',
    styleNo: 'DWBSP1Q-1.00-107524',
    description: ' ',
    jid: '23K0441L',
    quantity: '1',
    rateR: '$2,300.00',
    amountR: '$1,943.00',
  },

];
