"use client"
import { DataTable, DataTableExpand, FilterDropdown, TableFooter, TableHeader } from "@dilpesh/kgk-ui-library";
import { useRef, useState } from "react";

import { FilterLineIcon } from "@/assets/icons";
import { PageLimitOptions, SalesmanInsightType, useGetMemoData, useGetSalesmanFilterListQuery, useGetTranslation, usePagination, usePayloadFilter } from "@magneto-it-solutions/kgk-common-library";
import SalesmanInsightsPrintInvoiceModal from "../modal/print-invoice-modal/print-invoice-modal";
import { getStoneCardHead, getStoneInnerTableHead } from "./utils/constant";
import { SalesmanInsightsMemostyle } from "./utils/style";
import { coloumnDefStoneInner, getColoumnDefStoneCard } from "./utils/utility";


const SalesmanInsightsMemo = ({ params: { id } }) => {
  const { page, limit, changePage, changeLimit, changeSorting, sorting } = usePagination('memo-data');
  const { changeSearch, selectedFilter, handleFilterChange, clearFilters, applyFilters } = usePayloadFilter('memo-data');
  const { translation }: any = useGetTranslation();
  const { data, isLoading } = useGetMemoData(id);
  const ref: any = useRef();
  const [sipiModalOpen, setSipiModalOpen] = useState(false);
  const { data: FilteredList } = useGetSalesmanFilterListQuery(SalesmanInsightType.MEMO);
  const [isFilterView, setIsFilterView] = useState<boolean>(false);

  // Get translated table headers
  const translatedStoneCardHead = getStoneCardHead(translation);
  const translatedStoneInnerTableHead = getStoneInnerTableHead(translation);
  const collapsibleData = (rowItem) => {
    const detailData = rowItem?.detail_data || [];
    return (
      <td colSpan={translatedStoneCardHead.length + 2} className='stone_card_expanded_td'>

        <DataTable
          isMultiselect={false}
          column={translatedStoneInnerTableHead}
          coloumnDef={coloumnDefStoneInner}
          data={detailData}
        />
      </td>
    );
  };

  const onPrintInvoive = async (id) => {
    setSipiModalOpen(true);
  }

  return (
    <>
      <SalesmanInsightsMemostyle>
        <div className="container first_view_content">
          <div className="table_main_wpr">
            <TableHeader
              searchPlaceholder={translation?.search_memo}
              borderBtnText={translation?.filter}
              borderBtnIcon={<FilterLineIcon />}
              isSearch={true}
              borderBtnClick={() => setIsFilterView(!isFilterView)}
              onSearchChange={(e: any) => changeSearch(e.target.value)}
            />
            <FilterDropdown
              open={isFilterView}
              data={FilteredList?.data?.filters}
              onChangeFilter={handleFilterChange}
              selectedFilter={selectedFilter}
              onApplyFilter={applyFilters}
              onClearFilter={clearFilters}
              fetchOptions={{}}
              filterText={translation.Filters}
              clearAllBtnText={translation.ClearAll}
              applyBtntext={translation.Apply}
            />
            <DataTableExpand
              isMultiselect={false}
              column={translatedStoneCardHead}
              coloumnDef={getColoumnDefStoneCard({ isLoading, onPrintInvoive })}
              data={data?.data?.data || []}
              collapsibleData={collapsibleData}
              expandIconPosition=""
              ref={ref}
              fixedPosition="right"
              skeleton={isLoading}
              allowedSortingKeys={FilteredList?.data?.sort_fields}
              handleSortingChange={changeSorting}
              sorting={sorting}
            />
            <TableFooter
              skeleton={isLoading}
              currentPage={page}
              currentPageSize={limit}
              sortOptions={PageLimitOptions}
              recordsTotal={data?.data?.filteredRecords}
              setCurrentPage={changePage}
              setCurrentPageSize={changeLimit}
              showingText={translation.Showing}
              showLabel={translation.Show}
              OfText={translation.Of}
            />
          </div>
        </div>
      </SalesmanInsightsMemostyle>
      {sipiModalOpen && <SalesmanInsightsPrintInvoiceModal
        onHide={() => { setSipiModalOpen(false) }}
        show={sipiModalOpen}
        approveBtnEvent={() => { }}
        rejectBtnEvent={() => { setSipiModalOpen(false) }}
      />}
    </>
  )
}

export default SalesmanInsightsMemo
