import { CustomerInsightsProposalOrderModelStyle } from "@/modules/customer-insights/components/modal/proposal-order-model/utils/proposal-order-modal-style";
import { DataTable, Skeleton } from "@dilpesh/kgk-ui-library";
import { CloseIcon, getSessionItem, SESSION_KEYS, useGetProposalDetailsByIdQuery } from "@magneto-it-solutions/kgk-common-library";
import { Col, Row } from "antd";
import { format } from "date-fns";
import { assetsListingHead, coloumnDefAssetsListing } from "./utility";

export default function CustomerInsightsProposalOrderModel({
  show,
  onHide,
  isEdit,
  rejectBtnEvent,
  id,
}: any) {

  const { data, isLoading } = useGetProposalDetailsByIdQuery(id, { skip: !id });

  return (
    <CustomerInsightsProposalOrderModelStyle
      title={
        <h2>Proposal order</h2>
      }
      centered
      closeIcon={<CloseIcon />}
      open={show}
      onOk={onHide}
      onCancel={onHide}
      width={1400}
      wrapClassName="detail_modal"
      footer={''}
    >
      <form className="body">
        <div className="upper_data_wpr">
  <Row gutter={{ lg: 24 }}>
    <Col xl={12}>
      <div className="inner_data_wpr">
        <span>Proposal number</span>
        <p>{isLoading ? <Skeleton width="100px"  /> : data?.data?.proposal_number || "-"}</p>
      </div>
      <div className="inner_data_wpr">
        <span>Customer</span>
        <p>{isLoading ? <Skeleton width="100px"  /> : data?.data?.customer_name || "-"}</p>
      </div>
      <div className="inner_data_wpr">
        <span>Billing address</span>
        <p>{isLoading ? <Skeleton width="150px"  /> : data?.data?.billing_address || "-"}</p>
      </div>
      <div className="inner_data_wpr">
        <span>Shipping address</span>
        <p>{isLoading ? <Skeleton width="150px"  /> : data?.data?.shipping_address || "-"}</p>
      </div>
      <div className="inner_data_wpr">
        <span>Payment terms</span>
        <p>{isLoading ? <Skeleton width="100px"  /> : data?.data?.payment_terms || "-"}</p>
      </div>
    </Col>

    <Col xl={12}>
      <div className="inner_data_wpr">
        <span>Shipping date</span>
        <p>
          {isLoading ? (
            <Skeleton width="140px"  />
          ) : data?.data?.shipping_date ? (
            format(
              new Date(data.data.shipping_date),
              getSessionItem(SESSION_KEYS.DATE_FORMAT) || "dd MMM yyyy, h:mm a"
            )
          ) : (
            "-"
          )}
        </p>
      </div>
      <div className="inner_data_wpr">
        <span>Remarks</span>
        <p>{isLoading ? <Skeleton width="150px"  /> : data?.data?.remarks || "-"}</p>
      </div>
      <div className="inner_data_wpr">
        <span>Total quantity</span>
        <p>{isLoading ? <Skeleton width="80px"  /> : data?.data?.total_quantity ?? "-"}</p>
      </div>
      <div className="inner_data_wpr">
        <span>Total amount</span>
        <p>
          {isLoading ? (
            <Skeleton width="100px"  />
          ) : data?.data?.total_amount ? (
            getSessionItem(SESSION_KEYS.CURRENCY) + data.data.total_amount
          ) : (
            "-"
          )}
        </p>
      </div>
    </Col>
  </Row>
</div>

        <div className="proposal_details_wpr">
          <h2>Proposal details</h2>
          <DataTable
            isMultiselect={false}
            column={assetsListingHead}
            coloumnDef={coloumnDefAssetsListing(isLoading)}
            data={data?.data?.proposal_details}
            skeleton={isLoading}
          />
        </div>
      </form>
    </CustomerInsightsProposalOrderModelStyle>
  )
}
