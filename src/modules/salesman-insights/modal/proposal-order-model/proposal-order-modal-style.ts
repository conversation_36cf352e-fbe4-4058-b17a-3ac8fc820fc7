import { Media, bes, bl } from "@dilpesh/kgk-ui-library";
import { Modal } from "antd";
import styled, { css } from "styled-components";

export const SalesmanInsightsProposalOrderModelStyle = styled.div`
    ${(props) =>
        css`
            .ant-modal-body{
                padding-bottom: 32px !important;
                .body{
                    .upper_data_wpr{
                        padding-bottom: 24px;
                        border-bottom: 1px solid ${(props) => props?.theme?.line?.light};
                        .ant-row{
                            margin-bottom: -16px;
                            & > *{
                                margin-bottom: 16px;
                            }
                        }
                        .inner_data_wpr{
                            display: flex;
                            align-items: flex-start;
                            justify-content: start;
                            margin-bottom: 16px;
                            &:last-child{
                                margin-bottom: 0;
                            }
                            span{
                                display: block;
                                ${bes(props?.theme?.text?.mid, 400)};
                                min-width:136px;
                            }
                            p{  
                                padding-left: 12px;
                                ${bes(props?.theme?.text?.high, 400)};
                                width: calc(100% - 136px);
                            }
                        }
                    }
                    .proposal_details_wpr{
                        padding-top: 24px;
                        & > h2{
                            ${bl(props?.theme?.text?.high, 500)};
                            margin-bottom: 24px;
                        }
                        .img_icon_56{
                            width: 56px;
                            height: 56px;
                            @media ${Media.tablet}{
                                height: 48px;
                                width: 48px;
                            }
                        }
                    }
                }
            }
        `
    }
`