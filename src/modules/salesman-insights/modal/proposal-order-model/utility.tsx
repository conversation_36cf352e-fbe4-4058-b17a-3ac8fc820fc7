import { Skeleton } from "@dilpesh/kgk-ui-library";
import { getSessionItem, SESSION_KEYS } from "@magneto-it-solutions/kgk-common-library";

export const assetsListingHead = [
  {
    label: 'Image',
    key: 'Image',
  },
  {
    label: 'Style number',
    key: 'StyleNo',
  },
  {
    label: 'Product details',
    key: 'ProductDetail',
  },
  {
    label: 'Product size',
    key: 'ProductSize',
  },
  {
    label: 'Production remarks',
    key: 'ProductionRemarks',
  },
  {
    label: 'SP rate',
    key: 'SPRate',
  },
  {
    label: 'Quantity',
    key: 'Qty',
  },
  {
    label: 'Amount',
    key: 'Amount',
  },
];


const formatValue = (value: any) => {
  return value !== null && value !== undefined && value !== '' ? value : '-';
};

export const coloumnDefAssetsListing = (isLoading: boolean) => ({
  Image: {
    renderChildren: (item: any) =>
      isLoading ? (
        <Skeleton width="56px" />
      ) : (
        <div className='img_icon_56'>
          <img src={item.Image} alt="Product" />
        </div>
      ),
  },
  StyleNo: {
    renderChildren: (item: any) =>
      isLoading ? <Skeleton width="100px" /> : formatValue(item.StyleNo),
  },
  ProductDetail: {
    renderChildren: (item: any) =>
      isLoading ? <Skeleton width="160px" /> : formatValue(item.ProductDetail),
  },
  ProductSize: {
    renderChildren: (item: any) =>
      isLoading ? <Skeleton width="60px" /> : formatValue(item.ProductSize),
  },
  ProductionRemarks: {
    renderChildren: (item: any) =>
      isLoading ? <Skeleton width="120px" /> : formatValue(item.ProductionRemarks),
  },
  SPRate: {
    renderChildren: (item: any) =>
      isLoading ? (
        <Skeleton width="80px" />
      ) : item.SPRate || item.SPRate === 0 ? (
        getSessionItem(SESSION_KEYS.CURRENCY) + item.SPRate
      ) : (
        '-'
      ),
  },
  Qty: {
    renderChildren: (item: any) =>
      isLoading ? <Skeleton width="60px" /> : formatValue(item.Qty),
  },
  Amount: {
    renderChildren: (item: any) =>
      isLoading ? (
        <Skeleton width="100px" />
      ) : item.Amount || item.Amount === 0 ? (
        getSessionItem(SESSION_KEYS.CURRENCY) + item.Amount
      ) : (
        '-'
      ),
  },
});
