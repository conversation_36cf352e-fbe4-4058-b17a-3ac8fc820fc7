import { Btn, RadioLabel, SelectGroups } from "@dilpesh/kgk-ui-library";
import { CloseIcon } from "@magneto-it-solutions/kgk-common-library";
import { SalesmanInsightsPrintInvoiceModalstyle } from "./print-invoice-modal.style";

export default function SalesmanInsightsPrintInvoiceModal({
  show,
  onHide,
  rejectBtnEvent,
}: any) {


  const selectData = [
    {
      value: 'jewellery',
      label: 'Jewellery',
    },
    {
      value: 'ring',
      label: 'Ring',
    },
  ]

  return (
    <SalesmanInsightsPrintInvoiceModalstyle
      title={'Invoice print'}
      // centered
      closeIcon={<CloseIcon />}
      open={show}
      onOk={onHide}
      onCancel={onHide}
      width={464}
      wrapClassName="detail_modal"
      footer={[
        <Btn onClick={rejectBtnEvent} size="large">
          Cancel
        </Btn>,
        <Btn bg="fill" size="large">
          Download
        </Btn>,
      ]}
    >
      <form className="body">
        <SelectGroups label="Report format" options={selectData} />
        <div className="print_invoice_multi_radio">
          <p>File type</p>
          <div className="file_type_wpr">
            <RadioLabel name="file-type">PDF</RadioLabel>
            <RadioLabel name="file-type">Excel</RadioLabel>
            <RadioLabel name="file-type">CSV</RadioLabel>
            <RadioLabel name="file-type">HTML</RadioLabel>
          </div>
        </div>
      </form>
    </SalesmanInsightsPrintInvoiceModalstyle >
  )
}
