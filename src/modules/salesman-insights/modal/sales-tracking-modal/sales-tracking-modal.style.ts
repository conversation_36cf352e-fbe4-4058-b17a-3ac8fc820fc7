import { b2xs, bes, br } from "@dilpesh/kgk-ui-library";
import { Modal } from "antd";
import styled, { css } from "styled-components";

export const SalesmanInsightsSalesTrackingModalstyle = styled(Modal)`
    ${(props) =>
        css`
            .ant-modal-content{             
                .ant-modal-body{
                    .body{
                        .order_wpr{
                            margin-top: 3px;
                            margin-bottom: 32px;
                            background-color:${(props) => props?.theme?.colors?.brand_low};
                            padding: 16px;
                            & > p{
                                ${b2xs(props?.theme?.text?.mid, 400)}
                                margin-bottom: 8px;
                            }
                            .product_inner_detail{
                                display: flex;
                                justify-content: space-between;
                                align-items: flex-start;
                                .this_product_wpr{
                                    display: flex;
                                    justify-content: flex-start;
                                    align-items: flex-start;
                                    .this_img{
                                        width: 48px;
                                        height: 48px;
                                    }
                                    .this_content_wpr{
                                        padding-inline-start: 12px;
                                        .name_wpr{
                                            p{
                                                display: inline-block;
                                                margin-inline-end: 8px;
                                                ${bes(props?.theme?.text?.high, 400)}
                                            }
                                            span{
                                                ${bes(props?.theme?.text?.mid, 400)}
                                            }
                                        }
                                        .trail_wpr{
                                            span{
                                                ${b2xs(props?.theme?.text?.mid, 400)}
                                                padding-inline-end:16px;
                                                position: relative;
                                                &::after{
                                                    content: '';
                                                    position: absolute;
                                                    inset-inline-end: 8px;
                                                    top: 50%;
                                                    transform: translateY(-50%);
                                                    width: 1px;
                                                    height: 8px;
                                                    background-color:#E8E8E8;
                                                }
                                                &:last-child{
                                                    padding-inline-end: 0;
                                                    &::after{
                                                        display: none;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                .status_div{
                                    margin-left: 16px;
                                }
                            }
                        }
                        .track_order_wpr{
                            .inner_track{
                                padding-inline-start: 32px;
                                position: relative;
                                padding-bottom: 44px;
                                &:last-child{
                                    padding-bottom: 0;
                                }
                                &::after{
                                    content: '';
                                    position: absolute;
                                    width: 16px;
                                    height: 16px;
                                    top: 6px;
                                    inset-inline-start: 0;
                                    background:url(/crm/assets/img/pending-24.svg);
                                    background-position: center;
                                    background-repeat: no-repeat;
                                    background-size: cover;
                                }
                                &.success{
                                        &::after{
                                        content: '';
                                        position: absolute;
                                        width: 16px;
                                        height: 16px;
                                        top: 6px;
                                        inset-inline-start: 0;
                                        border-radius: 30px;
                                        background:${(props) => props?.theme?.status?.success};
                                    }
                                        &:before{
                                        content: '';
                                        position: absolute;
                                        width: 0;
                                        height: calc(100% - 32px);
                                        top: 30px;
                                        inset-inline-start: 8px;
                                        border-inline-start: 1px dashed ${(props) => props?.theme?.status?.success};
                                    }
                                }
                               p{
                                    ${br(props?.theme?.text?.high, 500)}
                                    margin-inline-end: 8px;
                                    display: inline-block;
                               }
                               span{
                                    ${br(props?.theme?.text?.mid, 400)}
                               }
                            }
                        }
                    }
                }
                .ant-modal-footer{
                    border-top: 0 !important;
                    margin-top: 0 !important;
                    padding-bottom:32px !important;
                    padding-top: 0 !important;
                }
            }   
        `
    }
`