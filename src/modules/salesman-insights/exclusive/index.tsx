"use client"
import { DownIcon, FilterLineIcon } from "@/assets/icons";
import { TableHeader } from "@dilpesh/kgk-ui-library";
import { Col, Dropdown, Row } from "antd";
import Link from "next/link";
import { useState } from "react";
import SalesmanInsightsExclusiveConcepts from "./components/exclusive-concepts";
import SalesmanInsightsExclusiveDesigns from "./components/exclusive-designs";
import SalesmanInsightsExclusiveStyles from "./components/exclusive-styles";
import { tabLinks } from "./utils/constant";
import { SalesmanInsightsExclusiveStyle } from "./utils/style";


const SalesmanInsightsExclusive = ({ params: { id } }) => {

  const [activeTab, setActiveTab] = useState(tabLinks[0].link)

  const viewRender = () => {
    switch (activeTab) {
      case 'concept':
        return <SalesmanInsightsExclusiveConcepts />

      case 'designs':
        return <SalesmanInsightsExclusiveDesigns />

      case 'styles':
        return <SalesmanInsightsExclusiveStyles />
    }
  }

  return (
    <SalesmanInsightsExclusiveStyle>
      <div className="container first_view_content">
        <div className="top_main_menu_wpr" id="top_main_menu_wpr">
          <Dropdown
            trigger={['click']}
            placement="bottomRight"
            getPopupContainer={() => document?.getElementById('top_main_menu_wpr') as HTMLElement}
            dropdownRender={() => (
              <div className="ant-dropdown-menu">
                <ul>
                  <li className="list_item active"><Link href={'#'}>Concept</Link></li>
                  <li className="list_item"><Link href={'#'}>Designs</Link></li>
                  <li className="list_item"><Link href={'#'}>Styles</Link></li>
                </ul>
              </div>
            )}
          >
            <div>
              <span>Concept</span>
              <div className="down_icon"><DownIcon /></div>
            </div>
          </Dropdown>
        </div>
        <Row gutter={{ lg: 32 }}>
          <Col sm={24} lg={5} xl={4} className='hide_in_tablet'>
            <div className='exclusive_sidebar'>
              <ul>
                {
                  tabLinks?.map((item: any, index: number) => (
                    <li
                      key={index}
                      className={`list_item ${activeTab == item.link ? 'active' : ''}`}
                    >
                      <a onClick={() => setActiveTab(item.link)}>{item.name}</a>
                    </li>
                  ))
                }
              </ul>
            </div>
          </Col>
          <Col lg={20}>
            <div className="table_main_wpr">
              <TableHeader
                searchPlaceholder="Search concept"
                borderBtnText="Filter"
                borderBtnIcon={<FilterLineIcon />}
                isSearch={true}
              />
              {viewRender()}
            </div>
          </Col>
        </Row>
      </div>
    </SalesmanInsightsExclusiveStyle>
  )
}

export default SalesmanInsightsExclusive
