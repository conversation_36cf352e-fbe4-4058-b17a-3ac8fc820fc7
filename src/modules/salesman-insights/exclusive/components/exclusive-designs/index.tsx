import { DataTable, TableFooter } from "@dilpesh/kgk-ui-library";
import { useState } from "react";
import { SalesmanInsightsExclusiveDesignsstyle } from "./utils/style";
import { assetsListingData, assetsListingHead } from "./utils/constant";


const SalesmanInsightsExclusiveDesigns = () => {

    const [currentPage, setCurrentPage] = useState(1);
    const [currentPageSize, setCurrentPageSize] = useState(25);
    const [recordsTotal, setRecordsTotal] = useState(500);

    
    const coloumnDefAssetsListing: Object = {
        customer: {
            renderChildren: (item: any) => {
                return (
                    <div className='icon_name_24_bes'>
                        <img src={item.customerIcon} alt="" />
                        <p>{item.customer}</p>
                    </div>
                );
            },
        },
        image: {
            renderChildren: (item: any) => {
                return (
                    <div className='img_32'>
                        <img src={item.image} alt="" />
                    </div>
                );
            },
        },
        designNumber: {
            renderChildren: (item: any) => {
                return (
                    <div className='bes_500_wpr'>
                        <p>{item.designNumber}</p>
                    </div>
                );
            },
        },
    };

    

    return (
        <SalesmanInsightsExclusiveDesignsstyle>
            <DataTable
                isMultiselect={false}
                column={assetsListingHead}
                coloumnDef={coloumnDefAssetsListing}
                data={assetsListingData}
                bodyClass='body_pt_8'
            />
            <TableFooter
                currentPageSize={currentPageSize}
                currentPage={currentPage}
                recordsTotal={recordsTotal}
                setCurrentPage={setCurrentPage}
                setCurrentPageSize={setCurrentPageSize}
                setRecordsTotal={setRecordsTotal}
            />
        </SalesmanInsightsExclusiveDesignsstyle>
    )
}

export default SalesmanInsightsExclusiveDesigns
