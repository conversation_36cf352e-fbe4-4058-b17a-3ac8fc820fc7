import { bes } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const SalesmanInsightsExclusiveStylesstyle = styled.div`
    ${(props) =>
        css`
            .icon_name_24_bes{
                display: flex;
                justify-content: flex-start;
                align-items: center;
                svg,img{
                    height: 24px;
                    width: 24px;
                }
                p{
                    padding-left: 8px;
                    ${bes(props?.theme?.text?.high, 400)}
                }
            }
            .bes_500_wpr{
                p{
                    display: block;
                    ${bes(props?.theme?.text?.high, 500)}
                }
            }
        `
    }

`