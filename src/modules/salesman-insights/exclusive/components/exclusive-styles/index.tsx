import { DataTable, TableFooter } from "@dilpesh/kgk-ui-library";
import { useState } from "react";
import { SalesmanInsightsExclusiveStylesstyle } from "./utils/style";
import { assetsListingData, assetsListingHead } from "./utils/constant";

const SalesmanInsightsExclusiveStyles = () => {

    const [currentPage, setCurrentPage] = useState(1);
    const [currentPageSize, setCurrentPageSize] = useState(25);
    const [recordsTotal, setRecordsTotal] = useState(500);

    
    const coloumnDefAssetsListing: Object = {
        customer: {
            renderChildren: (item: any) => {
                return (
                    <div className='icon_name_24_bes'>
                        <img src={item.customerIcon} alt="" />
                        <p>{item.customer}</p>
                    </div>
                );
            },
        },
        styleNumber: {
            renderChildren: (item: any) => {
                return (
                    <div className='bes_500_wpr'>
                        <p>{item.styleNumber}</p>
                    </div>
                );
            },
        },
    };

    
    return (
        <SalesmanInsightsExclusiveStylesstyle>
            <DataTable
                isMultiselect={false}
                column={assetsListingHead}
                coloumnDef={coloumnDefAssetsListing}
                data={assetsListingData}
                fixedPosition="left"
            />
            <TableFooter
                currentPageSize={currentPageSize}
                currentPage={currentPage}
                recordsTotal={recordsTotal}
                setCurrentPage={setCurrentPage}
                setCurrentPageSize={setCurrentPageSize}
                setRecordsTotal={setRecordsTotal}
            />
        </SalesmanInsightsExclusiveStylesstyle>
    )
}

export default SalesmanInsightsExclusiveStyles
