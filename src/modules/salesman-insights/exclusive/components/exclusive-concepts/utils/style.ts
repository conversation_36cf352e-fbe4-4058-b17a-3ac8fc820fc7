import { Media, bes } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const SalesmanInsightsExclusiveConceptsstyle = styled.div`
    ${(props) =>
        css`
            .icon_name_24_bes{
                display: flex;
                justify-content: flex-start;
                align-items: center;
                svg,img{
                    height: 24px;
                    width: 24px;
                }
                p{
                    padding-left: 8px;
                    ${bes(props?.theme?.text?.high, 400)}
                }
            }
            .bes_500_wpr{
                p{
                    display: block;
                    ${bes(props?.theme?.text?.high, 500)}
                }
            }
            .concept_decription{
                position: sticky;
                left: 0;
                padding: 12px 24px;
                display: flex;
                justify-content: flex-start;
                align-items: flex-start;
                @media ${Media.ultra_mobile}{
                    flex-wrap: wrap;
                    padding: 4px 16px;
                }
                & > label{
                    ${bes(props?.theme?.text?.mid, 400)};
                    display: block;
                    min-width: 164px;
                    margin-right: 60px;
                    @media ${Media.tablet}{
                        margin-right: 32px;
                        min-width: auto;
                    }
                    @media ${Media.ultra_mobile}{
                        width: 100%;
                        margin-bottom: 12px;
                    }
                }
                .concept_inner_data{
                    overflow: hidden;
                    flex: 1;
                    p{
                        ${bes(props?.theme?.text?.high, 400)};
                        white-space: pre-wrap;
                    }
                    .this_img_main_wpr{
                        margin-top: 16px;
                        display: flex;
                        justify-content: flex-start;
                        align-items: flex-start;
                        flex-wrap: wrap;
                        margin-left: -8px;
                        margin-right: -8px;
                        margin-bottom: -16px;
                        @media ${Media.ultra_mobile}{
                            margin-top: 12px;
                            margin-left: -6px;
                            margin-right: -6px;
                            margin-bottom: -12px;
                        }
                        .img_wpr{
                            width: 120px;
                            margin-left: 8px;
                            margin-right: 8px;
                            margin-bottom: 16px;
                            @media ${Media.ultra_mobile}{
                                width: 100px;
                                margin-left: 6px;
                                margin-right: 6px;
                                margin-bottom: 12px;
                            }
                            @media ${Media.mobile}{
                                width: calc(33.33% - 12px);
                            }
                            img{
                                border-radius: 4px;
                            }
                        }
                    }
                }
            }
        `
    }

`