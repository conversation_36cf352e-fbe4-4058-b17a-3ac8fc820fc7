import { DataTableExpand, TableFooter } from "@dilpesh/kgk-ui-library";
import { useLayoutEffect, useRef, useState } from "react";
import { SalesmanInsightsExclusiveConceptsstyle } from "./utils/style";
import { listData, listDataTableHead } from "./utils/constant";


const SalesmanInsightsExclusiveConcepts = () => {

    const [currentPage, setCurrentPage] = useState(1);
    const [currentPageSize, setCurrentPageSize] = useState(25);
    const [recordsTotal, setRecordsTotal] = useState(500);



    const coloumnDefListing: Object = {
        conceptNumber: {
            renderChildren: (item: any) => {
                return (
                    <div className='bes_500_wpr'>
                        <p>{item.conceptNumber}</p>
                    </div>
                );
            },
        },
        name: {
            renderChildren: (item: any) => {
                return (
                    <div className='icon_name_24_bes'>
                        <img src={item.nameIcon} alt="" />
                        <p>{item.name}</p>
                    </div>
                );
            },
        },
        market: {
            renderChildren: (item: any) => {
                return (
                    <div className='icon_name_24_bes'>
                        {item.marketIcon}
                        <p>{item.market}</p>
                    </div>
                );
            },
        },
        createdBy: {
            renderChildren: (item: any) => {
                return (
                    <div className='icon_name_24_bes'>
                        <img src={item.createdByIcon} alt="" />
                        <p>{item.createdBy}</p>
                    </div>
                );
            },
        },
    };

    const ref: any = useRef();

    const [width, setWidth] = useState(0);

    useLayoutEffect(() => {
        setWidth(ref.current.offsetWidth);
    }, []);

    const collapsibleData = () => {
        return (
            <td colSpan={listDataTableHead.length + 2}>
                <div className="concept_decription" style={{ width: width }}>
                    <label>Description</label>
                    <div className="concept_inner_data">
                        <p>A jewellery collection inspired by the moon's allure. Rings, necklaces, and earrings that capture its luminous beauty.</p>
                        <div className="this_img_main_wpr">
                            <div className="img_wpr"><img src="https://encrypted-tbn2.gstatic.com/images?q=tbn:ANd9GcScCS42etmyAJH-KNoU8S9-7eQagWfPrnC5hLHRmEotq3G1j6kF" alt="" /></div>
                            <div className="img_wpr"><img src="https://encrypted-tbn2.gstatic.com/images?q=tbn:ANd9GcScCS42etmyAJH-KNoU8S9-7eQagWfPrnC5hLHRmEotq3G1j6kF" alt="" /></div>
                            <div className="img_wpr"><img src="https://encrypted-tbn2.gstatic.com/images?q=tbn:ANd9GcScCS42etmyAJH-KNoU8S9-7eQagWfPrnC5hLHRmEotq3G1j6kF" alt="" /></div>
                        </div>
                    </div>
                </div>
            </td>
        )
    }

    return (
        <SalesmanInsightsExclusiveConceptsstyle>
            <DataTableExpand
                isMultiselect={true}
                column={listDataTableHead}
                coloumnDef={coloumnDefListing}
                data={listData}
                collapsibleData={collapsibleData}
                expandIconPosition="left"
                fixedPosition="arrow_multiselect_third"
                ref={ref}
                bodyClass="body_pt_12"
            />
            <TableFooter
                currentPageSize={currentPageSize}
                currentPage={currentPage}
                recordsTotal={recordsTotal}
                setCurrentPage={setCurrentPage}
                setCurrentPageSize={setCurrentPageSize}
                setRecordsTotal={setRecordsTotal}
            />
        </SalesmanInsightsExclusiveConceptsstyle>
    )
}

export default SalesmanInsightsExclusiveConcepts
