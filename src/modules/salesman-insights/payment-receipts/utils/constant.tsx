import { BankIcon, CheckIcon } from "@/assets/icons";

export const stoneCardHead = [
  { label: 'Settlement no', key: 'settlement_number' },
  { label: 'Reference number', key: 'reference_number' },
  { label: 'Date', key: 'settlement_date' },
  { label: 'Customer name', key: 'customer' },
  { label: 'Bank / Cash A/c Name', key: 'bank_name' },
  { label: 'Cheque no', key: 'cheque_number' },
  { label: 'Amount', key: 'amount' },
  { label: 'Action', key: 'action', class: 'action' },
];

export const stoneInnerTableHead = [
  { label: 'Invoice no', key: 'InvoiceNo' },
  { label: 'Reference number', key: 'ReferenceNumber' },
  { label: 'Date', key: 'InvoiceDate' },
  { label: 'Invoice amount', key: 'InvoiceAmount' },
  { label: 'Settled amount', key: 'SettlementAmount' },
  { label: 'Discount', key: 'DiscountAmount' },
];


export const stoneCardData = [
  {
    invoiceImg: <CheckIcon />,
    settlementNo: 'FSIL-230096',
    referenceNumber: 'Vegas Proposal/AG',
    date: '23 Mar, 2023 - 10:00 PM',
    customerName: '01EC2487',
    bankIcon: <BankIcon />,
    bankAcName: 'Bank of America',
    chequeNo: '5',
    amount: '$1,049.00',
  },
  {
    invoiceImg: <CheckIcon />,
    settlementNo: 'FSIL-230096',
    referenceNumber: 'Vegas Proposal/AG',
    date: '23 Mar, 2023 - 10:00 PM',
    customerName: '01EC2487',
    bankIcon: <BankIcon />,
    bankAcName: 'Bank of America',
    chequeNo: '5',
    amount: '$1,049.00',
  },
  {
    invoiceImg: <CheckIcon />,
    settlementNo: 'FSIL-230096',
    referenceNumber: 'Vegas Proposal/AG',
    date: '23 Mar, 2023 - 10:00 PM',
    customerName: '01EC2487',
    bankIcon: <BankIcon />,
    bankAcName: 'Bank of America',
    chequeNo: '5',
    amount: '$1,049.00',
  },
  {
    invoiceImg: <CheckIcon />,
    settlementNo: 'FSIL-230096',
    referenceNumber: 'Vegas Proposal/AG',
    date: '23 Mar, 2023 - 10:00 PM',
    customerName: '01EC2487',
    bankIcon: <BankIcon />,
    bankAcName: 'Bank of America',
    chequeNo: '5',
    amount: '$1,049.00',
  },

];



export const stoneInnerData = [
  {
    invoiceNo: 'MF/098522',
    referenceNumber: 'GR-TOWSEND',
    date: '23 Mar, 2023',
    invoiceAmount: '$1,943.00',
    settledAmount: '$1,943.00',
    discount: '$0.00',
  },
];
