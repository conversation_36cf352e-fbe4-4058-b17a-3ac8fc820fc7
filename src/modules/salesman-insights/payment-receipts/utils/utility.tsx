interface ColumnDefProps {
  onPrintInvoice: (item: any) => void;
  isLoading: boolean;
}
import { BankIcon, CheckIcon, PrintIcon } from "@/assets/icons";
import { Skeleton } from "@dilpesh/kgk-ui-library";
import { getSessionItem, SESSION_KEYS } from "@magneto-it-solutions/kgk-common-library";
import { format } from "date-fns";
import Link from "next/link";

export const getColoumnDefStoneCard = ({ onPrintInvoice, isLoading }: ColumnDefProps): Record<string, any> => ({
  settlement_number: {
    renderChildren: (item: any) =>
      isLoading ? (
        <Skeleton width="100px" />
      ) : (
        <div className="icon_16_bes">
          <p>{item?.settlement_number || '-'}</p>
          {item?.settlement_number && <CheckIcon />}
        </div>
      ),
  },
  reference_number: {
    renderChildren: (item: any) =>
      isLoading ? <Skeleton width="160px" /> : item?.reference_number?.trim() || '-',
  },
  settlement_date: {
    renderChildren: (item: any) =>
      isLoading ? (
        <Skeleton width="140px" />
      ) : item?.settlement_date ? (
        format(
          new Date(item.settlement_date),
          getSessionItem(SESSION_KEYS.DATE_FORMAT) || 'dd MMM yyyy, h:mm a'
        )
      ) : (
        '-'
      ),
  },
  customer: {
    renderChildren: (item: any) =>
      isLoading ? <Skeleton width="120px" /> : item?.customer?.trim() || '-',
  },
  bank_name: {
    renderChildren: (item: any) =>
      isLoading ? (
        <Skeleton width="120px" />
      ) : (
        <div className="icon_name_24_bes">
          {item?.bank_name && <BankIcon />}
          <p>{item?.bank_name || '-'}</p>
        </div>
      ),
  },
  cheque_number: {
    renderChildren: (item: any) =>
      isLoading ? <Skeleton width="100px" /> : item?.cheque_number?.trim() || '-',
  },
  amount: {
    renderChildren: (item: any) =>
      isLoading ? (
        <Skeleton width="100px" />
      ) : item?.amount ? (
        getSessionItem(SESSION_KEYS.CURRENCY) + item.amount
      ) : (
        '-'
      ),
  },
  action: {
    renderChildren: (item: any) =>
      isLoading ? (
        <Skeleton width="60px" />
      ) : (
        <div className="action_wpr">
          <Link href="#" onClick={() => onPrintInvoice(item)}>
            <PrintIcon />
          </Link>
        </div>
      ),
  },
});

const currency = getSessionItem(SESSION_KEYS.CURRENCY) || '';


export const coloumnDefStoneInner: Record<string, any> = {
  InvoiceNo: {
    renderChildren: (item: any) => item?.InvoiceNo ?? '-',
  },
  ReferenceNumber: {
    renderChildren: (item: any) => item?.ReferenceNumber ?? '-',
  },
  InvoiceDate: {
    renderChildren: (item: any) =>
      item?.InvoiceDate
        ? format(new Date(item.InvoiceDate), getSessionItem(SESSION_KEYS.DATE_FORMAT) || 'dd MMM yyyy, h:mm a')
        : '-',
  },
  InvoiceAmount: {
    renderChildren: (item: any) =>
      item?.InvoiceAmount != null
        ? `${currency} ${item.InvoiceAmount}`
        : '-',
  },
  SettlementAmount: {
    renderChildren: (item: any) =>
      item?.SettlementAmount != null
        ? `${currency} ${item.SettlementAmount}`
        : '-',
  },
  DiscountAmount: {
    renderChildren: (item: any) =>
      item?.DiscountAmount != null
        ? `${currency} ${item.DiscountAmount}`
        : '-',
  },
};
