"use client"
import { FilterLineIcon } from "@/assets/icons";
import { DataTable, DataTableExpand, FilterDropdown, TableFooter, TableHeader } from "@dilpesh/kgk-ui-library";
import { PageLimitOptions, SalesmanInsightType, useGetPaymentData, useGetSalesmanFilterListQuery, useGetTranslation, usePagination, usePayloadFilter } from "@magneto-it-solutions/kgk-common-library";
import { useRef, useState } from "react";
import { stoneCardHead, stoneInnerTableHead } from "./utils/constant";
import { SalesmanInsightsPaymentReceiptsstyle } from "./utils/style";
import { coloumnDefStoneInner, getColoumnDefStoneCard } from "./utils/utility";


const SalesmanInsightsPaymentReceipts = ({ params: { id } }) => {

  const { data, isLoading } = useGetPaymentData(id);
  const { page, limit, changePage, changeLimit, changeSorting, sorting } = usePagination('payment-data');
  const { changeSearch, selectedFilter, handleFilterChange, clearFilters, applyFilters } = usePayloadFilter('payment-data');
  const { data: FilteredList } = useGetSalesmanFilterListQuery(SalesmanInsightType.RECEIPT);
  const [isFilterView, setIsFilterView] = useState<boolean>(false);
  const { translation } = useGetTranslation();
  const ref: any = useRef();

  const collapsibleData = (rowItem) => {
    const detailData = rowItem?.payment_details || [];
    return (
      <td colSpan={stoneCardHead.length + 2} className='stone_card_expanded_td'>

        <DataTable
          isMultiselect={false}
          column={stoneInnerTableHead}
          coloumnDef={coloumnDefStoneInner}
          data={detailData}
          skeleton={isLoading}
        />
      </td>
    );
  };


  const onPrintInvoice = (rowItem) => {
  }




  return (
    <SalesmanInsightsPaymentReceiptsstyle>
      <div className="container first_view_content">
        <div className="table_main_wpr">
          <TableHeader
            searchPlaceholder={translation?.search_receipt}
            borderBtnText={translation?.filter}
            borderBtnIcon={<FilterLineIcon />}
            isSearch={true}
            borderBtnClick={() => setIsFilterView(!isFilterView)}
            onSearchChange={(e: any) => changeSearch(e.target.value)}
          />
          <FilterDropdown
            open={isFilterView}
            data={FilteredList?.data?.filters}
            onChangeFilter={handleFilterChange}
            selectedFilter={selectedFilter}
            onApplyFilter={applyFilters}
            onClearFilter={clearFilters}
            fetchOptions={{}}
            filterText={translation.Filters}
            clearAllBtnText={translation.ClearAll}
            applyBtntext={translation.Apply}
          />
          <DataTableExpand
            isMultiselect={false}
            column={stoneCardHead}
            coloumnDef={getColoumnDefStoneCard({ onPrintInvoice, isLoading })}
            data={data?.data?.data}
            collapsibleData={collapsibleData}
            expandIconPosition=""
            ref={ref}
            skeleton={isLoading}
            fixedPosition="right"
            allowedSortingKeys={FilteredList?.data?.sort_fields}
            handleSortingChange={changeSorting}
            sorting={sorting}
          />
          <TableFooter
            skeleton={isLoading}
            currentPage={page}
            currentPageSize={limit}
            sortOptions={PageLimitOptions}
            recordsTotal={data?.data?.filteredRecords}
            setCurrentPage={changePage}
            setCurrentPageSize={changeLimit}
            showingText={translation.Showing}
            showLabel={translation.Show}
            OfText={translation.Of}
          />
        </div>
      </div>
    </SalesmanInsightsPaymentReceiptsstyle>
  )
}

export default SalesmanInsightsPaymentReceipts
