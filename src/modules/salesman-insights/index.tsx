"use client";
import { FilterLineIcon } from "@/assets/icons/collection/FilterLineIcon";
import {
  DataTable,
  FilterDropdown,
  Skeleton,
  TableFooter,
  TableHeader,
  TableStatus
} from "@dilpesh/kgk-ui-library";
import { PageLimitOptions, SalesmanInsightType, useGetSalesmanData, useGetSalesmanFilterListQuery, useGetTranslation, usePagination, usePayloadFilter } from "@magneto-it-solutions/kgk-common-library";
import { useRouter } from "next/navigation";
import {
  assetsListingHead
} from "./utils/constant";
import { SalesmanInsightsStyle } from "./utils/style";
import { useState } from "react";

const SalesmanInsights = () => {
  const router = useRouter();
  const { data, isLoading } = useGetSalesmanData();
  const { page, limit, changePage, changeLimit, changeSorting, sorting } = usePagination('salesman-data');
  const { changeSearch, selectedFilter, handleFilter<PERSON>hange, clearFilters, applyFilters } = usePayloadFilter('salesman-data');
  const { data: FilteredList } = useGetSalesmanFilterListQuery(SalesmanInsightType.INSIGHT);
  const [isFilterView, setIsFilterView] = useState<boolean>(false);

  // Prepare the data from API
  const apiData = data?.data?.data || [];
  const { translation }: any = useGetTranslation();


  const coloumnDefAssetsListing: Record<string, any> = {
    name: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="120px" />
        ) : (
          <div
            className="icon_name_24_8_bes"
            onClick={() =>
              router.push(`/salesman-insights/summary/${item.customer_code}?name=${encodeURIComponent(item.name || '')}`)
            }
          >
            <img
              src={
                item.profile
                  ? process.env.NEXT_PUBLIC_ASSETS_URL + item.profile
                  : "/fallback-user-icon.png"
              }
              alt=""
              style={{ width: 24, height: 24, borderRadius: "50%" }}
            />
            <p>{item.name || '-'}</p>
          </div>
        ),
    },
    status: {
      renderChildren: (item: any) => (
        <TableStatus
          skeleton={isLoading}
          className={item.status === "Active" ? "active" : "inactive"}
        >
          {item.status || '-'}
        </TableStatus>
      ),
    },
    head_salesman: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : item.head_salesman || '-',
    },
    business_type: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="80px" /> : item.business_type || '-',
    },
    open_order: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="60px" /> : item.open_order ?? '-',
    },
    customer_code: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : item.customer_code || '-',
    },
    target: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="60px" /> : item.target ?? '-',
    },
    fg_sales: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="60px" /> : item.fg_sales ?? '-',
    },
    rm_sales: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="60px" /> : item.rm_sales ?? '-',
    },
    total: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="60px" /> : item.total ?? '-',
    },
  };



  return (
    <SalesmanInsightsStyle>
      <div className="container first_view_content">
        {/* <BreadCrumbs value={breadCrumbsValue1} />
                <h1>Salesman insights</h1> */}
        <div className="table_main_wpr">
          <TableHeader
            searchPlaceholder="Search customer"
            borderBtnText="Filter"
            borderBtnIcon={<FilterLineIcon />}
            isSearch={true}
          borderBtnClick={() => setIsFilterView(!isFilterView)}
            onSearchChange={(e: any) => changeSearch(e.target.value)}
          />
           <FilterDropdown
              open={isFilterView}
              data={FilteredList?.data?.filters}
              onChangeFilter={handleFilterChange}
              selectedFilter={selectedFilter}
              onApplyFilter={applyFilters}
              onClearFilter={clearFilters}
              fetchOptions={{}}
              filterText={translation.Filters}
              clearAllBtnText={translation.ClearAll}
              applyBtntext={translation.Apply}
            />
          <DataTable
            isMultiselect={false}
            column={assetsListingHead}
            coloumnDef={coloumnDefAssetsListing}
            data={apiData}
            allowedSortingKeys={FilteredList?.data?.sort_fields}
            handleSortingChange={changeSorting}
            sorting={sorting}
            bodyClass="body_pt_12"
          />
          <TableFooter
            skeleton={isLoading}
            currentPage={page}
            currentPageSize={limit}
            sortOptions={PageLimitOptions}
            recordsTotal={data?.data?.filteredRecords}
            setCurrentPage={changePage}
            setCurrentPageSize={changeLimit}
            showingText={translation.Showing}
            showLabel={translation.Show}
            OfText={translation.Of}
          />
        </div>
      </div>
    </SalesmanInsightsStyle>
  );
};

export default SalesmanInsights;
