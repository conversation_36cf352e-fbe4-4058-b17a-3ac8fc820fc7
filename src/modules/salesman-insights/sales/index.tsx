"use client"
import { FilterLineIcon } from "@/assets/icons";
import { DataTable, DataTableExpand, FilterDropdown, TableFooter, TableHeader } from "@dilpesh/kgk-ui-library";
import { PageLimitOptions, SalesmanInsightType, useGetSalesData, useGetSalesmanFilterListQuery, useGetTranslation, useModal, usePagination, usePayloadFilter } from "@magneto-it-solutions/kgk-common-library";
import { useRef, useState } from "react";
import SalesmanInsightsSalesTrackingModal from "../modal/sales-tracking-modal/sales-tracking-modal";
import { stoneCardHead, stoneInnerTableHead } from "./utils/constant";
import { SalesmanInsightsSalesstyle } from "./utils/style";
import { coloumnDefStoneCard, coloumnDefStoneInner } from "./utils/utility";


const SalesmanInsightsSales = ({ params: { id } }) => {

  const { data, isLoading } = useGetSalesData(id);
  const { page, limit, changePage, changeLimit, changeSorting, sorting } = usePagination('sales-data');
  const { changeSearch, selectedFilter, handleFilterChange, clearFilters, applyFilters } = usePayloadFilter('sales-data');
    const { data: FilteredList } = useGetSalesmanFilterListQuery(SalesmanInsightType.SALES);
  const [isFilterView, setIsFilterView] = useState<boolean>(false);
  const { translation } = useGetTranslation();
  const [cipiModalOpen, setCipiModalOpen] = useState(false);
  const ref: any = useRef();

  const collapsibleData = (rowItem) => {
    const detailData = rowItem?.component_details || [];
    return (
      <td colSpan={stoneCardHead.length + 2} className='stone_card_expanded_td'>

        <DataTable
          isMultiselect={false}
          column={stoneInnerTableHead}
          coloumnDef={coloumnDefStoneInner}
          data={detailData}
          skeleton={isLoading}
        />
      </td>
    );
  };



  const onPrintInvoive = async (id: any) => {
    setCipiModalOpen(true);

  }
  return (
    <>
      <SalesmanInsightsSalesstyle>
        <div className="container first_view_content">
          <div className="table_main_wpr">
             <TableHeader
              searchPlaceholder="Search customer"
              borderBtnText="Filter"
              borderBtnIcon={<FilterLineIcon />}
              isSearch={true}
              borderBtnClick={() => setIsFilterView(!isFilterView)}
              onSearchChange={(e: any) => changeSearch(e.target.value)}
              />
             <FilterDropdown
             open={isFilterView}
             data={FilteredList?.data?.filters}
             onChangeFilter={handleFilterChange}
             selectedFilter={selectedFilter}
             onApplyFilter={applyFilters}
             onClearFilter={clearFilters}
             fetchOptions={{}}
             filterText={translation.Filters}
             clearAllBtnText={translation.ClearAll}
             applyBtntext={translation.Apply}
           />
            <DataTableExpand
              isMultiselect={false}
              column={stoneCardHead}
              coloumnDef={coloumnDefStoneCard(isLoading, onPrintInvoive)}
              data={data?.data?.data}
              skeleton={isLoading}
              collapsibleData={collapsibleData}
              ref={ref}
              allowedSortingKeys={FilteredList?.data?.sort_fields}
              handleSortingChange={changeSorting}
              sorting={sorting}
              fixedPosition="right"
            />
            <TableFooter
              skeleton={isLoading}
              currentPage={page}
              currentPageSize={limit}
              sortOptions={PageLimitOptions}
              recordsTotal={data?.data?.filteredRecords}
              setCurrentPage={changePage}
              setCurrentPageSize={changeLimit}
              showingText={translation.Showing}
              showLabel={translation.Show}
              OfText={translation.Of}
            />
          </div>
        </div>
      </SalesmanInsightsSalesstyle>
      {
        cipiModalOpen && <SalesmanInsightsSalesTrackingModal
          onHide={() => { setCipiModalOpen(false) }}
          show={cipiModalOpen}
          approveBtnEvent={() => { }}
          rejectBtnEvent={() => { setCipiModalOpen(false) }}
        />
      }
    </>
  )
}

export default SalesmanInsightsSales
