import { CheckIcon, CopyIcon, PrintIcon } from "@/assets/icons";
import { Skeleton } from "@dilpesh/kgk-ui-library";
import { getSessionItem, SESSION_KEYS, TrackOrderIcon } from "@magneto-it-solutions/kgk-common-library";
import { format } from "date-fns";
import Link from "next/link";

export const coloumnDefStoneCard = (isLoading: boolean, onPrintInvoive: (item: any) => void) => ({
  action: {
    renderChildren: (item: any) => {
      return isLoading ? (
        <Skeleton width="80px" />
      ) : (
        <div className="action_wpr">
          <div onClick={() => onPrintInvoive(item)}>
            <TrackOrderIcon />
          </div>
          <Link href="#">
            <CopyIcon />
          </Link>
          <Link href="#">
            <PrintIcon />
          </Link>
        </div>
      );
    },
  },
  invoice_no: {
    renderChildren: (item: any) =>
      isLoading ? (
        <Skeleton width="120px" />
      ) : (
        <div className="icon_16_bes">
          <p>{item?.invoice_no?.trim() || "-"}</p>
          {item?.invoice_no && <CheckIcon />}
        </div>
      ),
  },
  reference_number: {
    renderChildren: (item: any) =>
      isLoading ? <Skeleton width="140px" /> : item?.reference_number?.trim() || "-",
  },
  invoice_date: {
    renderChildren: (item: any) =>
      isLoading ? (
        <Skeleton width="120px" />
      ) : item?.invoice_date ? (
        format(
          new Date(item.invoice_date),
          getSessionItem(SESSION_KEYS.DATE_FORMAT) || "dd MMM yyyy, h:mm a"
        )
      ) : (
        "-"
      ),
  },
  customer_name: {
    renderChildren: (item: any) =>
      isLoading ? <Skeleton width="160px" /> : item?.customer_name?.trim() || "-",
  },
  so_number: {
    renderChildren: (item: any) =>
      isLoading ? <Skeleton width="100px" /> : item?.so_number?.trim() || "-",
  },
  qty: {
    renderChildren: (item: any) =>
      isLoading ? <Skeleton width="60px" /> : item?.qty ?? "-",
  },
  total_amount: {
    renderChildren: (item: any) =>
      isLoading ? (
        <Skeleton width="100px" />
      ) : item?.total_amount ? (
        `$${Number(item.total_amount).toLocaleString(undefined, {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        })}`
      ) : (
        "-"
      ),
  },
  settlement_status: {
    renderChildren: (item: any) =>
      isLoading ? <Skeleton width="100px" /> : item?.settlement_status?.trim() || "-",
  },
});


export const coloumnDefStoneInner: Object = {
  contact_no: {
    renderChildren: (item: any) => item?.contact_no?.trim() || '-',
  },
  style_no: {
    renderChildren: (item: any) => (
      <div className="icon_name_24_bes">
        <p>{item?.style_no?.trim() || '-'}</p>
      </div>
    ),
  },
  description: {
    renderChildren: (item: any) => item?.description?.trim() || '-',
  },
  cs_stamping: {
    renderChildren: (item: any) => item?.cs_stamping?.trim() || '-',
  },
  dia_quality: {
    renderChildren: (item: any) => item?.dia_quality?.trim() || '-',
  },
  quantity: {
    renderChildren: (item: any) => item?.quantity ?? '-',
  },
  rate_r: {
    renderChildren: (item: any) => item?.rate_r ?? '-',
  },
  amount_r: {
    renderChildren: (item: any) => item?.amount_r ?? '-',
  },
};
