import { b2xs, bes } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const SalesmanInsightsSalesstyle = styled.div`
    ${(props) =>
        css`
            .table_main_wpr{
                .icon_name_24_bes{
                    display: flex;
                    justify-content: flex-start;
                    align-items: center;
                    p{
                        ${bes(props?.theme?.text?.high, 400)}
                    }
                }
                .icon_16_bes{
                    display: flex;
                    justify-content: flex-start;
                    align-items: center;
                    p{
                        padding-right: 8px;
                        ${bes(props?.theme?.text?.high, 500)}
                    }
                    svg , img{
                        width: 16px;
                        height: 16px;
                    }
                }
                .align_center{
                    text-align: center;
                    p{
                        ${bes(props?.theme?.text?.high, 400)}
                    }
                }
                .action_wpr{
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    & > *{
                        display: block;
                        width: 24px;
                        height: 24px;
                        margin-right: 8px;
                        cursor: pointer;
                        svg{
                            path{
                                stroke: ${(props.theme.colors.brand_high)};
                            }
                        }
                        &:last-child{
                            margin-right: 0;
                        }
                    }
                }
                .stone_card_expanded_td{
                    padding-left: 24px;
                    padding-right: 24px;
                    padding-top: 0;
                    padding-bottom: 0;
                    table{
                        thead{
                            tr{
                                th{
                                    padding-top: 16px;
                                    padding-bottom: 12px;
                                    border-bottom: 1px solid ${(props) => props?.theme?.line?.light};
                                    p{
                                        ${b2xs(props.theme.text.high, 500)}
                                    }
                                    &:first-child{
                                        padding-left: 0;
                                    }
                                    &:last-child{
                                        padding-right: 0;
                                        text-align: right;
                                    }
                                }
                            }
                        }
                        tbody{
                            tr{
                                td{
                                    background-color: transparent !important;
                                    border-bottom:0 !important;
                                    padding-top: 16px;
                                    padding-bottom: 16px;
                                    &:first-child{
                                        padding-left: 0;
                                    }
                                    &:last-child{
                                        padding-right: 0;
                                        text-align: right;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        `
    }

`