
export const stoneCardHead = [
  {
    label: 'Invoice number',
    key: 'invoice_no'
  },
  {
    label: 'Reference number',
    key: 'reference_number'
  },
  {
    label: 'Invoice date',
    key: 'invoice_date'
  },
  {
    label: 'Customer name',
    key: 'customer_name'
  },
  {
    label: 'SO number',
    key: 'so_number'
  },
  {
    label: 'Quantity',
    key: 'qty'
  },
  {
    label: 'Amount',
    key: 'total_amount'
  },
  {
    label: 'Settlement status',
    key: 'settlement_status'
  },
  {
    label: 'Action',
    key: 'action',
    class: 'action center'
  }
];


export const stoneInnerTableHead = [
  {
    label: 'Contract no',
    key: 'contact_no',
  },
  {
    label: 'Style no',
    key: 'style_no',
  },
  {
    label: 'Description',
    key: 'description',
  },
  {
    label: 'Dia/CS Stamping',
    key: 'cs_stamping',
  },
  {
    label: 'Dia Quality',
    key: 'dia_quality',
  },
  {
    label: 'Quantity',
    key: 'quantity',
  },
  {
    label: 'Rate R',
    key: 'rate_r',
  },
  {
    label: 'Amount R',
    key: 'amount_r',
  },
];


export const stoneInnerData = [
    {
        contractNo: 'MF/098522',
        styleNo: 'DWBSP1Q-1.00-107524',
        description: '',
        diaStamping: '',
        diaQuality: 'null, CUSDIA, NA',
        quantity: '1',
        rateR: '$2,300.00',
        amountR: '$1,943.00',
    },

]
