import { TableStatus, Skeleton } from "@dilpesh/kgk-ui-library";
import { useRouter } from "next/navigation";

export const getCustomersColumnDef = ({ isLoading }) => {
  const router = useRouter();

  return {
    name: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="140px" />
        ) : (
          <div
            className="icon_name_24_8_bes"
            onClick={() =>
              router.push(`/customer-insights/summary/${item.customer_code}`)
            }
          >
            <img src={item.nameIcon} alt="" />
            <p>{item.name}</p>
          </div>
        ),
    },
    group: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="140px" /> : <p>{item.group || "-"}</p>,
    },
    customerType: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="140px" />
        ) : (
          <p>{item.customer_type || "-"}</p>
        ),
    },
    customerCode: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="140px" />
        ) : (
          <p>{item.customer_code || "-"}</p>
        ),
    },
    salesman: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="140px" /> : <p>{item.salesman || "-"}</p>,
    },
    businessType: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="140px" />
        ) : (
          <p>{item.businessType || "-"}</p>
        ),
    },
    status: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="100px" />
        ) : (
          <TableStatus className={item.status.toLowerCase()}>
            {item.status}
          </TableStatus>
        ),
    },
    target: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="140px" /> : <p>{item.target || "-"}</p>,
    },
    fgSales: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="140px" /> : <p>{item.fg_sales || "-"}</p>,
    },
    rmSales: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="140px" /> : <p>{item.rm_sales || "-"}</p>,
    },
    total: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="140px" /> : <p>{item.total || "-"}</p>,
    },
    openOrder: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="140px" /> : <p>{item.openOrder || "-"}</p>,
    },
  };
};
