import { paths } from "@/modules/constants/constants";

export const assetsListingHead = [
  {
    label: "Name",
    key: "name",
  },
  {
    label: "Group",
    key: "group",
  },
  {
    label: "Customer type",
    key: "customerType",
  },
  {
    label: "Customer code",
    key: "customerCode",
  },
  {
    label: "Salesman",
    key: "salesman",
  },
  {
    label: "Business type",
    key: "businessType",
  },
  {
    label: "Status",
    key: "status",
  },
  {
    label: "Target",
    key: "target",
  },
  {
    label: "FG Sales",
    key: "fgSales",
  },
  {
    label: "RM Sales",
    key: "rmSales",
  },
  {
    label: "Total",
    key: "total",
  },
  {
    label: "Open order",
    key: "openOrder",
  },
];

export const assetsListingData = [
  {
    name: "Albert S. Smyth Co., Inc.-Annapolis",
    nameIcon: "/crm/assets/img/user.png",
    customerType: "Customer group",
    customerCode: "01EC2487",
    salesman: "<PERSON>",
    businessType: "Diamond",
    status: "Active",
    target: "77,815.00",
    fgSales: "22,322.00",
    rmSales: "15,365.00",
    total: "22,322.00",
    openOrder: "29,370.00",
    dropId: "1",
  },
  {
    name: "Albert S. Smyth Co., Inc.-Annapolis",
    nameIcon: "/crm/assets/img/user.png",
    customerType: "Customer group",
    customerCode: "01EC2487",
    salesman: "Angela Guenther",
    businessType: "Diamond",
    status: "Active",
    target: "77,815.00",
    fgSales: "22,322.00",
    rmSales: "15,365.00",
    total: "22,322.00",
    openOrder: "29,370.00",
    dropId: "1",
  },
  {
    name: "Albert S. Smyth Co., Inc.-Annapolis",
    nameIcon: "/crm/assets/img/user.png",
    customerType: "Customer group",
    customerCode: "01EC2487",
    salesman: "Angela Guenther",
    businessType: "Diamond",
    status: "Active",
    target: "77,815.00",
    fgSales: "22,322.00",
    rmSales: "15,365.00",
    total: "22,322.00",
    openOrder: "29,370.00",
    dropId: "1",
  },
  {
    name: "Albert S. Smyth Co., Inc.-Annapolis",
    nameIcon: "/crm/assets/img/user.png",
    customerType: "Customer group",
    customerCode: "01EC2487",
    salesman: "Angela Guenther",
    businessType: "Diamond",
    status: "Active",
    target: "77,815.00",
    fgSales: "22,322.00",
    rmSales: "15,365.00",
    total: "22,322.00",
    openOrder: "29,370.00",
    dropId: "1",
  },
  {
    name: "Albert S. Smyth Co., Inc.-Annapolis",
    nameIcon: "/crm/assets/img/user.png",
    customerType: "Customer group",
    customerCode: "01EC2487",
    salesman: "Angela Guenther",
    businessType: "Diamond",
    status: "Active",
    target: "77,815.00",
    fgSales: "22,322.00",
    rmSales: "15,365.00",
    total: "22,322.00",
    openOrder: "29,370.00",
    dropId: "1",
  },
  {
    name: "Albert S. Smyth Co., Inc.-Annapolis",
    nameIcon: "/crm/assets/img/user.png",
    customerType: "Customer group",
    customerCode: "01EC2487",
    salesman: "Angela Guenther",
    businessType: "Diamond",
    status: "Active",
    target: "77,815.00",
    fgSales: "22,322.00",
    rmSales: "15,365.00",
    total: "22,322.00",
    openOrder: "29,370.00",
    dropId: "1",
  },
];

export const mainActionList = [
  {
    key: 1,
    value: "Compare",
    class: "black",
  },
  {
    key: 4,
    value: "Reserve",
    class: "black",
  },
];

export const breadCrumbsValue = [
  {
    link: "Home",
    title: "Home",
  },
  {
    link: "CRM-portal",
    title: "CRM portal",
  },
  {
    link: "customer",
    title: "Customer",
  },
  {
    title: "Albert S. Smyth Co., Inc.-Annapolis",
  },
];

export const parentTabData: any = [
  {
    index: 0,
    label: "Summary",
    path: paths.ci_summary,
  },
  {
    index: 1,
    label: "Contacts",
    path: paths.ci_contacts,
  },
  {
    index: 2,
    label: "Address",
    path: paths.ci_address,
  },
  {
    index: 3,
    label: "Discussions",
    path: paths.ci_discussions,
  },
  {
    index: 4,
    label: "Orders",
    path: paths.ci_orders,
  },
  {
    index: 5,
    label: "Memo",
    path: paths.ci_memo,
  },
  {
    index: 6,
    label: "Sales",
    path: paths.ci_sales,
  },
  {
    index: 7,
    label: "Stock",
    path: paths.ci_stock,
  },
  {
    index: 8,
    label: "Items",
    path: paths.ci_items,
  },
  {
    index: 9,
    label: "Payment - Receipts",
    path: paths.ci_payment_receipts,
  },
  {
    index: 10,
    label: "Package log",
    path: paths.ci_package_log,
  },
  {
    index: 11,
    label: "Other",
    path: paths.ci_other,
  },
  {
    index: 12,
    label: "PDC",
    path: paths.ci_pdc,
  },
  {
    index: 13,
    label: "Proposal",
    path: paths.ci_proposal,
  },
  {
    index: 14,
    label: "Diamond chart",
    path: paths.ci_diamond_chart,
  },
  {
    index: 15,
    label: "Gemstone chart",
    path: paths.ci_gemstone_chart,
  },
  {
    index: 16,
    label: "Accessories",
    path: paths.ci_accessories,
  },
  {
    index: 17,
    label: "Labour",
    path: paths.ci_labour,
  },
  {
    index: 18,
    label: "Exclusive",
    path: paths.ci_exclusive,
  },
];
