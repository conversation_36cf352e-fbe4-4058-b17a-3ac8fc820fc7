"use client";

import { FilterLineIcon } from "@/assets/icons/collection/FilterLineIcon";
import { PlusIcon } from "@/assets/icons/collection/PlusIcon";
import {
  DataTable,
  FilterDropdown,
  TableFooter,
  TableHeader,
} from "@dilpesh/kgk-ui-library";
import {
  useGetDiscussionFiltersQuery,
  useGetDiscussionList,
  useGetTranslation,
  useModal,
  usePagination,
  usePayloadFilter,
} from "@magneto-it-solutions/kgk-common-library";
import { useParams } from "next/navigation";
import { useRef, useState } from "react";
import CustomerInsightsAddDiscussionsModal from "../modal/add-discussions-modal/add-discussions-modal";
import { assetsListingHead, mainActionList } from "./utils/constant";
import { CustomerInsightsDiscussionsstyle } from "./utils/style";
import { getDiscussionsColumnDef } from "./utils/utility";

const CustomerInsightsDiscussions = () => {
  const { translation } = useGetTranslation();
  const params = useParams();
  const id: string = String(params?.id);

  const {
    selectedFilter,
    search,
    changeSearch,
    appliedFilters,
    clearFilters,
    applyFilters,
    handleFilterChange,
  } = usePayloadFilter("discussion-list");

  const { page, limit, view, changeLimit, changePage } =
    usePagination("discussion-list");

  const { discussions, isLoading } = useGetDiscussionList(id);

  const { data: discussionFilter } = useGetDiscussionFiltersQuery({ customer_code: id });

  const addressRef = useRef<any>(null);

  const { open: addDiscussionModalOpen } = useModal(null, "custom-modal");

  const [filterStatus, setFilterStatus] = useState<boolean>(false);

  const coloumnDefListing = getDiscussionsColumnDef({ isLoading });

  const onAddDiscuss = async (id: string) => {
    addDiscussionModalOpen({
      title: "Add discussion",
      width: 416,
      wrapClassName: "detail_modal",
      approveBtnText: "Save",
      body: <CustomerInsightsAddDiscussionsModal ref={addressRef} />,
      approveBtnEvent: () => {
        if (addressRef.current) {
          addressRef.current.submit();
        }
      },
    });
  };

  return (
    <>
      <CustomerInsightsDiscussionsstyle>
        <div className="container first_view_content">
          <div className="table_main_wpr">
            <TableHeader
              isSearch={true}
              skeleton={isLoading}
              searchPlaceholder="Search discussions"
              borderBtnText={translation?.filter}
              borderBtnIcon={<FilterLineIcon />}
              searchText={search}
              onSearchChange={(e: any) =>
                changeSearch(e.target.value.toLowerCase())
              }
              borderBtnClick={() => setFilterStatus(!filterStatus)}
              actionList={mainActionList}
              fillBtnText={translation.add}
              fillBtnClick={onAddDiscuss}
              fillBtnIcon={<PlusIcon />}
            />
            <FilterDropdown
              open={filterStatus}
              close={() => setFilterStatus(!filterStatus)}
              data={discussionFilter?.filters}
              onChangeFilter={(filter, value) => {
                handleFilterChange(filter, value);
              }}
              selectedFilter={selectedFilter}
              onApplyFilter={applyFilters}
              onClearFilter={clearFilters}
              fetchOptions={(_, searchText) => { }}
              clearAllBtnText={translation.ClearAll}
              applyBtntext={translation.Apply}
              filterText={translation.Filter}
            />
            <DataTable
              isMultiselect={true}
              column={assetsListingHead}
              coloumnDef={coloumnDefListing}
              data={discussions?.data}
              skeleton={isLoading}
              bodyClass="body_pt_12"
              fixedPosition="right"
            />
            <TableFooter
              skeleton={isLoading}
              currentPageSize={limit}
              currentPage={page}
              recordsTotal={discussions?.filteredRecords}
              setCurrentPage={changePage}
              setCurrentPageSize={changeLimit}
              showingText={translation.Showing}
              showLabel={translation.Show}
              OfText={translation.Of}
            />
          </div>
        </div>
      </CustomerInsightsDiscussionsstyle>
    </>
  );
};

export default CustomerInsightsDiscussions;
