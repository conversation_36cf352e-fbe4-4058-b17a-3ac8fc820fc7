"use client"
import { FilterLineIcon } from "@/assets/icons/collection/FilterLineIcon";
import { BtnGroup, DataTable, FilterDropdown, TableFooter, TableHeader, TabletFilterBtn } from "@dilpesh/kgk-ui-library";
import { useGetLabourList, useGetTranslation, usePagination, usePayloadFilter } from "@magneto-it-solutions/kgk-common-library";
import { useParams } from "next/navigation";
import { useState } from "react";
import SearchLabour from "./search-labour/search-labour";
import { assetsListingHead, assetsListingHead2, dataitem } from "./utils/constant";
import { CustomerInsightsLabourstyle } from "./utils/style";
import { getLabourMetalColumnDef, getLabourStoneColumnDef } from "./utils/utility";


const CustomerInsightsLabour = () => {
  const { translation } = useGetTranslation();
  const params = useParams();
  const id: string = String(params?.id);

  const {
    selectedFilter,
    search,
    changeSearch,
    appliedFilters,
    clearFilters,
    applyFilters,
    handleFilterChange,
  } = usePayloadFilter("labour-list");

  const { page, limit, view, changeLimit, changePage } =
    usePagination("labour-list");

  const [btnGroupActive, setBtnGroupActive] = useState(dataitem[0].value);
  const [filterStatus, setFilterStatus] = useState<boolean>(false);

  const { labourList, isLoading } = useGetLabourList(id, dataitem[btnGroupActive].content.toLocaleLowerCase() as "metal" | "stone");
  // const { data: pdcFilter } = useGetPDCFiltersQuery({ customer_code: id });

  const [filterOpen, setFilterOpen] = useState(false);

  const coloumnStoneDefListing = getLabourStoneColumnDef({ isLoading });
  const coloumnMetalDefListing = getLabourMetalColumnDef({ isLoading });

  return (
    <CustomerInsightsLabourstyle>
      <div className="container first_view_content">
        <TabletFilterBtn
          filter={true}
          filterTitle="Filter labour"
          filterClick={() => { setFilterOpen(true) }}
        />
        <div className={`top_filter ${filterOpen ? 'active' : ''}`}>
          <div className="this_container">
            <div className="filter_mobile_header">
              <p>Filter labour</p>
              <div className="close_icon" onClick={() => { setFilterOpen(false) }}>
                <svg viewBox="0 0 24 24" fill="none">
                  <path d="M18 6L6 18M6 6L18 18" stroke="#8C8C8C" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
              </div>
            </div>
            <SearchLabour />

          </div>
        </div>
        <div
          className={`filter_backdrop_layer ${filterOpen ? 'active' : ''}`}
          onClick={() => { setFilterOpen(false) }}>
        </div>
        <div className="table_main_wpr">
          <div className="multi_btn_wpr">
            <BtnGroup data={dataitem}
              value={btnGroupActive}
              onChange={(e: any) => setBtnGroupActive(e.target.value)}
            />
          </div>
          <TableHeader
            isSearch={true}
            skeleton={isLoading}
            searchPlaceholder={translation?.search_customer_code}
            borderBtnText={translation?.filter}
            borderBtnIcon={<FilterLineIcon />}
            searchText={search}
            onSearchChange={(e: any) =>
              changeSearch(e.target.value.toLowerCase())
            }
            borderBtnClick={() => setFilterStatus(!filterStatus)}
          />
          <FilterDropdown
            open={filterStatus}
            close={() => setFilterStatus(!filterStatus)}
            // data={memoFilter?.filters}
            onChangeFilter={(filter, value) => {
              handleFilterChange(filter, value);
            }}
            selectedFilter={selectedFilter}
            onApplyFilter={applyFilters}
            fetchOptions={(_, searchText) => { }}
            clearAllBtnText={translation.ClearAll}
            applyBtntext={translation.Apply}
            filterText={translation.Filter}
          />
          {btnGroupActive === "0" &&
            <DataTable
              isMultiselect={false}
              column={assetsListingHead}
              coloumnDef={coloumnStoneDefListing}
              data={labourList?.data}
              skeleton={isLoading}
            />
          }
          {btnGroupActive === "1" &&
            <DataTable
              isMultiselect={false}
              column={assetsListingHead2}
              coloumnDef={coloumnMetalDefListing}
              data={labourList?.data}
              skeleton={isLoading}
            />
          }
          <TableFooter
            skeleton={isLoading}
            currentPageSize={limit}
            currentPage={page}
            recordsTotal={labourList?.filteredRecords}
            setCurrentPage={changePage}
            setCurrentPageSize={changeLimit}
            showingText={translation.Showing}
            showLabel={translation.Show}
            OfText={translation.Of}
          />
        </div>
      </div>
    </CustomerInsightsLabourstyle>
  )
}

export default CustomerInsightsLabour
