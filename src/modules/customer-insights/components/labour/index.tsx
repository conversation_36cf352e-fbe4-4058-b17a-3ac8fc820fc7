"use client"
import { FilterLineIcon } from "@/assets/icons/collection/FilterLineIcon";
import { BtnGroup, DataTable, TableFooter, TableHeader, TabletFilterBtn } from "@dilpesh/kgk-ui-library";
import { useGetLabourList, useGetTranslation, usePagination, usePayloadFilter } from "@magneto-it-solutions/kgk-common-library";
import { useParams } from "next/navigation";
import { useState } from "react";
import SearchLabour from "./search-labour/search-labour";
import { assetsListingData, assetsListingData2, assetsListingHead, assetsListingHead2, dataitem } from "./utils/constant";
import { CustomerInsightsLabourstyle } from "./utils/style";


const CustomerInsightsLabour = () => {
  const { translation } = useGetTranslation();
  const params = useParams();
  const id: string = String(params?.id);

  const {
    selectedFilter,
    search,
    changeSearch,
    appliedFilters,
    clearFilters,
    applyFilters,
    handleFilterChange,
  } = usePayloadFilter("labour-list");

  const { page, limit, view, changeLimit, changePage } =
    usePagination("labour-list");

  const { labourList, isLoading } = useGetLabourList(id);
  // const { data: pdcFilter } = useGetPDCFiltersQuery({ customer_code: id });

  const [filterStatus, setFilterStatus] = useState<boolean>(false);


  const [filterOpen, setFilterOpen] = useState(false);


  const coloumnDefAssetsListing: Object = {
    updatedBy: {
      renderChildren: (item: any) => {
        return (
          <div className='icon_name_24_8_bes'>
            <img src={item.updatedByIcon} alt="" />
            <p>{item.updatedBy}</p>
          </div>
        );
      },
    },
  };



  const coloumnDefAssetsListing2: Object = {
    updatedBy: {
      renderChildren: (item: any) => {
        return (
          <div className='icon_name_24_8_bes'>
            <img src={item.updatedByIcon} alt="" />
            <p>{item.updatedBy}</p>
          </div>
        );
      },
    },
  };


  const [currentPage, setCurrentPage] = useState(1);
  const [currentPageSize, setCurrentPageSize] = useState(25);
  const [recordsTotal, setRecordsTotal] = useState(500);




  const [btnGroupActive, setBtnGroupActive] = useState(dataitem[0].value);

  return (
    <CustomerInsightsLabourstyle>
      <div className="container first_view_content">
        <TabletFilterBtn
          filter={true}
          filterTitle="Filter labour"
          filterClick={() => { setFilterOpen(true) }}
        />
        <div className={`top_filter ${filterOpen ? 'active' : ''}`}>
          <div className="this_container">
            <div className="filter_mobile_header">
              <p>Filter labour</p>
              <div className="close_icon" onClick={() => { setFilterOpen(false) }}>
                <svg viewBox="0 0 24 24" fill="none">
                  <path d="M18 6L6 18M6 6L18 18" stroke="#8C8C8C" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
              </div>
            </div>
            <SearchLabour />

          </div>
        </div>
        <div
          className={`filter_backdrop_layer ${filterOpen ? 'active' : ''}`}
          onClick={() => { setFilterOpen(false) }}>
        </div>
        <div className="table_main_wpr">
          <div className="multi_btn_wpr">
            <BtnGroup data={dataitem}
              value={btnGroupActive}
              onChange={(e: any) => setBtnGroupActive(e.target.value)}
            />
          </div>
          <TableHeader
            searchPlaceholder="Search customer code"
            borderBtnText="Filter"
            borderBtnIcon={<FilterLineIcon />}
            isSearch={true}
          />
          {btnGroupActive === "0" &&
            <DataTable
              isMultiselect={false}
              column={assetsListingHead}
              coloumnDef={coloumnDefAssetsListing}
              data={assetsListingData}
            />
          }
          {btnGroupActive === "1" &&
            <DataTable
              isMultiselect={false}
              column={assetsListingHead2}
              coloumnDef={coloumnDefAssetsListing2}
              data={assetsListingData2}
            />
          }
          <TableFooter
            skeleton={isLoading}
            currentPageSize={limit}
            currentPage={page}
            recordsTotal={labourList?.filteredRecords}
            setCurrentPage={changePage}
            setCurrentPageSize={changeLimit}
            showingText={translation.Showing}
            showLabel={translation.Show}
            OfText={translation.Of}
          />
        </div>
      </div>
    </CustomerInsightsLabourstyle>
  )
}

export default CustomerInsightsLabour
