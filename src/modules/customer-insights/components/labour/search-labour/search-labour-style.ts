import { Media, b2xs, bs } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const SearchLabourStyle = styled.div`
  ${(props) => css`
    .scrollable_content_wpr {
      @media ${Media.desktop} {
        padding-bottom: 32px;
        overflow-y: auto;
        overflow-x: hidden;
      }
      @media ${Media.ultra_mobile} {
        padding-bottom: 24px;
      }
    }
    .top_select_wpr {
      .left_select_wpr {
        .ant-row {
          margin-bottom: -24px;
          & > * {
            margin-bottom: 24px;
          }
        }
      }
    }

    .inp_top_wpr {
      margin-top: 24px;
      .search_btn_wpr {
        padding-top: 51px;
        @media ${Media.below_1399} {
          padding-top: 47px;
        }
        @media ${Media.tablet} {
          padding-top: 24px;
        }
        .fill-btn {
          width: 120px;
          @media ${Media.desktop} {
            width: 100%;
          }
        }
      }
      .valid_lable_wpr {
        & > p {
          ${bs(props?.theme?.text?.high, 500)};
          margin-bottom: 16px;
        }
      }
    }
  `}
`;
