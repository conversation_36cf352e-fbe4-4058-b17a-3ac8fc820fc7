import React from 'react'
import { Btn, InputGroups, SelectGroups } from "@dilpesh/kgk-ui-library";
import { Col, Row } from "antd";
import { SearchLabourStyle } from './search-labour-style';
const SearchLabour = () => {
    return (
        <SearchLabourStyle>
            <div className="scrollable_content_wpr">
                <div className="top_select_wpr">
                    <div className="left_select_wpr">
                        <Row gutter={{ md: 24 }}>
                            <Col md={12} lg={6}>
                                <InputGroups label="Labour" />
                            </Col>
                            <Col md={12} lg={6}>
                                <SelectGroups label="Factory code" />
                            </Col>
                            <Col md={12} lg={6}>
                                <SelectGroups label="Currency code" />
                            </Col>
                            <Col md={12} lg={6}>
                                <SelectGroups label="Chart based on" />
                            </Col>
                        </Row>
                    </div>
                </div>
                <div className="inp_top_wpr">
                    <Row gutter={{ md: 24 }}>
                        <Col md={24} lg={20} xl={12}>
                            <Row gutter={{ md: 24 }}>
                                <Col md={12}>
                                    <div className="valid_lable_wpr">
                                        <p>CTC/Weight</p>
                                        <Row gutter={{ md: 16 }}>
                                            <Col md={12}>
                                                <InputGroups label="Minimum" />
                                            </Col>
                                            <Col md={12}>
                                                <InputGroups label="Maximum" />
                                            </Col>
                                        </Row>
                                    </div>
                                </Col>
                                <Col md={12}>
                                    <div className="valid_lable_wpr">
                                        <p>Labour SP rate</p>
                                        <Row gutter={{ md: 16 }}>
                                            <Col md={12}>
                                                <InputGroups label="Rate" />
                                            </Col>
                                            <Col md={12}>
                                                <InputGroups label="Minimum SP" />
                                            </Col>
                                        </Row>
                                    </div>
                                </Col>
                            </Row>
                        </Col>
                        <Col md={24} lg={4} xl={12}>
                            <div className="search_btn_wpr">
                                <Btn bg="fill" size="large">Search</Btn>
                            </div>
                        </Col>
                    </Row>
                </div>
            </div>
        </SearchLabourStyle>
    )
}

export default SearchLabour
