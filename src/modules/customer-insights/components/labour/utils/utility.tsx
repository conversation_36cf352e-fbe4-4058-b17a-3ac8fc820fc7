import { Skeleton } from "@dilpesh/kgk-ui-library";
import { getSessionItem, SESSION_KEYS } from "@magneto-it-solutions/kgk-common-library";
import { format } from "date-fns";

export const getLabourStoneColumnDef = ({ isLoading }) => {
  return {
    rmType: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : <p>{item?.rmType || "-"}</p>,
    },
    commodity: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : <p>{item?.stone_setting_labour_chart?.Commodity || "-"}</p>,
    },
    setting: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : <p>{item?.stone_setting_labour_chart?.SettingMethod || "-"}</p>,
    },
    type: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : <p>{item?.stone_setting_labour_chart?.SettingType || "-"}</p>,
    },
    min: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="60px" /> : <p>{item?.stone_setting_labour_chart?.MinWeight || "-"}</p>,
    },
    max: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="60px" /> : <p>{item?.stone_setting_labour_chart?.MaxWeight || "-"}</p>,
    },
    labSPRate: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="80px" /> : <p>{item?.stone_setting_labour_chart?.LabourPrice || "-"}</p>,
    },
    updatedOn: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="120px" /> : <p>{format(
          new Date(item?.updatedAt),
          getSessionItem(SESSION_KEYS.DATE_FORMAT) ||
          "dd MMM yyyy, h:mm a"
        ) || "-"}</p>,
    },
    updatedBy: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="120px" />
        ) : (
          <div className="icon_name_24_8_bes">
            <img src={item.updatedByIcon} alt="" />
            <p>{item.updated_by || "-"}</p>
          </div>
        ),
    },
  };
};


export const getLabourMetalColumnDef = ({ isLoading }) => {
  return {
    labType: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : <p>{item?.metal_labour_chart?.labType || "-"}</p>,
    },
    commodity: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : <p>{item?.metal_labour_chart?.Commodity || "-"}</p>,
    },
    jewelType: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : <p>{item?.metal_labour_chart?.JewelleryType || "-"}</p>,
    },
    calOn: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : <p>{item?.metal_labour_chart?.calOn || "-"}</p>,
    },
    min: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : <p>{item?.metal_labour_chart?.min || "-"}</p>,
    },
    isSPL: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : <p>{item?.metal_labour_chart?.isSPL || "-"}</p>,
    },
    spRate: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : <p>{item?.metal_labour_chart?.spRate || "-"}</p>,
    },
    minSP: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : <p>{item?.metal_labour_chart?.MinSellingPrice || "-"}</p>,
    },
    updatedOn: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : <p>{format(
          new Date(item?.updatedAt),
          getSessionItem(SESSION_KEYS.DATE_FORMAT) ||
          "dd MMM yyyy, h:mm a"
        ) || "-"}</p>,
    },
    updatedBy: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="120px" />
        ) : (
          <div className="icon_name_24_8_bes">
            <img src={item.updatedByIcon} alt="" />
            <p>{item.updated_by || "-"}</p>
          </div>
        ),
    },
  }
} 