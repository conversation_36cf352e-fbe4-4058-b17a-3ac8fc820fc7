import { ActionIcon } from "@/assets/icons";
import { ActionDropdown, Skeleton } from "@dilpesh/kgk-ui-library";
import {
  getSessionItem,
  SESSION_KEYS,
} from "@magneto-it-solutions/kgk-common-library";
import { format } from "date-fns";

export const getDiscussionsColumnDef = ({ isLoading }) => {
  return {
    discussion: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="140px" /> : <p>{item?.comments || "-"}</p>,
    },
    postedBy: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="140px" />
        ) : (
          <div className="icon_name_24_8_bes">
            <img src={item?.postedByIcon} alt="" />
            <p>{item?.postedBy || "-"}</p>
          </div>
        ),
    },
    postedOn: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="140px" />
        ) : (
          <p>
            {item?.created_on
              ? format(
                  new Date(item.created_on),
                  getSessionItem(SESSION_KEYS.DATE_FORMAT) ||
                    "dd MMM yyyy, h:mm a"
                )
              : "-"}
          </p>
        ),
    },
    action: {
      renderChildren: (_item: any) => {
        const items = {
          dropId: "super-action",
        };

        const actionitemList = [
          { key: 1, value: "Edit", class: "black" },
          { key: 2, value: "Remove", class: "red" },
        ];

        return (
          <div className="action_wpr">
            <ActionDropdown
              items={items}
              actionList={actionitemList}
              actionIcon={<ActionIcon />}
              className="mx-auto"
            />
          </div>
        );
      },
    },
  };
};
