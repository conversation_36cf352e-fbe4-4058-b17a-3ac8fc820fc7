export const assetsListingHead = [
    {
        label: 'RM type',
        key: 'rmType'
    },
    {
        label: 'Commodity',
        key: 'commodity'
    },
    {
        label: 'Setting',
        key: 'setting',
    },
    {
        label: 'Type',
        key: 'type',
    },
    {
        label: 'Min',
        key: 'min',
    },
    {
        label: 'Max',
        key: 'max'
    },
    {
        label: 'Lab SP rate',
        key: 'labSPRate'
    },
    {
        label: 'Updated by',
        key: 'updatedBy'
    },
    {
        label: 'Updated on',
        key: 'updatedOn'
    },
];

export const assetsListingData = [
    {
        rmType: 'Precious color stone',
        commodity: 'Emerald',
        setting: 'Hand',
        type: 'Prong',
        min: '0.001',
        max: '0.009',
        labSPRate: '0.60',
        updatedByIcon: 'https://encrypted-tbn1.gstatic.com/images?q=tbn:ANd9GcTSsp8ZcUG7qn6RccmY8zBJN3aYRbo-zY12UJbvLfncoQM_baKp',
        updatedBy: '<PERSON>',
        updatedOn: '23 Mar, 2023 - 10:00 PM',

    },
    {
        rmType: 'Precious color stone',
        commodity: 'Emerald',
        setting: 'Hand',
        type: 'Prong',
        min: '0.001',
        max: '0.009',
        labSPRate: '0.60',
        updatedByIcon: 'https://encrypted-tbn1.gstatic.com/images?q=tbn:ANd9GcTSsp8ZcUG7qn6RccmY8zBJN3aYRbo-zY12UJbvLfncoQM_baKp',
        updatedBy: 'Andrew Lewis',
        updatedOn: '23 Mar, 2023 - 10:00 PM',

    },
    {
        rmType: 'Precious color stone',
        commodity: 'Emerald',
        setting: 'Hand',
        type: 'Prong',
        min: '0.001',
        max: '0.009',
        labSPRate: '0.60',
        updatedByIcon: 'https://encrypted-tbn1.gstatic.com/images?q=tbn:ANd9GcTSsp8ZcUG7qn6RccmY8zBJN3aYRbo-zY12UJbvLfncoQM_baKp',
        updatedBy: 'Andrew Lewis',
        updatedOn: '23 Mar, 2023 - 10:00 PM',

    },
    {
        rmType: 'Precious color stone',
        commodity: 'Emerald',
        setting: 'Hand',
        type: 'Prong',
        min: '0.001',
        max: '0.009',
        labSPRate: '0.60',
        updatedByIcon: 'https://encrypted-tbn1.gstatic.com/images?q=tbn:ANd9GcTSsp8ZcUG7qn6RccmY8zBJN3aYRbo-zY12UJbvLfncoQM_baKp',
        updatedBy: 'Andrew Lewis',
        updatedOn: '23 Mar, 2023 - 10:00 PM',

    },
    {
        rmType: 'Precious color stone',
        commodity: 'Emerald',
        setting: 'Hand',
        type: 'Prong',
        min: '0.001',
        max: '0.009',
        labSPRate: '0.60',
        updatedByIcon: 'https://encrypted-tbn1.gstatic.com/images?q=tbn:ANd9GcTSsp8ZcUG7qn6RccmY8zBJN3aYRbo-zY12UJbvLfncoQM_baKp',
        updatedBy: 'Andrew Lewis',
        updatedOn: '23 Mar, 2023 - 10:00 PM',

    },
];

export const assetsListingHead2 = [
    {
        label: 'Lab type',
        key: 'labType'
    },
    {
        label: 'Commodity',
        key: 'commodity'
    },
    {
        label: 'Jewel type',
        key: 'jewelType',
    },
    {
        label: 'Cal. on',
        key: 'calOn',
    },
    {
        label: 'Min',
        key: 'min',
    },
    {
        label: 'IS SPL',
        key: 'isSPL'
    },
    {
        label: 'SP rate',
        key: 'spRate'
    },
    {
        label: 'Min SP',
        key: 'minSP'
    },
    {
        label: 'Updated by',
        key: 'updatedBy'
    },
    {
        label: 'Updated on',
        key: 'updatedOn'
    },
];

export const assetsListingData2 = [
    {
        labType: 'Two tone',
        commodity: 'Gold',
        jewelType: 'Necklace',
        calOn: 'Flat',
        min: '0.001',
        isSPL: 'No',
        spRate: '3.60',
        minSP: '3.60',
        updatedByIcon: 'https://encrypted-tbn1.gstatic.com/images?q=tbn:ANd9GcTSsp8ZcUG7qn6RccmY8zBJN3aYRbo-zY12UJbvLfncoQM_baKp',
        updatedBy: 'Andrew Lewis',
        updatedOn: '23 Mar, 2023 - 10:00 PM',

    },
    {
        labType: 'Two tone',
        commodity: 'Gold',
        jewelType: 'Necklace',
        calOn: 'Flat',
        min: '0.001',
        isSPL: 'No',
        spRate: '3.60',
        minSP: '3.60',
        updatedByIcon: 'https://encrypted-tbn1.gstatic.com/images?q=tbn:ANd9GcTSsp8ZcUG7qn6RccmY8zBJN3aYRbo-zY12UJbvLfncoQM_baKp',
        updatedBy: 'Andrew Lewis',
        updatedOn: '23 Mar, 2023 - 10:00 PM',

    },
    {
        labType: 'Two tone',
        commodity: 'Gold',
        jewelType: 'Necklace',
        calOn: 'Flat',
        min: '0.001',
        isSPL: 'No',
        spRate: '3.60',
        minSP: '3.60',
        updatedByIcon: 'https://encrypted-tbn1.gstatic.com/images?q=tbn:ANd9GcTSsp8ZcUG7qn6RccmY8zBJN3aYRbo-zY12UJbvLfncoQM_baKp',
        updatedBy: 'Andrew Lewis',
        updatedOn: '23 Mar, 2023 - 10:00 PM',

    },
    {
        labType: 'Two tone',
        commodity: 'Gold',
        jewelType: 'Necklace',
        calOn: 'Flat',
        min: '0.001',
        isSPL: 'No',
        spRate: '3.60',
        minSP: '3.60',
        updatedByIcon: 'https://encrypted-tbn1.gstatic.com/images?q=tbn:ANd9GcTSsp8ZcUG7qn6RccmY8zBJN3aYRbo-zY12UJbvLfncoQM_baKp',
        updatedBy: 'Andrew Lewis',
        updatedOn: '23 Mar, 2023 - 10:00 PM',

    },

];

export const dataitem = [
    {
        value: "0",
        content: "Stone"
    },
    {
        value: "1",
        content: "Metal"
    },
];