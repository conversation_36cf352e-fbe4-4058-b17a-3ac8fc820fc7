interface ILabValue {
    label:string,
	value:string 
}

export const customerProfile :ILabValue[] = [
    {
        label:"Account created on",
        value:"15 Jan, 2019"
    },
    {
        label:"Salesman",
        value:"<PERSON>"
    },
    {
        label:"Payment term",
        value:"6% 30, Net 60 days"
    },
    {
        label:"Customer type",
        value:"Retailer"
    },
    {
        label:"Discount %",
        value:"No"
    },
    {
        label:"Projection Amount",
        value:"0"
    },
    {
        label:"Special requirements",
        value:"77815"
    },
    {
        label:"Last stock order date",
        value:"6% 30, Net 60 days"
    },
    {
        label:"Payment term",
        value:"15 May 2023"
    },
    {
        label:"Last special order date",
        value:"15 Jun 2023"
    },
   
    
]

export const programInformation : ILabValue [] = [
    {
        label:"Buyer group",
        value:"01EC6431"
    },
    {
        label:"Authorised retailer",
        value:"Yes"
    },
    {
        label:"FM customer",
        value:"No"
    },
    {
        label:"Programs enrolled",
        value:""
    },
    {
        label:"Co-op Applicable",
        value:"No"
    },
    {
        label:"Prototype applicable",
        value:""
    },
    {
        label:"Center CZ",
        value:"0"
    },
    {
        label:"No. of stores",
        value:"0"
    },
    
    
]