import React from "react";
import { Col, Row } from "antd";
import { CustomerProfileStyle } from "./customer-profile-style";
const CustomerProfile = (props) => {
  return (
    <>
      <Col md={12} xl={8}>
        <CustomerProfileStyle>
          <div className="customer_profile_table">
            <table>
              <thead>
                <tr>
                  <th>Customer profile</th>
                  <th></th>
                </tr>
              </thead>
              <tbody>
                {props.data?.map((item: any) => (
                  <tr>
                    <td>{item?.label}</td>
                    <td>{item?.value}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CustomerProfileStyle>
      </Col>
    </>
  );
};

export default CustomerProfile;
