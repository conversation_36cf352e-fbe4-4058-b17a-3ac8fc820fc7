import { Media } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const CustomerProfileDetailsStyle = styled.div`
  ${(props) => css`
    .customer_profile_wpr {
      margin-bottom: 64px;
      @media ${Media.desktop} {
        margin-bottom: 52px;
      }
      @media ${Media.tablet} {
        margin-bottom: 48px;
      }
      .ant-row {
        margin-bottom: -32px;
        @media ${Media.tablet} {
          margin-bottom: -24px;
        }
        & > * {
          margin-bottom: 32px;
          @media ${Media.tablet} {
            margin-bottom: 24px;
          }
        }
      }
    }
  `}
`;
