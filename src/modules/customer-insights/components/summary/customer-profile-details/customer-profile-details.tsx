import { Row } from "antd";
import CustomerMemo from "../customer-memo/customer-memo";
import CustomerProfile from "../customer-profile/customer-profile";
import { CustomerProfileDetailsStyle } from "./customer-profile-details-style";

const CustomerProfileDetails = (props: { summary: any }) => {
  const customerProfile = [
    {
      label: "Account created on",
      value: `${props.summary?.account_created_on
        ? props.summary?.account_created_on
        : "-"
        }`,
    },
    {
      label: "Salesman",
      value: `${props.summary?.Salesman ? "-" : "-"}`,
    },
    {
      label: "Payment term",
      value: `${props.summary?.payment_term ? props.summary?.payment_term : "-"
        }`,
    },
    {
      label: "Customer type",
      value: `${props.summary?.customer_type
        ? props.summary?.customer_type
        : "-"
        }`,
    },
    {
      label: "Discount %",
      value: `${props.summary?.discount_percentage
        ? props.summary?.discount_percentage + "%"
        : "-"
        }`,
    },
    {
      label: "Projection Amount",
      value: `${props.summary?.projection_amount ? props.summary?.projection_amount : "-"
        }`,
    },
    {
      label: "Special requirements",
      value: `${props.summary?.special_requirements
        ? props.summary?.special_requirements
        : "-"
        }`,
    },
    {
      label: "Last stock order date",
      value: `${props.summary?.last_stock_order_date
        ? props.summary?.last_stock_order_date
        : "-"
        }`,
    },
    {
      label: "Payment term",
      value: `${props.summary?.payment_term ? props.summary?.payment_term : "-"
        }`,
    },
    {
      label: "Last special order date",
      value: `${props.summary?.last_special_order_date
        ? props.summary?.last_special_order_date
        : "-"
        }`,
    },
  ];

  const programInformation = [
    {
      label: "Buyer group",
      value: `${props.summary?.buyer_group ? props.summary?.buyer_group : "-"}`,
    },
    {
      label: "Authorised retailer",
      value: `${props.summary?.authorized_retailer
        ? props.summary?.authorized_retailer
        : "-"
        }`,
    },
    {
      label: "FM customer",
      value: `${props.summary?.fm_customer ? props.summary?.fm_customer : "-"}`,
    },
    {
      label: "Programs enrolled",
      value: `${props.summary?.programs_enrolled
        ? props.summary?.programs_enrolled
        : "-"
        }`,
    },
    {
      label: "Co-op Applicable",
      value: `${props.summary?.coop_applicable ? props.summary?.coop_applicable : "-"
        }`,
    },
    {
      label: "Prototype applicable",
      value: `${props.summary?.prototype_applicable
        ? props.summary?.prototype_applicable
        : "-"
        }`,
    },
    {
      label: "Center CZ",
      value: `${props.summary?.center_cz ? props.summary?.center_cz : "-"}`,
    },
    {
      label: "No. of stores",
      value: `${props.summary?.no_of_stores
        ? props.summary?.no_of_stores
        : "-"
        }`,
    },
  ];

  return (
    <CustomerProfileDetailsStyle>
      <div className="customer_profile_wpr">
        <Row gutter={{ md: 24, lg: 32, xl: 52, xxl: 72 }}>
          <CustomerProfile data={customerProfile} />
          <CustomerProfile data={programInformation} />
          <CustomerMemo />
        </Row>
      </div>
    </CustomerProfileDetailsStyle>
  );
};

export default CustomerProfileDetails;
