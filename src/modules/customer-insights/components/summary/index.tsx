import UserProfile from "./user-profile/user-profile";
import CustomerProfileDetails from "./customer-profile-details/customer-profile-details";
import AccountSummary from "./account-summary/account-summary";
import AddressContact from "./address-contact/address-contact";
import { useParams } from "next/navigation";
import { useGetCustomerSummary } from "@magneto-it-solutions/kgk-common-library";

const SalesType: {
  label: string;
  value1: string;
  value2: string;
  value3: string;
}[] = [
  {
    label: "FG",
    value1: "24,808.00",
    value2: "56,349.00",
    value3: "43,163.00",
  },
  {
    label: "Total sales",
    value1: "24,808.00",
    value2: "56,349.00",
    value3: "43,163.00",
  },
  {
    label: "Sales target",
    value1: "24,808.00",
    value2: "56,349.00",
    value3: "43,163.00",
  },
];

interface IbillAddess {
  AddressType: string;
  Address: string;
  ContactName: string;
  Phone: string;
  Mobile: string;
  Email: string;
  DefaultAddress: string;
}

const BillAddress: IbillAddess[] = [
  {
    AddressType: "Billing address",
    Address: "431 School House Road, Georgetown, KY 40324",
    ContactName: "Michael Lee",
    Phone: "****** 555 0120",
    Mobile: "",
    Email: "<EMAIL>",
    DefaultAddress: "Yes",
  },
  {
    AddressType: "Billing address",
    Address: "431 School House Road, Georgetown, KY 40324",
    ContactName: "Michael Lee",
    Phone: "****** 555 0120",
    Mobile: "",
    Email: "<EMAIL>",
    DefaultAddress: "Yes",
  },
];

function CustomerInsightsSummary() {
  const params = useParams();
  const id = params?.id;

  const { summary, isLoading } = useGetCustomerSummary(id as string);

  const getValueOfOrder = (label: string) => {
    const item = summary?.open_order.find(
      (d) => d.Label === label.toUpperCase()
    );
    return item?.Value || "-";
  };

  const total = summary?.open_order.reduce((sum, item) => {
    const val = parseFloat(item.Value);
    return sum + (isNaN(val) ? 0 : val);
  }, 0);

  const Order = [
    {
      label: "Others",
      value: getValueOfOrder("OTHERS"),
    },
    {
      label: "Special",
      value: getValueOfOrder("SPECIAL"),
    },
    {
      label: "Stock",
      value: getValueOfOrder("STOCK"),
    },
    {
      label: "Total",
      value: total,
    },
  ];

  const Memo = [
    {
      label: "FG",
      value: `${summary?.fg_sales ? summary?.fg_sales : "-"}`,
    },
    {
      label: "RM",
      value: `${summary?.rm_sales ? summary?.rm_sales : "-"}`,
    },
  ];

  const billsReceivable = summary?.bills_receivable;

  return (
    <div className="container first_view_content">
      <UserProfile summary={summary} />
      <CustomerProfileDetails summary={summary} />
      <AccountSummary
        Order={Order}
        SalesType={SalesType}
        Memo={Memo}
        billsReceivable={billsReceivable}
      />
      <AddressContact BillAddress={BillAddress} />
    </div>
  );
}

export default CustomerInsightsSummary;
