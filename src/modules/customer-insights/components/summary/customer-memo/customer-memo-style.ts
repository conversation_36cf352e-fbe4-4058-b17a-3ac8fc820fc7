import { bes, bs } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const CustomerMemoStyle = styled.div`
  ${(props) => css`
    .credit_memo_wpr {
      table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        padding: 0;
        thead {
          tr {
            th {
              ${bs(props?.theme?.text?.high, 500)};
              padding-top: 10px;
              padding-bottom: 10px;
              padding-left: 12px;
              padding-right: 12px;
              background-color: ${(props) => props?.theme?.colors?.brand_low};
              text-align: right;
              white-space: nowrap;
              &:last-child {
                padding-right: 16px;
              }
              &:first-child {
                text-align: left;
                padding-left: 16px;
              }
            }
          }
        }
        tbody {
          tr {
            &:first-child {
              td {
                padding-top: 14px;
              }
            }
            td {
              ${bes(props?.theme?.text?.mid, 400)};
              padding-top: 8px;
              padding-bottom: 8px;
              padding-left: 12px;
              padding-right: 12px;
              text-align: right;
              &:last-child {
                padding-right: 16px;
                ${bes(props?.theme?.text?.high, 500)};
              }
              &:first-child {
                text-align: left;
                padding-left: 16px;
              }
              p {
                &.alert {
                  ${bes(props?.theme?.status?.alert, 500)};
                }
                &.attention {
                  ${bes(props?.theme?.status?.attension, 500)};
                }
                &.success {
                  ${bes(props?.theme?.status?.success, 500)};
                }
                &.normal {
                  ${bes(props?.theme?.text?.high, 500)};
                }
              }
            }
          }
        }
      }
    }
  `}
`;
