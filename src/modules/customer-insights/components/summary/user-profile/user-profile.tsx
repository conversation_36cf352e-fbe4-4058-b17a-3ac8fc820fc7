import {
  CaseIcon,
  HashIcon,
  MailIcon,
  MapIcon,
  PhoneIcon,
  UserIcon,
} from "@/assets/icons";
import React from "react";
import { UserProfileStyle } from "./user-profile-style";

const UserProfile = (props: { summary: any }) => {
  return (
    <UserProfileStyle>
      <div className="customer_info_wpr">
        <div className="cus_img_wpr">
          <picture>
            <source media="(min-width: 1200px)" srcSet="" />
            <img
              src="https://encrypted-tbn1.gstatic.com/images?q=tbn:ANd9GcTSsp8ZcUG7qn6RccmY8zBJN3aYRbo-zY12UJbvLfncoQM_baKp"
              alt=""
            />
            <img src="" alt="" />
          </picture>
        </div>
        <div className="cus_content_wpr">
          <div className="top_cus_wpr">
            <div className="this_row">
              <div className="this_column">
                <div className="inner_info">
                  <UserIcon />
                  <div className="icon_details">
                    <p>Full name</p>
                    <span>{props.summary?.name}</span>
                  </div>
                </div>
              </div>
              <div className="this_column">
                <div className="inner_info">
                  <HashIcon />
                  <div className="icon_details">
                    <p>Customer code</p>
                    <span>{props.summary?.customer_code}</span>
                  </div>
                </div>
              </div>
              <div className="this_column">
                <div className="inner_info">
                  <CaseIcon />
                  <div className="icon_details">
                    <p>Business type</p>
                    <span>-</span>
                  </div>
                </div>
              </div>
              <div className="this_column">
                <div className="inner_info">
                  <PhoneIcon />
                  <div className="icon_details">
                    <p>Contact number</p>
                    <span>+91 98765 43210</span>
                  </div>
                </div>
              </div>
              <div className="this_column">
                <div className="inner_info">
                  <MailIcon />
                  <div className="icon_details">
                    <p>Email address</p>
                    <span>
                      {props.summary?.email ? props.summary?.email : "-"}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="bottom_cus_wpr">
            <div className="this_row">
              <div className="this_column">
                <div className="inner_info">
                  <MapIcon />
                  <div className="icon_details">
                    <p>Default billing address</p>
                    <span>Michael Lee</span>
                    <span>431 School House Road, Georgetown, KY 40324</span>
                    <span>+1 406 555 0120</span>
                  </div>
                </div>
              </div>
              <div className="this_column">
                <div className="inner_info">
                  <MapIcon />
                  <div className="icon_details">
                    <p>Default shipping address</p>
                    <span>Michael Lee</span>
                    <span>431 School House Road, Georgetown, KY 40324</span>
                    <span>+1 406 555 0120</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </UserProfileStyle>
  );
};

export default UserProfile;
