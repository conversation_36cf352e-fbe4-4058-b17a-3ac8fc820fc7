import { Media, bes, bl, bs, h4 } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const AccountSummaryStyle = styled.div`
  ${(props) => css`
    .account_summary_wpr {
      margin-bottom: 64px;
      @media ${Media.desktop} {
        margin-bottom: 52px;
      }
      @media ${Media.tablet} {
        margin-bottom: 48px;
      }
      & > h2 {
        ${h4(props?.theme?.text?.high, 400)};
        margin-bottom: 24px;
      }
      .ant-row {
        margin-bottom: -32px;
        @media ${Media.tablet} {
          margin-bottom: -24px;
        }
        & > * {
          margin-bottom: 32px;
          @media ${Media.tablet} {
            margin-bottom: 24px;
          }
        }
      }
      .account_summary_inner {
        margin-bottom: 40px;
        .account_summary_table1 {
          overflow: auto;
          table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            padding: 0;
            thead {
              tr {
                th {
                  ${bs(props?.theme?.text?.high, 500)};
                  padding-top: 10px;
                  padding-bottom: 10px;
                  padding-left: 12px;
                  padding-right: 12px;
                  background-color: ${(props) =>
                    props?.theme?.colors?.brand_low};
                  white-space: nowrap;
                  &:last-child {
                    text-align: right;
                    padding-right: 16px;
                  }
                  &:first-child {
                    text-align: left;
                    padding-left: 16px;
                  }
                }
              }
            }
            tbody {
              tr {
                &:first-child {
                  td {
                    padding-top: 16px;
                  }
                }
                &:last-child {
                  td {
                    ${bes(props?.theme?.text?.high, 500)};
                  }
                }
                td {
                  ${bes(props?.theme?.text?.mid, 400)};
                  padding-top: 8px;
                  padding-bottom: 8px;
                  padding-left: 12px;
                  padding-right: 12px;
                  text-align: right;
                  &:last-child {
                    padding-right: 16px;
                  }
                  &:first-child {
                    text-align: left;
                    padding-left: 16px;
                  }
                }
              }
            }
          }
        }
        .account_summary_table2 {
          overflow: auto;
          table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            padding: 0;
            thead {
              tr {
                th {
                  ${bs(props?.theme?.text?.high, 500)};
                  padding-top: 10px;
                  padding-bottom: 10px;
                  padding-left: 12px;
                  padding-right: 12px;
                  background-color: ${(props) =>
                    props?.theme?.colors?.brand_low};
                  text-align: right;
                  white-space: nowrap;
                  &:last-child {
                    padding-right: 16px;
                  }
                  &:first-child {
                    text-align: left;
                    padding-left: 16px;
                  }
                }
              }
            }
            tbody {
              tr {
                &:first-child {
                  td {
                    padding-top: 16px;
                  }
                }
                &:last-child {
                  td {
                    ${bes(props?.theme?.text?.high, 500)};
                  }
                }
                td {
                  ${bes(props?.theme?.text?.mid, 400)};
                  padding-top: 8px;
                  padding-bottom: 8px;
                  padding-left: 12px;
                  padding-right: 12px;
                  text-align: right;
                  &:last-child {
                    padding-right: 16px;
                  }
                  &:first-child {
                    text-align: left;
                    padding-left: 16px;
                  }
                }
              }
            }
          }
        }
      }
      .bills_receivable_wpr {
        & > h3 {
          ${bl(props?.theme?.text?.high, 400)};
          margin-bottom: 24px;
        }
        .account_summary_table1 {
          overflow: auto;
          table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            padding: 0;
            thead {
              tr {
                th {
                  ${bs(props?.theme?.text?.high, 500)};
                  padding-top: 10px;
                  padding-bottom: 10px;
                  padding-left: 12px;
                  padding-right: 12px;
                  background-color: ${(props) =>
                    props?.theme?.colors?.brand_low};
                  white-space: nowrap;
                  &:last-child {
                    text-align: right;
                    padding-right: 16px;
                  }
                  &:first-child {
                    text-align: left;
                    padding-left: 16px;
                  }
                }
              }
            }
            tbody {
              tr {
                &:first-child {
                  td {
                    padding-top: 16px;
                  }
                }
                td {
                  ${bes(props?.theme?.text?.mid, 400)};
                  padding-top: 8px;
                  padding-bottom: 8px;
                  padding-left: 12px;
                  padding-right: 12px;
                  text-align: right;
                  &:last-child {
                    padding-right: 16px;
                  }
                  &:first-child {
                    text-align: left;
                    padding-left: 16px;
                  }
                }
              }
            }
          }
        }
        .account_summary_table2 {
          table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            padding: 0;
            thead {
              tr {
                th {
                  ${bs(props?.theme?.text?.high, 500)};
                  padding-top: 10px;
                  padding-bottom: 10px;
                  padding-left: 12px;
                  padding-right: 12px;
                  background-color: ${(props) =>
                    props?.theme?.colors?.brand_low};
                  text-align: right;
                  white-space: nowrap;
                  &:last-child {
                    padding-right: 16px;
                  }
                  &:first-child {
                    text-align: left;
                    padding-left: 16px;
                  }
                }
              }
            }
            tbody {
              tr {
                &:first-child {
                  td {
                    padding-top: 14px;
                  }
                }
                td {
                  ${bes(props?.theme?.text?.mid, 400)};
                  padding-top: 8px;
                  padding-bottom: 8px;
                  padding-left: 12px;
                  padding-right: 12px;
                  text-align: right;
                  &:last-child {
                    padding-right: 16px;
                  }
                  &:first-child {
                    text-align: left;
                    padding-left: 16px;
                  }
                }
              }
            }
          }
        }
      }
    }
  `}
`;
