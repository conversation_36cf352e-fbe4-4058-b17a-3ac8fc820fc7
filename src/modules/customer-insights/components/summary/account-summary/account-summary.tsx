import React from "react";
import { Col, Row } from "antd";
import { AccountSummaryStyle } from "./account-summary-style";
const AccountSummary = (props) => {
  return (
    <AccountSummaryStyle>
      <div className="account_summary_wpr">
        <h2>Account summary</h2>
        <div className="account_summary_inner">
          <Row gutter={{ md: 24, lg: 32, xl: 52, xxl: 72 }}>
            <Col md={8}>
              <div className="account_summary_table1">
                <table>
                  <thead>
                    <tr>
                      <th>Open order</th>
                      <th>Amount</th>
                    </tr>
                  </thead>
                  <tbody>
                    {props.Order?.map((item) => (
                      <tr>
                        <td>{item?.label}</td>
                        <td>{item?.value}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </Col>
            <Col md={16}>
              <div className="account_summary_table2">
                <table>
                  <thead>
                    <tr>
                      <th>Sales type</th>
                      <th>2023-24</th>
                      <th>2022-23</th>
                      <th>2021-22</th>
                    </tr>
                  </thead>
                  <tbody>
                    {props.SalesType?.map((item) => (
                      <tr>
                        <td>{item?.label}</td>
                        <td>{item?.value1}</td>
                        <td>{item?.value2}</td>
                        <td>{item?.value3}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </Col>
          </Row>
        </div>
        <div className="bills_receivable_wpr">
          <h3>Bills receivable</h3>
          <Row gutter={{ md: 24, lg: 32, xl: 52, xxl: 72 }}>
            <Col md={24} lg={16}>
              <div className="account_summary_table2">
                <table>
                  <thead>
                    <tr>
                      {props.billsReceivable?.map((item, index) => (
                        <th key={`header-${index}`}>{item.Label}</th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      {props.billsReceivable?.map((item, index) => (
                        <td key={`value-${index}`}>{item.Value}</td>
                      ))}
                    </tr>
                  </tbody>
                </table>
              </div>
            </Col>
            <Col md={24} lg={8}>
              <div className="account_summary_table1">
                <table>
                  <thead>
                    <tr>
                      <th>Open memo</th>
                      <th>Amount</th>
                    </tr>
                  </thead>
                  <tbody>
                    {props.Memo?.map((item) => (
                      <tr>
                        <td>{item?.label}</td>
                        <td>{item?.value}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </Col>
          </Row>
        </div>
      </div>
    </AccountSummaryStyle>
  );
};

export default AccountSummary;
