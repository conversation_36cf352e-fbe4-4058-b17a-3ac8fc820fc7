import React from 'react'
import { Btn, InputGroups, SelectGroups } from "@dilpesh/kgk-ui-library";
import { Col, Row } from "antd";
import { SearchAccessoriesStyle } from './search-accessories-style';
const SearchAccessories = () => {
    return (
        <SearchAccessoriesStyle>
            <div className="scrollable_content_wpr">
                <div className="top_select_wpr">
                    <div className="left_select_wpr">
                        <Row gutter={{ md: 24 }}>
                            <Col md={12} lg={6}>
                                <InputGroups label="Group name" />
                                <p className="sub_item">Clarity + Color</p>
                            </Col>
                            <Col md={12} lg={6}>
                                <SelectGroups label="Currency" />
                            </Col>
                            <Col md={12} lg={6}>
                                <SelectGroups label="Chart based on" />
                            </Col>
                            <Col md={12} lg={6}>
                                <SelectGroups label="Lot code" />
                            </Col>
                            <Col md={24} className="search_btn_wpr">
                                <Btn bg="fill" size="large">Search</Btn>
                            </Col>
                        </Row>
                    </div>
                </div>
            </div>
        </SearchAccessoriesStyle>

    )
}

export default SearchAccessories
