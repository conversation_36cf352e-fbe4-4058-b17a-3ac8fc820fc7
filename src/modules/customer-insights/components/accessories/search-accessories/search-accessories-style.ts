import { Media, b2xs } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const SearchAccessoriesStyle = styled.div`
  ${(props) => css`
    .scrollable_content_wpr {
      @media ${Media.desktop} {
        padding-bottom: 32px;
        overflow-y: auto;
        overflow-x: hidden;
      }
      @media ${Media.ultra_mobile} {
        padding-bottom: 24px;
      }
    }
    .top_select_wpr {
      .left_select_wpr {
        .ant-row {
          margin-bottom: -24px;
          & > * {
            margin-bottom: 24px;
          }
        }
        .ant-col-lg-6 {
          @media ${Media.tablet_above} {
            max-width: calc(25% - 36px);
            flex: 0 0 calc(25% - 36px);
          }
        }
        .search_btn_wpr {
          @media ${Media.tablet_above} {
            max-width: 144px;
            flex: 0 0 144px;
          }
        }
        .sub_item {
          margin-top: 8px;
          ${b2xs(props?.theme?.text?.high, 400, false)};
        }
        .search_btn_wpr {
          padding-top: 7px;
          @media ${Media.tablet} {
            padding-top: 0;
            text-align: right;
          }
          .fill-btn {
            width: 120px;
            @media ${Media.tablet} {
              width: 100%;
            }
          }
        }
      }
    }
  `}
`;
