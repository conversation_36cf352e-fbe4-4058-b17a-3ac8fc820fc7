"use client"

import { FilterLineIcon } from "@/assets/icons/collection/FilterLineIcon";
import { PlusIcon } from "@/assets/icons/collection/PlusIcon";
import { DataTable, FilterDropdown, TableFooter, TableHeader, TabletFilterBtn } from "@dilpesh/kgk-ui-library";
import { useGetAccessoriesList, useGetTranslation, usePagination, usePayloadFilter } from "@magneto-it-solutions/kgk-common-library";
import { useParams } from "next/navigation";
import { useState } from "react";
import SearchAccessories from "./search-accessories/search-accessories";
import { actionitemList, assetsListingHead } from "./utils/constant";
import { CustomerInsightsAccessoriesstyle } from "./utils/style";
import { getAccessoriesColumnDef } from "./utils/utility";


const CustomerInsightsAccessories = () => {
  const { translation } = useGetTranslation();
  const params = useParams();
  const id: string = String(params?.id);

  const {
    selectedFilter,
    search,
    changeSearch,
    appliedFilters,
    clearFilters,
    applyFilters,
    handleFilterChange,
  } = usePayloadFilter("accessories-list");

  const { page, limit, view, changeLimit, changePage } =
    usePagination("accessories-list");

  const { accessoriesList, isLoading } = useGetAccessoriesList(id);
  // const { data: pdcFilter } = useGetPDCFiltersQuery({ customer_code: id });

  const [filterStatus, setFilterStatus] = useState<boolean>(false);

  const [filterOpen, setFilterOpen] = useState(false);

  const coloumnDefListing = getAccessoriesColumnDef({ isLoading });

  return (
    <CustomerInsightsAccessoriesstyle>
      <div className="container first_view_content">
        <TabletFilterBtn
          filter={true}
          filterTitle="Filter accessories"
          filterClick={() => { setFilterOpen(true) }}
        />
        <div className={`top_filter ${filterOpen ? 'active' : ''}`}>
          <div className="this_container">
            <div className="filter_mobile_header">
              <p>Filter accessories</p>
              <div className="close_icon" onClick={() => { setFilterOpen(false) }}>
                <svg viewBox="0 0 24 24" fill="none">
                  <path d="M18 6L6 18M6 6L18 18" stroke="#8C8C8C" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
              </div>
            </div>
            <SearchAccessories />
          </div>
        </div>
        <div
          className={`filter_backdrop_layer ${filterOpen ? 'active' : ''}`}
          onClick={() => { setFilterOpen(false) }}>
        </div>
        <div className="table_main_wpr">
          <TableHeader
            isSearch={true}
            skeleton={isLoading}
            searchPlaceholder="Search customer code"
            borderBtnText={translation?.filter}
            borderBtnIcon={<FilterLineIcon />}
            searchText={search}
            onSearchChange={(e: any) =>
              changeSearch(e.target.value.toLowerCase())
            }
            borderBtnClick={() => setFilterStatus(!filterStatus)}
            actionList={actionitemList}
            fillBtnText="Add"
            fillBtnClick={() => { }}
            fillBtnIcon={<PlusIcon />}
          />
          <FilterDropdown
            open={filterStatus}
            close={() => setFilterStatus(!filterStatus)}
            // data={pdcFilter?.filters}
            onChangeFilter={(filter, value) => {
              handleFilterChange(filter, value);
            }}
            selectedFilter={selectedFilter}
            onApplyFilter={applyFilters}
            onClearFilter={clearFilters}
            fetchOptions={(_, searchText) => { }}
            clearAllBtnText={translation.ClearAll}
            applyBtntext={translation.Apply}
            filterText={translation.Filter}
          />
          <DataTable
            isMultiselect={true}
            column={assetsListingHead}
            coloumnDef={coloumnDefListing}
            data={accessoriesList?.data}
            skeleton={isLoading}
            fixedPosition="right"
          />
          <TableFooter
            skeleton={isLoading}
            currentPageSize={limit}
            currentPage={page}
            recordsTotal={accessoriesList?.filteredRecords}
            setCurrentPage={changePage}
            setCurrentPageSize={changeLimit}
            showingText={translation.Showing}
            showLabel={translation.Show}
            OfText={translation.Of}
          />
        </div>
      </div>
    </CustomerInsightsAccessoriesstyle>
  )
}

export default CustomerInsightsAccessories
