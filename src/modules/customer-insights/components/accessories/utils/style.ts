import { Media, b2xs, bes, bl } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const CustomerInsightsAccessoriesstyle = styled.div`
  ${(props) =>
    css`
      .tablet_filter_btn_wpr {
        margin-top: 40px;
        @media ${Media.desktop_above} {
          display: none;
        }
        @media ${Media.desktop} {
          margin-top: 32px;
        }
        @media ${Media.tablet} {
          margin-top: 32px;
        }
        @media ${Media.ultra_mobile} {
          margin-top: 24px;
        }
      }
      .top_filter {
        overflow: hidden;
        @media ${Media.desktop} {
          display: flex;
          margin-top: 0;
          position: fixed;
          bottom: 0;
          left: 0;
          right: 0;
          background-color: ${props.theme.colors.brand_dark_high};
          z-index: 89;
          max-height: calc(100% - 64px);
          bottom: calc(-100% + 64px);
          transition: bottom 100ms ease-in;
          &.active {
            bottom: 0;
            transition: bottom 100ms ease-in;
          }
        }
        .this_container {
          @media ${Media.desktop} {
            max-width: 936px;
            margin-left: auto;
            margin-right: auto;
            display: flex;
            flex-direction: column;
            & > * {
              width: 100%;
            }
          }
          @media ${Media.tablet} {
            max-width: 696px;
          }
          @media ${Media.ultra_mobile} {
            max-width: 516px;
          }
          @media ${Media.mobile} {
            max-width: 100%;
          }
        }
        .filter_mobile_header {
          padding-top: 32px;
          padding-bottom: 24px;
          @media ${Media.desktop_above} {
            display: none;
          }
          @media ${Media.mobile} {
            padding-left: 18px;
            padding-right: 48px;
          }
          p {
            ${bl(props.theme.text.high, 500)}
          }
          .close_icon {
            position: absolute;
            top: 32px;
            right: 32px;
            height: 24px;
            width: 24px;
            cursor: pointer;
            @media ${Media.mobile} {
              top: 16px;
              right: 16px;
            }
            svg {
              height: inherit;
              width: inherit;
              path {
                stroke: ${props.theme.text.mid};
                transition: all 200ms ease-in;
              }
            }
            &:hover {
              svg {
                path {
                  stroke: ${props.theme.colors.brand_high};
                  transition: all 200ms ease-in;
                }
              }
            }
          }
        }
      }
      .filter_backdrop_layer {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 88;
        background-color: #000;
        opacity: 0.4;
        display: none;
        &.active {
          display: block;
        }
        @media ${Media.desktop_above} {
          display: none;
        }
      }

      .table_main_wpr {
        margin-top: 32px;
        .data_table {
          margin-top: 24px;
        }
        .icon_name_24_8_bes {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          img,
          svg {
            width: 24px;
            height: 24px;
            path {
              stroke: ${props.theme.colors.brand_high};
            }
          }
          p {
            padding-left: 8px;
            width: calc(100% - 24px);
            ${bes(props?.theme?.text?.high, 400)};
          }
        }
      }
    `}
`;
