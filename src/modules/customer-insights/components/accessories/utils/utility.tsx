import { ActionIcon } from "@/assets/icons";
import { ActionDropdown, Skeleton } from "@dilpesh/kgk-ui-library";
import { actionitemList, items } from "./constant";

export const getAccessoriesColumnDef = ({ isLoading }) => {
  return {
    groupName: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : <p>{item?.component_details?.Group_Name || "-"}</p>,
    },
    customer: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : <p>{item?.customer || "-"}</p>,
    },
    commodity: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : <p>{item?.component_details?.commodity || "-"}</p>,
    },
    color: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="60px" /> : <p>{item?.component_details?.Color || "-"}</p>,
    },
    chartBase: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="60px" /> : <p>{item?.chartBase || "-"}</p>,
    },
    lotCode: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : <p>{item?.component_details?.LotCode || "-"}</p>,
    },
    spRate: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="80px" /> : <p>{item?.component_details?.SalesPrice || "-"}</p>,
    },
    updatedBy: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="80px" />
        ) : (
          <div className='icon_name_24_8_bes'>
            <img src={item.updatedByIcon} alt="" />
            <p>{item.updatedBy}</p>
          </div>
        ),
    },
    updatedOn: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : <p>{item?.updatedAt || "-"}</p>,
    },
    action: {
      renderChildren: () =>
        isLoading ? (
          <Skeleton width="32px" />
        ) : (
          <div className='action_wpr'>
            <ActionDropdown
              items={items}
              actionList={actionitemList}
              actionIcon={<ActionIcon />}
              className="mx-auto"
            />
          </div>
        ),
    },
  };
};
