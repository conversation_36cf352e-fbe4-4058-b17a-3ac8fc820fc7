"use client"

import { <PERSON>rid<PERSON>con, ListIcon, Metal1, Metal2, Metal3, Metal4, Qty1, Qty2, Qty3 } from "@/assets/icons";
import { Btn, ListingHeader, ProductGrid, ProductList, Searchbar, SideFilter, TableFooter, TabletFilterBtn } from "@dilpesh/kgk-ui-library";
import { Col, Row } from "antd";
import { useState } from "react";
import { CustomerInsightsStockstyle } from "./utils/style";


const CustomerInsightsStock = () => {

  const [filterOpen, setFilterOpen] = useState(false);
  const [sortOpen, setSortOpen] = useState(false);

  const [currentPage, setCurrentPage] = useState(1);
  const [currentPageSize, setCurrentPageSize] = useState(25);
  const [recordsTotal, setRecordsTotal] = useState(500);

  //**customer-insights-data-table-expand**/

  const data = [
    {
      value: 'grid',
      content: <GridIcon />
    },
    {
      value: 'listing',
      content: <ListIcon />
    },
  ];

  const [activeBtn, setActivebtn] = useState(data[0].value);

  const productGridData = [
    {
      id: '1',
      img: '/crm/assets/img/stock-img-1.png',
      owner: 'Martin Flyer',
      ownerId: 'DERS01XXSRR',
      name: 'Diamond Vine Ring in 18k Rose Gold',
      collectionName: 'Business category',
      price: '$5,000.00',
      crm: '2.5',
      grms: '3.5',
      customisationBtn: true,
    },
    {
      id: '2',
      img: '/crm/assets/img/stock-img-2.png',
      owner: 'Martin Flyer',
      ownerId: 'DERS01XXSRS',
      name: 'Diamond Vine Ring in 18k Rose Gold',
      collectionName: 'Business category',
      price: '$5,000.00',
      crm: '2.5',
      grms: '3.5',
      customisationBtn: true,
      label: 'Out of stock',
    },
    {
      id: '3',
      img: '/crm/assets/img/stock-img-3.png',
      owner: 'Martin Flyer',
      ownerId: 'DERS02XXSRR',
      name: 'Diamond Vine Ring in 18k Rose Gold',
      collectionName: 'Business category',
      price: '$5,000.00',
      crm: '2.5',
      grms: '3.5',
      customisationBtn: true,
    },
    {
      id: '4',
      img: '/crm/assets/img/stock-img-4.png',
      owner: 'Martin Flyer',
      ownerId: 'DERD01XXSRR',
      name: 'Diamond Vine Ring in 18k Rose Gold',
      collectionName: 'Business category',
      price: '$5,000.00',
      crm: '2.5',
      grms: '3.5',
      customisationBtn: true,
    },
    {
      id: '5',
      img: '/crm/assets/img/stock-img-5.png',
      owner: 'Martin Flyer',
      ownerId: 'DEAS01XXSRR',
      name: 'Diamond Vine Ring in 18k Rose Gold',
      collectionName: 'Business category',
      price: '$5,000.00',
      crm: '2.5',
      grms: '3.5',
      customisationBtn: true,
    },
    {
      id: '6',
      img: '/crm/assets/img/stock-img-6.png',
      owner: 'Martin Flyer',
      ownerId: 'DERS05XXSRR',
      name: 'Diamond Vine Ring in 18k Rose Gold',
      collectionName: 'Business category',
      price: '$5,000.00',
      crm: '2.5',
      grms: '3.5',
      customisationBtn: true,
    },
    {
      id: '7',
      img: '/crm/assets/img/stock-img-7.png',
      owner: 'Martin Flyer',
      ownerId: 'DERS05XXSRR',
      name: 'Diamond Vine Ring in 18k Rose Gold',
      collectionName: 'Business category',
      price: '$5,000.00',
      crm: '2.5',
      grms: '3.5',
      customisationBtn: true,
    },
    {
      id: '8',
      img: '/crm/assets/img/stock-img-8.png',
      owner: 'Martin Flyer',
      ownerId: 'DERS05XXSRR',
      name: 'Diamond Vine Ring in 18k Rose Gold',
      collectionName: 'Business category',
      price: '$5,000.00',
      crm: '2.5',
      grms: '3.5',
      customisationBtn: true,
    },
    {
      id: '9',
      img: '/crm/assets/img/stock-img-9.png',
      owner: 'Martin Flyer',
      ownerId: 'DERS05XXSRR',
      name: 'Diamond Vine Ring in 18k Rose Gold',
      collectionName: 'Business category',
      price: '$5,000.00',
      crm: '2.5',
      grms: '3.5',
      customisationBtn: true,
    },
    {
      id: '10',
      img: '/crm/assets/img/stock-img-10.png',
      owner: 'Martin Flyer',
      ownerId: 'DERS05XXSRR',
      name: 'Diamond Vine Ring in 18k Rose Gold',
      collectionName: 'Business category',
      price: '$5,000.00',
      crm: '2.5',
      grms: '3.5',
      customisationBtn: true,
    },
  ];

  const productListData = [
    {
      id: '1',
      img: '/crm/assets/img/stock-img-1.png',
      owner: 'Martin Flyer',
      ownerId: 'DERS01XXSRR',
      description: 'Solitaire is the ultimate classic engagement ring style. Crafted to showcase your choice of Diamond. ',
      metalData: [
        {
          icon: <Metal1 />,
          value: 'silver',
          name: 'metal1'
        },
        {
          icon: <Metal2 />,
          value: 'rose',
          name: 'metal1'
        },
        {
          icon: <Metal3 />,
          value: 'gold',
          name: 'metal1'
        },
        {
          icon: <Metal4 />,
          value: 'pt',
          name: 'metal1'
        },
      ],
      qtyData: [
        {
          icon: <Qty1 />,
          value: 'silver',
          name: 'qty1'
        },
        {
          icon: <Qty2 />,
          value: 'rose',
          name: 'qty1'
        },
        {
          icon: <Qty3 />,
          value: 'gold',
          name: 'qty1'
        },
      ],
      name: 'Diamond Vine Ring in 18k Rose Gold',
      collectionName: 'Business category',
      price: '$5,000.00',
      crm: '2.5',
      grms: '3.5',
      customisationBtn: true,
    },
    {
      id: '2',
      img: '/crm/assets/img/stock-img-2.png',
      owner: 'Martin Flyer',
      ownerId: 'DERS01XXSRS',
      description: 'Solitaire is the ultimate classic engagement ring style. Crafted to showcase your choice of Diamond. ',
      metalData: [
        {
          icon: <Metal1 />,
          value: 'silver',
          name: 'metal2'
        },
        {
          icon: <Metal2 />,
          value: 'rose',
          name: 'metal2'
        },
        {
          icon: <Metal3 />,
          value: 'gold',
          name: 'metal2'
        },
        {
          icon: <Metal4 />,
          value: 'pt',
          name: 'metal2'
        },
      ],
      qtyData: [
        {
          icon: <Qty1 />,
          value: 'silver',
          name: 'qty2'
        },
        {
          icon: <Qty2 />,
          value: 'rose',
          name: 'qty2'
        },
        {
          icon: <Qty3 />,
          value: 'gold',
          name: 'qty2'
        },
      ],
      name: '14K Yellow Gold Garnet Birthstone Necklace',
      price: '$2,475.00',
    },
    {
      id: '3',
      img: '/crm/assets/img/stock-img-3.png',
      owner: 'Martin Flyer',
      ownerId: 'DERS02XXSRR',
      description: 'Solitaire is the ultimate classic engagement ring style. Crafted to showcase your choice of Diamond. ',
      metalData: [
        {
          icon: <Metal1 />,
          value: 'silver',
          name: 'metal3'
        },
        {
          icon: <Metal2 />,
          value: 'rose',
          name: 'metal3'
        },
        {
          icon: <Metal3 />,
          value: 'gold',
          name: 'metal3'
        },
        {
          icon: <Metal4 />,
          value: 'pt',
          name: 'metal3'
        },
      ],
      qtyData: [
        {
          icon: <Qty1 />,
          value: 'silver',
          name: 'qty3'
        },
        {
          icon: <Qty2 />,
          value: 'rose',
          name: 'qty3'
        },
        {
          icon: <Qty3 />,
          value: 'gold',
          name: 'qty3'
        },
      ],
      name: '14K White Gold Seven Stone Low Dome Basket Lab Created Diamond Ring',
      price: '$1,950.00',
    },
    {
      id: '4',
      img: '/crm/assets/img/stock-img-4.png',
      owner: 'Martin Flyer',
      ownerId: 'DERD01XXSRR',
      description: 'Solitaire is the ultimate classic engagement ring style. Crafted to showcase your choice of Diamond. ',
      metalData: [
        {
          icon: <Metal1 />,
          value: 'silver',
          name: 'metal4'
        },
        {
          icon: <Metal2 />,
          value: 'rose',
          name: 'metal4'
        },
        {
          icon: <Metal3 />,
          value: 'gold',
          name: 'metal4'
        },
        {
          icon: <Metal4 />,
          value: 'pt',
          name: 'metal4'
        },
      ],
      qtyData: [
        {
          icon: <Qty1 />,
          value: 'silver',
          name: 'qty4'
        },
        {
          icon: <Qty2 />,
          value: 'rose',
          name: 'qty4'
        },
        {
          icon: <Qty3 />,
          value: 'gold',
          name: 'qty4'
        },
      ],
      name: '14K White Gold Diamond Infinity Necklace',
      price: '$2,475.00',
    },
    {
      id: '5',
      img: '/crm/assets/img/stock-img-5.png',
      owner: 'Martin Flyer',
      ownerId: 'DEAS01XXSRR',
      description: 'Solitaire is the ultimate classic engagement ring style. Crafted to showcase your choice of Diamond. ',
      metalData: [
        {
          icon: <Metal1 />,
          value: 'silver',
          name: 'metal5'
        },
        {
          icon: <Metal2 />,
          value: 'rose',
          name: 'metal5'
        },
        {
          icon: <Metal3 />,
          value: 'gold',
          name: 'metal5'
        },
        {
          icon: <Metal4 />,
          value: 'pt',
          name: 'metal5'
        },
      ],
      qtyData: [
        {
          icon: <Qty1 />,
          value: 'silver',
          name: 'qty5'
        },
        {
          icon: <Qty2 />,
          value: 'rose',
          name: 'qty5'
        },
        {
          icon: <Qty3 />,
          value: 'gold',
          name: 'qty5'
        },
      ],
      name: '14K White Gold Seven Stone Low Dome Basket Lab Created Diamond Ring',
      price: '$1,950.00',
      label: 'Customisable'
    },
    {
      id: '6',
      img: '/crm/assets/img/stock-img-6.png',
      owner: 'Martin Flyer',
      ownerId: 'DERS01XXSRR',
      description: 'Solitaire is the ultimate classic engagement ring style. Crafted to showcase your choice of Diamond. ',
      metalData: [
        {
          icon: <Metal1 />,
          value: 'silver',
          name: 'metal6'
        },
        {
          icon: <Metal2 />,
          value: 'rose',
          name: 'metal6'
        },
        {
          icon: <Metal3 />,
          value: 'gold',
          name: 'metal6'
        },
        {
          icon: <Metal4 />,
          value: 'pt',
          name: 'metal6'
        },
      ],
      qtyData: [
        {
          icon: <Qty1 />,
          value: 'silver',
          name: 'qty6'
        },
        {
          icon: <Qty2 />,
          value: 'rose',
          name: 'qty6'
        },
        {
          icon: <Qty3 />,
          value: 'gold',
          name: 'qty6'
        },
      ],
      name: 'Diamond Vine Ring in 18k Rose Gold',
      price: '$5,000.00',
    },
    {
      id: '7',
      img: '/crm/assets/img/stock-img-7.png',
      owner: 'Martin Flyer',
      ownerId: 'DERS01XXSRS',
      description: 'Solitaire is the ultimate classic engagement ring style. Crafted to showcase your choice of Diamond. ',
      metalData: [
        {
          icon: <Metal1 />,
          value: 'silver',
          name: 'metal7'
        },
        {
          icon: <Metal2 />,
          value: 'rose',
          name: 'metal7'
        },
        {
          icon: <Metal3 />,
          value: 'gold',
          name: 'metal7'
        },
        {
          icon: <Metal4 />,
          value: 'pt',
          name: 'metal7'
        },
      ],
      qtyData: [
        {
          icon: <Qty1 />,
          value: 'silver',
          name: 'qty7'
        },
        {
          icon: <Qty2 />,
          value: 'rose',
          name: 'qty7'
        },
        {
          icon: <Qty3 />,
          value: 'gold',
          name: 'qty7'
        },
      ],
      name: '14K Yellow Gold Garnet Birthstone Necklace',
      price: '$2,475.00',
    },
    {
      id: '8',
      img: '/crm/assets/img/stock-img-8.png',
      owner: 'Martin Flyer',
      ownerId: 'DERS02XXSRR',
      description: 'Solitaire is the ultimate classic engagement ring style. Crafted to showcase your choice of Diamond. ',
      metalData: [
        {
          icon: <Metal1 />,
          value: 'silver',
          name: 'metal8'
        },
        {
          icon: <Metal2 />,
          value: 'rose',
          name: 'metal8'
        },
        {
          icon: <Metal3 />,
          value: 'gold',
          name: 'metal8'
        },
        {
          icon: <Metal4 />,
          value: 'pt',
          name: 'metal8'
        },
      ],
      qtyData: [
        {
          icon: <Qty1 />,
          value: 'silver',
          name: 'qty8'
        },
        {
          icon: <Qty2 />,
          value: 'rose',
          name: 'qty8'
        },
        {
          icon: <Qty3 />,
          value: 'gold',
          name: 'qty8'
        },
      ],
      name: '14K White Gold Seven Stone Low Dome Basket Lab Created Diamond Ring',
      price: '$1,950.00',
    },
    {
      id: '9',
      img: '/crm/assets/img/stock-img-9.png',
      owner: 'Martin Flyer',
      ownerId: 'DERD01XXSRR',
      description: 'Solitaire is the ultimate classic engagement ring style. Crafted to showcase your choice of Diamond. ',
      metalData: [
        {
          icon: <Metal1 />,
          value: 'silver',
          name: 'metal9'
        },
        {
          icon: <Metal2 />,
          value: 'rose',
          name: 'metal9'
        },
        {
          icon: <Metal3 />,
          value: 'gold',
          name: 'metal9'
        },
        {
          icon: <Metal4 />,
          value: 'pt',
          name: 'metal9'
        },
      ],
      qtyData: [
        {
          icon: <Qty1 />,
          value: 'silver',
          name: 'qty9'
        },
        {
          icon: <Qty2 />,
          value: 'rose',
          name: 'qty9'
        },
        {
          icon: <Qty3 />,
          value: 'gold',
          name: 'qty9'
        },
      ],
      name: '14K White Gold Diamond Infinity Necklace',
      price: '$2,475.00',
    },
    {
      id: '10',
      img: '/crm/assets/img/stock-img-10.png',
      owner: 'Martin Flyer',
      ownerId: 'DEAS01XXSRR',
      description: 'Solitaire is the ultimate classic engagement ring style. Crafted to showcase your choice of Diamond. ',
      metalData: [
        {
          icon: <Metal1 />,
          value: 'silver',
          name: 'metal10'
        },
        {
          icon: <Metal2 />,
          value: 'rose',
          name: 'metal10'
        },
        {
          icon: <Metal3 />,
          value: 'gold',
          name: 'metal10'
        },
        {
          icon: <Metal4 />,
          value: 'pt',
          name: 'metal10'
        },
      ],
      qtyData: [
        {
          icon: <Qty1 />,
          value: 'silver',
          name: 'qty10'
        },
        {
          icon: <Qty2 />,
          value: 'rose',
          name: 'qty10'
        },
        {
          icon: <Qty3 />,
          value: 'gold',
          name: 'qty10'
        },
      ],
      name: '14K White Gold Seven Stone Low Dome Basket Lab Created Diamond Ring',
      price: '$1,950.00',
    },
    {
      id: '11',
      img: '/crm/assets/img/stock-img-1.png',
      owner: 'Martin Flyer',
      ownerId: 'DERS01XXSRR',
      description: 'Solitaire is the ultimate classic engagement ring style. Crafted to showcase your choice of Diamond. ',
      metalData: [
        {
          icon: <Metal1 />,
          value: 'silver',
          name: 'metal11'
        },
        {
          icon: <Metal2 />,
          value: 'rose',
          name: 'metal11'
        },
        {
          icon: <Metal3 />,
          value: 'gold',
          name: 'metal11'
        },
        {
          icon: <Metal4 />,
          value: 'pt',
          name: 'metal11'
        },
      ],
      qtyData: [
        {
          icon: <Qty1 />,
          value: 'silver',
          name: 'qty11'
        },
        {
          icon: <Qty2 />,
          value: 'rose',
          name: 'qty11'
        },
        {
          icon: <Qty3 />,
          value: 'gold',
          name: 'qty11'
        },
      ],
      name: 'Diamond Vine Ring in 18k Rose Gold',
      price: '$5,000.00',
    },
    {
      id: '13',
      img: '/crm/assets/img/stock-img-1.png',
      owner: 'Martin Flyer',
      ownerId: 'DERS01XXSRS',
      description: 'Solitaire is the ultimate classic engagement ring style. Crafted to showcase your choice of Diamond. ',
      metalData: [
        {
          icon: <Metal1 />,
          value: 'silver',
          name: 'metal12'
        },
        {
          icon: <Metal2 />,
          value: 'rose',
          name: 'metal12'
        },
        {
          icon: <Metal3 />,
          value: 'gold',
          name: 'metal12'
        },
        {
          icon: <Metal4 />,
          value: 'pt',
          name: 'metal12'
        },
      ],
      qtyData: [
        {
          icon: <Qty1 />,
          value: 'silver',
          name: 'qty12'
        },
        {
          icon: <Qty2 />,
          value: 'rose',
          name: 'qty12'
        },
        {
          icon: <Qty3 />,
          value: 'gold',
          name: 'qty12'
        },
      ],
      name: '14K Yellow Gold Garnet Birthstone Necklace',
      price: '$2,475.00',
    },
    {
      id: '12',
      img: '/crm/assets/img/stock-img-1.png',
      owner: 'Martin Flyer',
      ownerId: 'DERS02XXSRR',
      description: 'Solitaire is the ultimate classic engagement ring style. Crafted to showcase your choice of Diamond. ',
      metalData: [
        {
          icon: <Metal1 />,
          value: 'silver',
          name: 'metal13'
        },
        {
          icon: <Metal2 />,
          value: 'rose',
          name: 'metal13'
        },
        {
          icon: <Metal3 />,
          value: 'gold',
          name: 'metal13'
        },
        {
          icon: <Metal4 />,
          value: 'pt',
          name: 'metal13'
        },
      ],
      qtyData: [
        {
          icon: <Qty1 />,
          value: 'silver',
          name: 'qty13'
        },
        {
          icon: <Qty2 />,
          value: 'rose',
          name: 'qty13'
        },
        {
          icon: <Qty3 />,
          value: 'gold',
          name: 'qty13'
        },
      ],
      name: '14K White Gold Seven Stone Low Dome Basket Lab Created Diamond Ring',
      price: '$1,950.00',
    },
    {
      id: '13',
      img: '/crm/assets/img/stock-img-1.png',
      owner: 'Martin Flyer',
      ownerId: 'DERD01XXSRR',
      description: 'Solitaire is the ultimate classic engagement ring style. Crafted to showcase your choice of Diamond. ',
      metalData: [
        {
          icon: <Metal1 />,
          value: 'silver',
          name: 'metal14'
        },
        {
          icon: <Metal2 />,
          value: 'rose',
          name: 'metal14'
        },
        {
          icon: <Metal3 />,
          value: 'gold',
          name: 'metal14'
        },
        {
          icon: <Metal4 />,
          value: 'pt',
          name: 'metal14'
        },
      ],
      qtyData: [
        {
          icon: <Qty1 />,
          value: 'silver',
          name: 'qty14'
        },
        {
          icon: <Qty2 />,
          value: 'rose',
          name: 'qty14'
        },
        {
          icon: <Qty3 />,
          value: 'gold',
          name: 'qty14'
        },
      ],
      name: '14K White Gold Diamond Infinity Necklace',
      price: '$2,475.00',
    },
    {
      id: '14',
      img: '/crm/assets/img/stock-img-1.png',
      owner: 'Martin Flyer',
      ownerId: 'DEAS01XXSRR',
      description: 'Solitaire is the ultimate classic engagement ring style. Crafted to showcase your choice of Diamond. ',
      metalData: [
        {
          icon: <Metal1 />,
          value: 'silver',
          name: 'metal15'
        },
        {
          icon: <Metal2 />,
          value: 'rose',
          name: 'metal15'
        },
        {
          icon: <Metal3 />,
          value: 'gold',
          name: 'metal15'
        },
        {
          icon: <Metal4 />,
          value: 'pt',
          name: 'metal15'
        },
      ],
      qtyData: [
        {
          icon: <Qty1 />,
          value: 'silver',
          name: 'qty15'
        },
        {
          icon: <Qty2 />,
          value: 'rose',
          name: 'qty15'
        },
        {
          icon: <Qty3 />,
          value: 'gold',
          name: 'qty15'
        },
      ],
      name: '14K White Gold Seven Stone Low Dome Basket Lab Created Diamond Ring',
      price: '$1,950.00',
    },
    {
      id: '15',
      img: '/crm/assets/img/stock-img-1.png',
      owner: 'Martin Flyer',
      ownerId: 'DERS01XXSRR',
      description: 'Solitaire is the ultimate classic engagement ring style. Crafted to showcase your choice of Diamond. ',
      metalData: [
        {
          icon: <Metal1 />,
          value: 'silver',
          name: 'metal16'
        },
        {
          icon: <Metal2 />,
          value: 'rose',
          name: 'metal16'
        },
        {
          icon: <Metal3 />,
          value: 'gold',
          name: 'metal16'
        },
        {
          icon: <Metal4 />,
          value: 'pt',
          name: 'metal16'
        },
      ],
      qtyData: [
        {
          icon: <Qty1 />,
          value: 'silver',
          name: 'qty16'
        },
        {
          icon: <Qty2 />,
          value: 'rose',
          name: 'qty16'
        },
        {
          icon: <Qty3 />,
          value: 'gold',
          name: 'qty16'
        },
      ],
      name: 'Diamond Vine Ring in 18k Rose Gold',
      price: '$5,000.00',
    },
    {
      id: '16',
      img: '/crm/assets/img/stock-img-1.png',
      owner: 'Martin Flyer',
      ownerId: 'DERS01XXSRS',
      description: 'Solitaire is the ultimate classic engagement ring style. Crafted to showcase your choice of Diamond. ',
      metalData: [
        {
          icon: <Metal1 />,
          value: 'silver',
          name: 'metal17'
        },
        {
          icon: <Metal2 />,
          value: 'rose',
          name: 'metal17'
        },
        {
          icon: <Metal3 />,
          value: 'gold',
          name: 'metal17'
        },
        {
          icon: <Metal4 />,
          value: 'pt',
          name: 'metal17'
        },
      ],
      qtyData: [
        {
          icon: <Qty1 />,
          value: 'silver',
          name: 'qty17'
        },
        {
          icon: <Qty2 />,
          value: 'rose',
          name: 'qty17'
        },
        {
          icon: <Qty3 />,
          value: 'gold',
          name: 'qty17'
        },
      ],
      name: '14K Yellow Gold Garnet Birthstone Necklace',
      price: '$2,475.00',
    },
  ];

  const sideFilterData = [
    {
      title: 'Jewellery',
      value: [
        {
          ck_label: 'Rings',
          defaultChecked: true
        },
        {
          ck_label: 'Necklaces',
          defaultChecked: false
        },
        {
          ck_label: 'Earrings',
          defaultChecked: false
        },
        {
          ck_label: 'Bracelets',
          defaultChecked: false
        },
        {
          ck_label: 'Necklaces',
          defaultChecked: false
        },
      ]
    },
    {
      title: 'Metal',
      value: [
        {
          ck_label: 'Rings',
          defaultChecked: false
        },
        {
          ck_label: 'Necklaces',
          defaultChecked: false
        },
      ]
    },
    {
      title: 'Diamond',
      value: [
        {
          ck_label: 'Rings',
          defaultChecked: false
        },
        {
          ck_label: 'Necklaces',
          defaultChecked: false
        },
      ]
    },
    {
      title: 'Carat',
      value: [
        {
          ck_label: 'Rings',
          defaultChecked: false
        },
        {
          ck_label: 'Necklaces',
          defaultChecked: false
        },
      ]
    },
    {
      title: 'Size',
      value: [
        {
          ck_label: 'Rings',
          defaultChecked: false
        },
        {
          ck_label: 'Necklaces',
          defaultChecked: false
        },
      ]
    },
    {
      title: 'Colour',
      value: [
        {
          ck_label: 'Rings',
          defaultChecked: false
        },
        {
          ck_label: 'Necklaces',
          defaultChecked: false
        },
      ]
    },
    {
      title: 'Clarity',
      value: [
        {
          ck_label: 'Rings',
          defaultChecked: false
        },
        {
          ck_label: 'Necklaces',
          defaultChecked: false
        },
      ]
    },
    {
      title: 'Gemstone',
      value: [
        {
          ck_label: 'Rings',
          defaultChecked: false
        },
        {
          ck_label: 'Necklaces',
          defaultChecked: false
        },
      ]
    },
  ]

  const showOptions = [
    {
      label: '5',
      value: '5'
    },
    {
      label: '10',
      value: '10'
    },
    {
      label: '15',
      value: '15'
    },
    {
      label: '20',
      value: '20'
    },
    {
      label: '25',
      value: '25'
    }
  ];

  const sortOptions = [
    {
      label: 'Best seller',
      value: 'Best seller'
    },
    {
      label: 'Best seller 2',
      value: 'Best seller 2'
    },
  ];

  const viewData = [
    {
      value: 'grid',
      content: <GridIcon />
    },
    {
      value: 'listing',
      content: <ListIcon />
    },
  ];

  const appliedFilterValue = ['Rings', 'Yellow gold'];



  return (
    <CustomerInsightsStockstyle>
      <div className='stocks_listing_wpr'>
        <div className='container'>
          <Row gutter={{ xs: 0, lg: 24 }} className="mt_48">
            <Col xs={24} lg={24} xl={6} xxl={4}>
              <div className="listing_filter_wpr">
                <SideFilter
                  open={filterOpen}
                  close={() => { setFilterOpen(false) }}
                  clearAllLink={'#'}
                  sideFilterData={sideFilterData as any}
                />
              </div>
            </Col>
            <Col xs={24} lg={24} xl={18} xxl={20}>
              <div className="product_listing_data_wpr">
                <Searchbar placeholder="Search item" />
                <TabletFilterBtn
                  filter={true}
                  sort={true}
                  filterText="3"
                  sortText=""
                  filterClick={() => { setFilterOpen(true) }}
                  sortClick={() => { setSortOpen(true) }}
                />
                <ListingHeader
                  showing={"1-25 / 100"}
                  showOptions={showOptions}
                  showChange={() => { }}
                  sortOptions={sortOptions}
                  sortOpen={sortOpen}
                  sortClose={() => { setSortOpen(false) }}
                  sortChange={() => { }}
                  viewData={viewData}
                  activeViewValue={activeBtn}
                  viewOnChange={(e: any) => setActivebtn(e.target.value)}
                />
                {/* <ApplidFilter value={}></ApplidFilter> */}
                <div className="product_listing_data">
                  {
                    activeBtn == 'grid' ?
                      <div className="grid_view">
                        <div className="row-5">
                          {productGridData?.map((item: any, index: number) => (
                            item?.column == 3 ?
                              <div className="column-3" key={index + 'productGrid'}>
                                <div
                                  className="product_grid_adv"
                                  style={{ background: 'url(' + item.img + ')' }}>
                                  <div className="adv_overlay">
                                    <h2>{item.title}<br /><i>{item.titleItalic}</i></h2>
                                    <div className="adv_btn_wpr">
                                      <Btn size="large" bg="fill">{item.btnText1}</Btn>
                                      <Btn size="large" bg="fill">{item.btnText2}</Btn>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              :
                              <div className="column-1" key={index + 'productGrid'}>
                                <ProductGrid {...item} selection={true} />
                              </div>
                          ))}
                        </div>
                      </div>
                      :
                      <div className="list_view">
                        {productListData?.map((item: any, index: number) => (
                          item?.adBanner == true ?
                            <div className="list_view_adv" key={index + 'productList'}>
                              <picture>
                                <source media="(min-width: 1200px)" srcSet={item.img} />
                                <img src={item.img} alt="adv-banner" />
                              </picture>
                            </div>
                            :
                            <div className="product_list_box_wpr" key={index + 'productList'}>
                              <ProductList
                                {...item}
                                selection={true}
                              />
                            </div>
                        ))}
                      </div>
                  }
                  <TableFooter
                    currentPageSize={currentPageSize}
                    currentPage={currentPage}
                    recordsTotal={recordsTotal}
                    setCurrentPage={setCurrentPage}
                    setCurrentPageSize={setCurrentPageSize}
                  />
                </div>
              </div>
            </Col>
          </Row>
        </div>
      </div>
    </CustomerInsightsStockstyle>
  )
}

export default CustomerInsightsStock
