import { Media, h4 } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const CustomerInsightsStockstyle = styled.div`
  ${(props) =>
    css`
      .stocks_listing_wpr {
        .listing_filter_wpr {
          padding-top: 12px;
          padding-right: 24px;
          @media ${Media.desktop} {
            padding-top: 0;
            padding-right: 0;
          }
        }
        .product_listing_data_wpr {
          .search-box {
            margin-bottom: 16px;
          }
          .tablet_filter_btn_wpr {
            margin-bottom: 16px;
            @media ${Media.desktop_above} {
              display: none;
            }
          }
          .product_listing_data {
            margin-top: 24px;
            .list_view {
              & > * {
                margin-top: 40px;
                @media ${Media.tablet} {
                  margin-top: 32px;
                }
                @media ${Media.ultra_mobile} {
                  margin-top: 24px;
                }
                &:first-child {
                  margin-top: 0;
                }
              }
              .product_list_box_wpr {
                /* margin-top: 24px;
                                border-top: 1px solid ${props.theme.line.light};
                                padding-top: 24px;
                                &:first-child{
                                    border-top: 0;
                                    padding-top: 0;
                                } */
              }
              /* .list_view_adv{
                                margin-top: 24px;
                                border-top: 1px solid ${props.theme.line.light};
                                padding-top: 40px;
                                margin-bottom: 40px;
                                & + .product_list_box_wpr{
                                    margin-top: 0;
                                    border-top: 0;
                                    padding-top: 0;
                                }
                            } */
            }
            .row-5 {
              margin-top: -40px;
              @media ${Media.tablet} {
                margin-top: -32px;
              }
              @media ${Media.ultra_mobile} {
                margin-top: -24px;
              }
              & > * {
                margin-top: 40px;
                @media ${Media.tablet} {
                  margin-top: 32px;
                }
                @media ${Media.ultra_mobile} {
                  margin-top: 24px;
                }
              }
            }
            .product_grid_adv {
              height: 100%;
              background-position: right center !important;
              background-size: cover !important;
              padding: 48px;
              display: flex;
              align-items: center;
              @media ${Media.below_1599} {
                padding: 30px;
              }
              @media ${Media.ultra_mobile} {
                padding: 24px;
              }
              .adv_overlay {
                width: 448px;
                h2 {
                  ${h4(props?.theme?.text?.high, 400)}
                }
                .adv_btn_wpr {
                  margin-top: 32px;
                  width: 100%;
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  @media ${Media.below_1399} {
                    justify-content: flex-start;
                    flex-direction: column;
                    align-items: flex-start;
                  }
                  @media ${Media.ultra_mobile} {
                    margin-top: 24px;
                  }
                  & > * {
                    width: calc(50% - 12px);
                    @media ${Media.below_1399} {
                      width: 200px;
                      margin-bottom: 16px;
                      &:last-child {
                        margin-bottom: 0;
                      }
                    }
                    @media ${Media.ultra_mobile} {
                      margin-bottom: 8px;
                      &:last-child {
                        margin-bottom: 0;
                      }
                    }
                  }
                }
              }
            }
            .data_footer_info {
              margin-top: 48px;
              @media ${Media.desktop} {
                margin-top: 40px;
              }
              @media ${Media.tablet} {
                margin-top: 32px;
              }
              @media ${Media.ultra_mobile} {
                margin-top: 24px;
              }
            }
          }
        }
      }
    `}
`;
