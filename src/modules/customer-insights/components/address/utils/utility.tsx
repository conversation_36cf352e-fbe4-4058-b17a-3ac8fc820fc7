import { ActionIcon } from "@/assets/icons";
import { ActionDropdown, Skeleton } from "@dilpesh/kgk-ui-library";
import { actionitemList, items } from "./constant";

export const getAddressColumnDef = ({ isLoading }) => {
  return {
    addressType: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="140px" /> : <p>{item?.type || "-"}</p>,
    },
    address: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="140px" /> : <p>{item?.street_address || "-"}</p>,
    },
    countryCode: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="140px" /> : <p>{item?.country || "-"}</p>,
    },
    stateCode: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="140px" /> : <p>{item?.stateCode || "-"}</p>,
    },
    defaultAddress: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="140px" /> : <p>{(item?.is_billing_default || item?.is_shipping_default) ? "Yes" : "No"}</p>,
    },
    country: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="140px" />
        ) : (
          <div className="icon_name_24_8_bes">
            {item?.countryIcon}
            <p>{item?.country_name || "-"}</p>
          </div>
        ),
    },
    action: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="32px" />
        ) : (
          <div className="action_wpr">
            <ActionDropdown
              items={items}
              actionList={actionitemList}
              actionIcon={<ActionIcon />}
              className="mx-auto"
            />
          </div>
        ),
    },
  };
};
