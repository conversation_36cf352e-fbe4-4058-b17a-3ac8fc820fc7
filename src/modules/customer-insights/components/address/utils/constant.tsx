import { CountryIcon } from "@/assets/icons/collection/CountryIcon";

export const actionitemList = [
  {
    key: 1,
    value: 'Edit', // This will be replaced dynamically in components
    class: 'black',
  },
  {
    key: 2,
    value: 'Remove', // This will be replaced dynamically in components
    class: 'red',
  },
];

export const items = {
  dropId: 'super-action'
};

export const assetsListingHead = [
  {
    label: 'Address type', // This will be replaced dynamically in components
    key: 'addressType'
  },
  {
    label: 'Country', // This will be replaced dynamically in components
    key: 'country'
  },
  {
    label: 'Address', // This will be replaced dynamically in components
    key: 'address'
  },
  {
    label: 'Country code', // This will be replaced dynamically in components
    key: 'countryCode'
  },
  {
    label: 'State code', // This will be replaced dynamically in components
    key: 'stateCode'
  },
  {
    label: 'Default address', // This will be replaced dynamically in components
    key: 'defaultAddress'
  },
  {
    label: 'Actions', // This will be replaced dynamically in components
    key: 'action',
    class: 'action'
  },
];

export const assetsListingData = [
  {
    addressType: 'House',
    country: 'United States',
    countryIcon: <CountryIcon />,
    address: '1915 Towne Centre Blvd, Suite 100,,Annapolis,21401,MD,United States',
    countryCode: 'US',
    stateCode: 'MD',
    defaultAddress: 'Yes',
  },
  {
    addressType: 'House',
    country: 'United States',
    countryIcon: <CountryIcon />,
    address: '1915 Towne Centre Blvd, Suite 100,,Annapolis,21401,MD,United States',
    countryCode: 'US',
    stateCode: 'MD',
    defaultAddress: 'Yes',
  },
  {
    addressType: 'House',
    country: 'United States',
    countryIcon: <CountryIcon />,
    address: '1915 Towne Centre Blvd, Suite 100,,Annapolis,21401,MD,United States',
    countryCode: 'US',
    stateCode: 'MD',
    defaultAddress: 'Yes',
  },
  {
    addressType: 'House',
    country: 'United States',
    countryIcon: <CountryIcon />,
    address: '1915 Towne Centre Blvd, Suite 100,,Annapolis,21401,MD,United States',
    countryCode: 'US',
    stateCode: 'MD',
    defaultAddress: 'Yes',
  },
  {
    addressType: 'House',
    country: 'United States',
    countryIcon: <CountryIcon />,
    address: '1915 Towne Centre Blvd, Suite 100,,Annapolis,21401,MD,United States',
    countryCode: 'US',
    stateCode: 'MD',
    defaultAddress: 'Yes',
  },
  {
    addressType: 'House',
    country: 'United States',
    countryIcon: <CountryIcon />,
    address: '1915 Towne Centre Blvd, Suite 100,,Annapolis,21401,MD,United States',
    countryCode: 'US',
    stateCode: 'MD',
    defaultAddress: 'Yes',
  },
  {
    addressType: 'House',
    country: 'United States',
    countryIcon: <CountryIcon />,
    address: '1915 Towne Centre Blvd, Suite 100,,Annapolis,21401,MD,United States',
    countryCode: 'US',
    stateCode: 'MD',
    defaultAddress: 'Yes',
  },
];


export const mainActionList = [
  {
    key: 1,
    value: 'Compare',
    class: 'black'
  },
  {
    key: 4,
    value: 'Reserve',
    class: 'black'
  },
];
