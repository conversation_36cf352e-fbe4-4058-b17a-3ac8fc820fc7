"use client"
import { FilterLineIcon } from "@/assets/icons/collection/FilterLineIcon";
import { PlusIcon } from "@/assets/icons/collection/PlusIcon";
import { DataTable, FilterDropdown, TableFooter, TableHeader } from "@dilpesh/kgk-ui-library";
import { useGetAddressList, useGetTranslation, usePagination, usePayloadFilter } from "@magneto-it-solutions/kgk-common-library";
import { useParams } from "next/navigation";
import { useState } from "react";
import CustomerInsightsAddAddressModel from "../modal/add-address-modal/add-address-model";
import { assetsListingHead, mainActionList } from "./utils/constant";
import { CustomerInsightsAddressstyle } from "./utils/style";
import { getAddressColumnDef } from "./utils/utility";

const CustomerInsightsAddress = () => {
  const { translation } = useGetTranslation();
  const params = useParams();
  const id: string = String(params?.id);

  const {
    selectedFilter,
    search,
    changeSearch,
    appliedFilters,
    clearFilters,
    applyFilters,
    handleFilterChange,
  } = usePayloadFilter("address-list");

  const { page, limit, view, changeLimit, changePage } =
    usePagination("address-list");

  const { addressList, isLoading } = useGetAddressList();

  const [filterStatus, setFilterStatus] = useState<boolean>(false);

  const [ciaaModalOpen, setCiaaModalOpen] = useState(false);
  const onAddAddress = (id: any) => {
    setCiaaModalOpen(true)
  }

  const coloumnDefListing = getAddressColumnDef({ isLoading });

  return (
    <>
      <CustomerInsightsAddressstyle>
        <div className="container first_view_content">
          <div className="table_main_wpr">
            <TableHeader
              isSearch={true}
              skeleton={isLoading}
              searchPlaceholder={translation?.search_address}
              borderBtnText={translation?.filter}
              borderBtnIcon={<FilterLineIcon />}
              searchText={search}
              onSearchChange={(e: any) =>
                changeSearch(e.target.value.toLowerCase())
              }
              borderBtnClick={() => setFilterStatus(!filterStatus)}
              actionList={mainActionList}
              fillBtnText={translation.add}
              fillBtnClick={onAddAddress}
              fillBtnIcon={<PlusIcon />}
            />
            <FilterDropdown
              open={filterStatus}
              close={() => setFilterStatus(!filterStatus)}
              //   data={customersFilter?.filters}
              onChangeFilter={(filter, value) => {
                handleFilterChange(filter, value);
              }}
              selectedFilter={selectedFilter}
              onApplyFilter={applyFilters}
              onClearFilter={clearFilters}
              fetchOptions={(_, searchText) => { }}
              clearAllBtnText={translation.ClearAll}
              applyBtntext={translation.Apply}
              filterText={translation.Filter}
            />
            <DataTable
              isMultiselect={true}
              column={assetsListingHead}
              coloumnDef={coloumnDefListing}
              data={addressList?.data}
              skeleton={isLoading}
              bodyClass="body_pt_12"
              fixedPosition="right"
            />
            <TableFooter
              skeleton={isLoading}
              currentPageSize={limit}
              currentPage={page}
              recordsTotal={addressList?.filteredRecords}
              setCurrentPage={changePage}
              setCurrentPageSize={changeLimit}
              showingText={translation.Showing}
              showLabel={translation.Show}
              OfText={translation.Of}
            />
          </div>
        </div>
      </CustomerInsightsAddressstyle>
      <CustomerInsightsAddAddressModel
        onHide={() => { setCiaaModalOpen(false) }}
        show={ciaaModalOpen}
        approveBtnEvent={() => { }}
        rejectBtnEvent={() => { setCiaaModalOpen(false) }}
      />
    </>
  )
}

export default CustomerInsightsAddress
