"use client";

import { FilterLineIcon } from "@/assets/icons/collection/FilterLineIcon";
import { PlusIcon } from "@/assets/icons/collection/PlusIcon";
import {
  DataTable,
  FilterDropdown,
  TableFooter,
  TableHeader
} from "@dilpesh/kgk-ui-library";
import {
  useGetPDCFiltersQuery,
  useGetPDCList,
  useGetTranslation,
  usePagination,
  usePayloadFilter
} from "@magneto-it-solutions/kgk-common-library";
import { useParams } from "next/navigation";
import { useState } from "react";
import CustomerInsightsAddPdcModel from "../modal/add-pdc-model/add-pdc-model";
import {
  assetsListingHead,
  mainActionList
} from "./utils/constant";
import { CustomerInsightsPdcstyle } from "./utils/style";
import { getPDCColumnDef } from "./utils/utility";

const CustomerInsightsPdc = () => {
  const { translation } = useGetTranslation();
  const params = useParams();
  const id: string = String(params?.id);

  const {
    selectedFilter,
    search,
    changeSearch,
    appliedFilters,
    clearFilters,
    applyFilters,
    handleFilterChange,
  } = usePayloadFilter("pdc-list");

  const { page, limit, view, changeLimit, changePage } =
    usePagination("pdc-list");

  const { pdcList, isLoading } = useGetPDCList(id);
  const { data: pdcFilter } = useGetPDCFiltersQuery({ customer_code: id });

  const [filterStatus, setFilterStatus] = useState<boolean>(false);

  const [ciaaModalOpen, setCiaaModalOpen] = useState(false);

  const onAddAddress = (id: any) => {
    setCiaaModalOpen(true);
  };

  const coloumnDefListing = getPDCColumnDef({ isLoading });

  return (
    <>
      <CustomerInsightsPdcstyle>
        <div className="container first_view_content">
          <div className="table_main_wpr">
            <TableHeader
              isSearch={true}
              skeleton={isLoading}
              searchPlaceholder={translation?.search_pdc}
              borderBtnText={translation?.filter}
              borderBtnIcon={<FilterLineIcon />}
              searchText={search}
              onSearchChange={(e: any) =>
                changeSearch(e.target.value.toLowerCase())
              }
              borderBtnClick={() => setFilterStatus(!filterStatus)}
              actionList={mainActionList}
              fillBtnText={translation?.add}
              fillBtnClick={onAddAddress}
              fillBtnIcon={<PlusIcon />}
            />
            <FilterDropdown
              open={filterStatus}
              close={() => setFilterStatus(!filterStatus)}
              data={pdcFilter?.filters}
              onChangeFilter={(filter, value) => {
                handleFilterChange(filter, value);
              }}
              selectedFilter={selectedFilter}
              onApplyFilter={applyFilters}
              onClearFilter={clearFilters}
              fetchOptions={(_, searchText) => { }}
              clearAllBtnText={translation.ClearAll}
              applyBtntext={translation.Apply}
              filterText={translation.Filter}
            />
            <DataTable
              isMultiselect={false}
              column={assetsListingHead}
              coloumnDef={coloumnDefListing}
              data={pdcList?.data}
              skeleton={isLoading}
              fixedPosition="right"
            />
            <TableFooter
              skeleton={isLoading}
              currentPageSize={limit}
              currentPage={page}
              recordsTotal={pdcList?.filteredRecords}
              setCurrentPage={changePage}
              setCurrentPageSize={changeLimit}
              showingText={translation.Showing}
              showLabel={translation.Show}
              OfText={translation.Of}
            />
          </div>
        </div>
      </CustomerInsightsPdcstyle>
      <CustomerInsightsAddPdcModel
        onHide={() => {
          setCiaaModalOpen(false);
        }}
        show={ciaaModalOpen}
        approveBtnEvent={() => { }}
        rejectBtnEvent={() => {
          setCiaaModalOpen(false);
        }}
      />
    </>
  );
};

export default CustomerInsightsPdc;
