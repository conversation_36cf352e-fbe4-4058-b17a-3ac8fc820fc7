import { ActionIcon } from "@/assets/icons";
import { ActionDropdown, Skeleton } from "@dilpesh/kgk-ui-library";
import { getSessionItem, SESSION_KEYS } from "@magneto-it-solutions/kgk-common-library";
import { format } from "date-fns";

export const getPDCColumnDef = ({ isLoading }) => {
  return {
    pdcNumber: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="120px" /> : (
          <div className="bes_500_wpr">
            <p>{item?.pdc_number || "-"}</p>
          </div>
        ),
    },
    referenceNumber: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="120px" /> : (
          <div className="bes_500_wpr">
            <p>{item?.reference_number || "-"}</p>
          </div>
        ),
    },
    receiveDate: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="150px" /> : (
          <div className="bes_500_wpr">
            <p>
              {item?.received_date_time
                ? format(
                  new Date(item?.received_date_time),
                  getSessionItem(SESSION_KEYS.DATE_FORMAT) || "dd MMM yyyy, h:mm a"
                )
                : "-"}
            </p>
          </div>
        ),
    },
    maturityDate: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="150px" /> : (
          <div className="bes_500_wpr">
            <p>
              {item?.maturity_date
                ? format(
                  new Date(item?.maturity_date),
                  getSessionItem(SESSION_KEYS.DATE_FORMAT) || "dd MMM yyyy, h:mm a"
                )
                : "-"}
            </p>
          </div>
        ),
    },
    currency: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : (
          <div className="bes_500_wpr">
            <p>{item?.currency || "-"}</p>
          </div>
        ),
    },
    status: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : (
          <div className="bes_500_wpr">
            <p>{item?.pdc_status || "-"}</p>
          </div>
        ),
    },
    pnAmount: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : (
          <div className="bes_500_wpr">
            <p>{item?.pan_amount || "-"}</p>
          </div>
        ),
    },
    action: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="32px" />
        ) : (
          <div className="action_wpr">
            <ActionDropdown
              items={{ dropId: "super-action" }}
              actionList={[
                { key: 1, value: "Edit", class: "black" },
                { key: 2, value: "Remove", class: "red" },
              ]}
              actionIcon={<ActionIcon />}
              className="mx-auto"
            />
          </div>
        ),
    },
  };
};
