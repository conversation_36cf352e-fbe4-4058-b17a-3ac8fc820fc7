import { bes } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const CustomerInsightsPdcstyle = styled.div`
    ${(props) =>
        css`
            .table_main_wpr{
                .data_table{
                    margin-top: 24px;
                }
                .bes_500_wpr{
                    p{
                        display: block;
                        ${bes(props?.theme?.text?.high, 500)}
                    }
                }
            }
        `
    }

`