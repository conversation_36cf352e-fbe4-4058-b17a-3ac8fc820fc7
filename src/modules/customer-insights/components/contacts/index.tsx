"use client"
import { ActionDropdown, DataTable, TableFooter, TableHeader } from "@dilpesh/kgk-ui-library";
import { useRef, useState } from "react";


import { CustomerInsightsContactsstyle } from "./utils/style";
import { actionitemList, assetsListingData, assetsListingHead, items } from "./utils/constant";
import { mainActionList } from "../../utils/constant";
import { FilterLineIcon } from "@/assets/icons/collection/FilterLineIcon";

import { ActionIcon } from "@/assets/icons/collection/ActionIcon";
import { PlusIcon } from "@/assets/icons/collection/PlusIcon";
import CustomerInsightsAddContactsModal from "../modal/add-contacts-modal/add-contacts-modal";
import { useModal } from "@magneto-it-solutions/kgk-common-library";


const CustomerInsightsContacts = () => {
    const addressRef = useRef<any>(null);

    const { open: addContactModalOpen } = useModal(null, "custom-modal");

    const onAddContacts = async (id: string) => {
        // const editData: any = await getAddress(id);

        addContactModalOpen({
            title: "Add contact",
            width: 792,
            wrapClassName: "detail_modal",
            approveBtnText: "Save",
            body: <CustomerInsightsAddContactsModal ref={addressRef} />,
            approveBtnEvent: () => {
                if (addressRef.current) {
                    addressRef.current.submit();
                }
            }

        })

    }

    const coloumnDefAssetsListing: Object = {

        contactName: {
            renderChildren: (item: any) => {
                return (
                    <div className='icon_name_24_8_bes'>
                        <img src={item.contactNameIcon} alt="" />
                        <p>{item.contactName}</p>
                    </div>
                );
            },
        },
        action: {
            renderChildren: (item: any) => {
                return (
                    <div className='action_wpr'>
                        <ActionDropdown items={items} actionList={actionitemList} actionIcon={<ActionIcon />} className="mx-auto" />
                    </div>
                );
            },
        }
    };


    const [currentPage, setCurrentPage] = useState(1);
    const [currentPageSize, setCurrentPageSize] = useState(25);
    const [recordsTotal, setRecordsTotal] = useState(500);


    const [ciacModalOpen, setCiacModalOpen] = useState(false);

    // const onAddContacts = (id: any) => {
    //     setCiacModalOpen(true)
    // }



    return (
        <>
            <CustomerInsightsContactsstyle>
                <div className="container first_view_content">
                    <div className="table_main_wpr">
                        <TableHeader
                            searchPlaceholder="Search contacts"
                            actionList={mainActionList}
                            borderBtnText="Filter"
                            borderBtnIcon={<FilterLineIcon />}
                            fillBtnText="Add"
                            fillBtnClick={onAddContacts}
                            fillBtnIcon={<PlusIcon />}
                            isSearch={true}
                        />
                        <DataTable
                            isMultiselect={true}
                            column={assetsListingHead}
                            coloumnDef={coloumnDefAssetsListing}
                            data={assetsListingData}
                            fixedPosition="right"
                        />
                        <TableFooter
                            currentPageSize={currentPageSize}
                            currentPage={currentPage}
                            recordsTotal={recordsTotal}
                            setCurrentPage={setCurrentPage}
                            setCurrentPageSize={setCurrentPageSize}
                            setRecordsTotal={setRecordsTotal}
                        />
                    </div>
                </div>
            </CustomerInsightsContactsstyle>
        </>
    )
}

export default CustomerInsightsContacts
