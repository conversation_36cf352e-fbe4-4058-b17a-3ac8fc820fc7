import { bes } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const CustomerInsightsContactsstyle = styled.div`
    ${(props) =>
        css`
            .table_main_wpr{
                .data_table{
                    margin-top: 24px;
                }
                .icon_name_24_8_bes{
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    img, svg{
                        width: 24px;
                        height: 24px;
                    }
                    p{
                        padding-left: 8px;
                        width: calc(100% - 24px);
                        ${bes(props?.theme?.text?.high, 400)};
                    }
                }
                .data_footer_info{
                    margin-top: 24px;
                }
            }
        `
    }

`