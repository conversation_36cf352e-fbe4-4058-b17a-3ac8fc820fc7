export class ClassChartFilterBox {
  label: string;
  value: string;
  subHeader: string;
  handleOnChange: (val: string) => void;
  options: {
    label: string;
    value: string;
  }[]

  constructor(data: any) {
    this.label = data.label;
    this.value = data.value;
    this.subHeader = data.subHeader;
    this.handleOnChange = data.handleOnChange;
    this.options = data.options?.map((item) => {
      return {
        label: item.label,
        value: item.value,
      }
    });
  }

}