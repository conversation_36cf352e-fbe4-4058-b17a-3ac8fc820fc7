import { Media, b2xs, bes, bl } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const CustomerInsightsDiamondChartstyle = styled.div`
    ${(props) =>
        css`
            .btn_group_wpr{
                max-width: 528px;
                margin-left: auto;
                margin-right: auto;
            }
            .tablet_filter_btn_wpr{
                margin-top: 40px;
                @media ${Media.desktop_above}{
                    display: none;
                }
                @media ${Media.desktop}{
                    margin-top: 32px;
                }
                @media ${Media.tablet}{
                    margin-top: 32px;
                }
                @media ${Media.ultra_mobile}{
                    margin-top: 24px;
                }
            }
            .top_filter{
                margin-top: 48px;
                overflow: hidden;
                @media ${Media.desktop}{
                    display: flex;
                    margin-top: 0;
                    position: fixed;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    background-color: ${(props.theme.colors.brand_dark_high)};
                    z-index: 89;
                    max-height: calc(100% - 64px);
                    bottom: calc(-100% + 64px);
                    transition: bottom 100ms ease-in;
                    &.active{
                        bottom: 0;
                        transition: bottom 100ms ease-in;
                    }
                }
                .this_container{
                    @media ${Media.desktop}{
                        max-width: 936px;
                        margin-left: auto;
                        margin-right: auto;
                        display: flex;
                        flex-direction: column;
                        & > *{
                            width: 100%;
                        }
                    }
                    @media ${Media.tablet}{
                        max-width: 696px;
                    }
                    @media ${Media.ultra_mobile}{
                        max-width: 516px;
                    }
                    @media ${Media.mobile}{
                        max-width: 100%;
                    }
                }
                .filter_mobile_header{
                    padding-top: 32px;
                    padding-bottom: 24px;
                    @media ${Media.desktop_above}{
                        display: none;
                    }
                    @media ${Media.mobile}{
                        padding-left: 18px;
                        padding-right: 48px;
                    }
                    p{
                        ${bl(props.theme.text.high, 500)}
                    }
                    .close_icon{
                        position: absolute;
                        top: 32px;
                        right: 32px;
                        height: 24px;
                        width: 24px;
                        cursor: pointer;
                        @media ${Media.mobile}{
                            top: 16px;
                            right: 16px;
                        }
                        svg{
                            height: inherit;
                            width: inherit;
                            path{
                                stroke: ${(props.theme.text.mid)};
                                transition: all 200ms ease-in;
                            }
                        }
                        &:hover{
                            svg{
                                path{
                                    stroke: ${(props.theme.colors.brand_high)};
                                    transition: all 200ms ease-in;
                                }
                            }
                        }
                    }
                }
                .scrollable_content_wpr{
                    @media ${Media.desktop}{
                        padding-bottom: 32px;
                        overflow-y: auto;
                        overflow-x: hidden;
                    }
                    @media ${Media.ultra_mobile}{
                        padding-bottom: 24px;
                    }
                }
                .top_filter_container{
                    @media ${Media.mobile}{
                        padding-left: 18px;
                        padding-right: 18px;
                    }
                    .top_filter_fields{
                        margin-bottom: -40px;
                        @media ${Media.desktop}{
                            margin-bottom: -32px;
                        }
                        @media ${Media.ultra_mobile}{
                            margin-bottom: -24px;
                        }
                        & > *{
                            margin-bottom: 40px;
                            @media ${Media.desktop}{
                                margin-bottom: 32px;
                            }
                            @media ${Media.ultra_mobile}{
                                margin-bottom: 24px;
                            }
                        }
                        .shape_wpr, .color_wpr{
                            & > label{
                                ${bes(props.theme.text.high, 500)};
                                margin-bottom: 16px;
                                display: block;
                                @media ${Media.ultra_mobile}{
                                    margin-bottom: 8px;
                                }
                            }
                        }
                        .size_wpr{
                            & > label{
                                ${bes(props.theme.text.high, 500)};
                                margin-bottom: 16px;
                                display: block;
                                @media ${Media.ultra_mobile}{
                                    margin-bottom: 8px;
                                }
                            }
                            .ant-row{
                                margin-bottom: -16px;
                                & > *{
                                    margin-bottom: 16px;
                                }
                            }
                        }
                        .color_main_wpr{
                            .tab-1{
                                .MuiTabs-flexContainer{
                                    display: inline-flex;
                                    .MuiTab-textColorPrimary{
                                        padding-top: 0;
                                    }
                                }
                            }
                            .range_slider{
                                margin-top: 16px;
                                @media ${Media.ultra_mobile}{
                                    margin-top: 12px;
                                }
                            }
                            .fancy_color_box{
                                margin-top: 8px;
                                @media ${Media.ultra_mobile}{
                                    margin-top: 12px;
                                }
                            }
                        }
                    }
                }
                .top_select_wpr{
                    margin-top: 40px;
                    .left_select_wpr{
                        .ant-row{
                            margin-bottom: -24px;
                            & > *{
                                margin-bottom: 24px;
                            }
                        }
                        .ant-col-lg-6{
                            @media ${Media.tablet_above}{
                                max-width: calc(25% - 36px);
                                flex: 0 0 calc(25% - 36px);
                            }
                        }
                        .search_btn_wpr{
                            @media ${Media.tablet_above}{
                                max-width: 144px;
                                flex: 0 0 144px
                            }
                        }
                        .sub_item{
                            margin-top: 8px;
                            ${b2xs(props?.theme?.text?.high, 400, false)};
                        }
                        .search_btn_wpr{
                            padding-top: 7px;
                            @media ${Media.tablet}{
                                padding-top: 0;
                                text-align: right;
                            }
                            .fill-btn{
                                width: 120px;
                                @media ${Media.tablet}{
                                    width: 100%;
                                }
                            }
                        }
                    }
                }
            }
            .filter_backdrop_layer{
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                z-index: 88;
                background-color: #000;
                opacity: 0.4;
                display: none;
                &.active{
                    display: block;
                }
                @media ${Media.desktop_above}{
                    display: none;
                }
            }
            
            .table_main_wpr{
                margin-top: 40px;
                @media ${Media.desktop}{
                    margin-top: 32px;
                }
                .data_table{
                    margin-top: 24px;
                }
                .icon_name_24_8_bes{
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    img, svg{
                        width: 24px;
                        height: 24px;
                        path{
                            stroke: ${(props.theme.colors.brand_high)};
                        }
                    }
                    p{
                        padding-left: 8px;
                        width: calc(100% - 24px);
                        ${bes(props?.theme?.text?.high, 400)};
                    }
                }
            }
            
        `
    }

`