"use client";
import { FilterLineIcon } from "@/assets/icons/collection/FilterLineIcon";
import {
  DataTable,
  FilterDropdown,
  TableFooter,
  TableHeader,
} from "@dilpesh/kgk-ui-library";
import {
  useGetPackageLogFiltersQuery,
  useGetPackageLogList,
  useGetTranslation,
  usePagination,
  usePayloadFilter,
} from "@magneto-it-solutions/kgk-common-library";
import { useParams } from "next/navigation";
import { useState } from "react";
import { assetsListingHead } from "./utils/constant";
import { CustomerInsightsPackageLogstyle } from "./utils/style";
import { getPackageLogColumnDef } from "./utils/utility";

const CustomerInsightsPackageLog = () => {
  const { translation } = useGetTranslation();
  const params = useParams();
  const id: string = String(params?.id);

  const {
    selectedFilter,
    search,
    changeSearch,
    appliedFilters,
    clearFilters,
    applyFilters,
    handleFilterChange,
  } = usePayloadFilter("packagelog-list");

  const { page, limit, view, changeLimit, changePage } =
    usePagination("packagelog-list");

  const { logs, isLoading } = useGetPackageLogList(id);
  const { data: packagelogFilter } = useGetPackageLogFiltersQuery({ customer_code: id })

  const [filterStatus, setFilterStatus] = useState<boolean>(false);

  const coloumnDefListing = getPackageLogColumnDef({ isLoading });

  return (
    <>
      <CustomerInsightsPackageLogstyle>
        <div className="container first_view_content">
          <div className="table_main_wpr">
            <TableHeader
              isSearch={true}
              skeleton={isLoading}
              searchPlaceholder="Search package"
              borderBtnText={translation?.filter}
              borderBtnIcon={<FilterLineIcon />}
              searchText={search}
              onSearchChange={(e: any) =>
                changeSearch(e.target.value.toLowerCase())
              }
              borderBtnClick={() => setFilterStatus(!filterStatus)}
            />
            <FilterDropdown
              open={filterStatus}
              close={() => setFilterStatus(!filterStatus)}
              data={packagelogFilter?.filters}
              onChangeFilter={(filter, value) => {
                handleFilterChange(filter, value);
              }}
              selectedFilter={selectedFilter}
              onApplyFilter={applyFilters}
              onClearFilter={clearFilters}
              fetchOptions={(_, searchText) => { }}
              clearAllBtnText={translation.ClearAll}
              applyBtntext={translation.Apply}
              filterText={translation.Filter}
            />
            <DataTable
              isMultiselect={false}
              column={assetsListingHead}
              coloumnDef={coloumnDefListing}
              data={logs?.data}
              skeleton={isLoading}
            />
            <TableFooter
              skeleton={isLoading}
              currentPageSize={limit}
              currentPage={page}
              recordsTotal={logs?.filteredRecords}
              setCurrentPage={changePage}
              setCurrentPageSize={changeLimit}
              showingText={translation.Showing}
              showLabel={translation.Show}
              OfText={translation.Of}
            />
          </div>
        </div>
      </CustomerInsightsPackageLogstyle>
    </>
  );
};

export default CustomerInsightsPackageLog;
