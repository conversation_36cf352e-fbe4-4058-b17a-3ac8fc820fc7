import { Skeleton } from "@dilpesh/kgk-ui-library";

export const getPackageLogColumnDef = ({ isLoading }) => {
  return {
    logNumber: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="120px" />
        ) : (
          <div className="bes_500_wpr">
            <p>{item?.log_number || "-"}</p>
          </div>
        ),
    },
    offReferenceNumber: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="120px" />
        ) : (
          <p>{item?.off_reference_number || "-"}</p>
        ),
    },
    logBy: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="160px" />
        ) : (
          <div className="icon_name_24_8_bes">
            <img src={item?.logByIcon} alt="" />
            <p>{item?.created_by || "-"}</p>
          </div>
        ),
    },
    airwayBillNumber: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="120px" />
        ) : (
          <p>{item?.airway_bill_number || "-"}</p>
        ),
    },
    logOn: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="120px" />
        ) : (
          <p>{item?.created_on || "-"}</p>
        ),
    },
    category: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="120px" />
        ) : (
          <p>{item?.category || "-"}</p>
        ),
    },
    description: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="120px" />
        ) : (
          <p>{item?.description || "-"}</p>
        ),
    },
  };
};
