"use client"
import { ActionDropdown, Btn, BtnGroup, DataTable, InputGroups, RangeSlider, SelectGroups, Tab1, TableFooter, TableHeader, TabletFilterBtn } from "@dilpesh/kgk-ui-library";
import { Col, Row } from "antd";
import { useState } from "react";
import { CustomerInsightsDiamondChartstyle } from "../diamond-chart/utils/style";
import ShapeTab from "../../common/components/atoms/shape-tab/shape-tab";
import HeadTab from "../../common/components/atoms/head-tab/head-tab";
import { actionitemList, assetsListingData, assetsListingHead, colorTabData, dataitem, diamondStoneData, fancyColorData, items } from "./utils/constant";
import { ActionIcon } from "@/assets/icons/collection/ActionIcon";
import { FilterLineIcon } from "@/assets/icons/collection/FilterLineIcon";
import { PlusIcon } from "@/assets/icons";


const CustomerInsightsGemstoneChart = () => {

    const [filterOpen, setFilterOpen] = useState(false);

    


    const [diamondStoneValue, setdiamondStoneValue] = useState<any>(0);
    const diamondStoneChange = (event: React.SyntheticEvent, newValue: number) => {
        setdiamondStoneValue(newValue);
    };
   

    const [colorTab, setcolorTab] = useState<any>(0);
    const colorTabChange = (event: React.SyntheticEvent, newValue: number) => {
        setcolorTab(newValue);
    };
   

    const [fancyColorValue, setfancyColorValue] = useState<any>(0);
    const fancyColorChange = (event: React.SyntheticEvent, newValue: number) => {
        setfancyColorValue(newValue);
    };
    

    
    const coloumnDefAssetsListing: Object = {
        updatedBy: {
            renderChildren: (item: any) => {
                return (
                    <div className='icon_name_24_8_bes'>
                        <img src={item.updatedByIcon} alt="" />
                        <p>{item.updatedBy}</p>
                    </div>
                );
            },
        },
        action: {
            renderChildren: (item: any) => {
                return (
                    <ActionDropdown
                        items={items}
                        actionList={actionitemList}
                        actionIcon={<ActionIcon />}
                        className="mx-auto" />
                );
            },
        }
    };

    

    const [currentPage, setCurrentPage] = useState(1);
    const [currentPageSize, setCurrentPageSize] = useState(25);
    const [recordsTotal, setRecordsTotal] = useState(500);


   

    const [btnGroupActive, setBtnGroupActive] = useState(dataitem[0].value);


    return (
        <CustomerInsightsDiamondChartstyle>
            <div className="container first_view_content">
                <div className="btn_group_wpr">
                    <BtnGroup
                        data={dataitem}
                        value={btnGroupActive}
                        onChange={(e: any) => setBtnGroupActive(e.target.value)}
                    ></BtnGroup>
                </div>
                <TabletFilterBtn
                    filter={true}
                    filterTitle="Filter diamonds"
                    filterText="3"
                    filterClick={() => { setFilterOpen(true) }}
                />
                <div className={`top_filter ${filterOpen ? 'active' : ''}`}>
                    <div className="this_container">
                        <div className="filter_mobile_header">
                            <p>Filter diamonds</p>
                            <div className="close_icon" onClick={() => { setFilterOpen(false) }}>
                                <svg viewBox="0 0 24 24" fill="none">
                                    <path d="M18 6L6 18M6 6L18 18" stroke="#8C8C8C" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                </svg>
                            </div>
                        </div>
                        <div className="scrollable_content_wpr">
                            <div className="top_filter_container">
                                <Row gutter={{ lg: 32, xl: 40, xxl: 52 }} className="top_filter_fields">
                                    <Col xs={24} sm={24} lg={12} xl={8} xxl={8}>
                                        <div className='shape_wpr'>
                                            <label>Diamond Shape</label>
                                            <ShapeTab
                                                value={diamondStoneValue}
                                                onChange={diamondStoneChange}
                                                data={diamondStoneData}
                                            />
                                        </div>
                                    </Col>
                                    <Col xs={24} sm={24} lg={12} xl={8} xxl={8}>
                                        <div className="color_main_wpr">
                                            <Tab1
                                                tabValue={colorTab}
                                                tabHandleChange={colorTabChange}
                                                tabOptions={colorTabData}
                                            />
                                            {
                                                colorTab == 0 ?
                                                    <RangeSlider
                                                        sliderValue={{
                                                            10: 'M',
                                                            20: 'L',
                                                            30: 'K',
                                                            40: 'J',
                                                            50: 'I',
                                                            60: 'H',
                                                            70: 'G',
                                                            80: 'F',
                                                            90: 'E',
                                                            100: 'D'
                                                        }}
                                                    />
                                                    :
                                                    <div className="fancy_color_box">
                                                        <HeadTab
                                                            value={fancyColorValue}
                                                            onChange={fancyColorChange}
                                                            data={fancyColorData}
                                                        />
                                                    </div>
                                            }
                                        </div>
                                    </Col>
                                    <Col xs={24} sm={24} lg={12} xl={8} xxl={8}>
                                        <RangeSlider
                                            title="Clarity"
                                            tooltipValue="Clarity"
                                            sliderValue={{
                                                11.11: 'l1',
                                                22.22: 'Sl2',
                                                33.33: 'Sl1',
                                                44.44: 'VS2',
                                                55.55: 'VS1',
                                                66.66: 'VVS2',
                                                77.77: 'VVS1',
                                                88.88: 'IL',
                                                99.99: 'IF',
                                            }}
                                        />
                                    </Col>
                                </Row>
                            </div>
                            <div className="top_select_wpr">
                                <div className="left_select_wpr">
                                    <Row gutter={{ md: 24 }}>
                                        <Col md={12} lg={6}>
                                            <InputGroups label="Group name" />
                                            <p className="sub_item">Clarity + Color</p>
                                        </Col>
                                        <Col md={12} lg={6}>
                                            <SelectGroups label="Currency" />
                                        </Col>
                                        <Col md={12} lg={6}>
                                            <SelectGroups label="Chart based on" />
                                        </Col>
                                        <Col md={12} lg={6}>
                                            <SelectGroups label="Lot code" />
                                        </Col>
                                        <Col md={24} className="search_btn_wpr">
                                            <Btn bg="fill" size="large">Search</Btn>
                                        </Col>
                                    </Row>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div
                    className={`filter_backdrop_layer ${filterOpen ? 'active' : ''}`}
                    onClick={() => { setFilterOpen(false) }}>
                </div>
                <div className="table_main_wpr">
                    <TableHeader
                        searchPlaceholder="Search customer code"
                        actionList={actionitemList}
                        borderBtnText="Filter"
                        borderBtnIcon={<FilterLineIcon />}
                        fillBtnText="Add"
                        fillBtnIcon={<PlusIcon />}
                        isSearch={true}
                    />
                    <DataTable
                        isMultiselect={true}
                        column={assetsListingHead}
                        coloumnDef={coloumnDefAssetsListing}
                        data={assetsListingData}
                        fixedPosition="right"
                    />
                    <TableFooter
                        currentPageSize={currentPageSize}
                        currentPage={currentPage}
                        recordsTotal={recordsTotal}
                        setCurrentPage={setCurrentPage}
                        setCurrentPageSize={setCurrentPageSize}
                        setRecordsTotal={setRecordsTotal}
                    />
                </div>
            </div>
        </CustomerInsightsDiamondChartstyle>
    )
}

export default CustomerInsightsGemstoneChart
