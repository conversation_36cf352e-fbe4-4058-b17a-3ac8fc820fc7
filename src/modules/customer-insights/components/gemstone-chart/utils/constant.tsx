import { ShapeIcon1 } from "@/assets/icons/collection/shape-icons/ShapeIcon1";
import { ShapeIcon2 } from "@/assets/icons/collection/shape-icons/ShapeIcon2";
import { ShapeIcon3 } from "@/assets/icons/collection/shape-icons/ShapeIcon3";
import { ShapeIcon4 } from "@/assets/icons/collection/shape-icons/ShapeIcon4";
import { ShapeIcon5 } from "@/assets/icons/collection/shape-icons/ShapeIcon5";
import { ShapeIcon6 } from "@/assets/icons/collection/shape-icons/ShapeIcon6";

export const actionitemList = [
    {
        key: 1,
        value: 'Edit',
        class: 'black',
    },
    {
        key: 2,
        value: 'Remove',
        class: 'red',
    },
];

export const items = {
    dropId: 'super-action'
};

export const diamondStoneData = [
    {
        index: 0,
        icon: <ShapeIcon1 />,
        name: 'Round',
    },
    {
        index: 1,
        icon: <ShapeIcon5 />,
        name: 'Oval',
    },
    {
        index: 2,
        icon: <ShapeIcon2 />,
        name: 'Princess',
    },
    {
        index: 3,
        icon: <ShapeIcon3 />,
        name: 'Emerald',
        status: 'active'
    },
    {
        index: 4,
        icon: <ShapeIcon4 />,
        name: 'Asscher',
    },
    {
        index: 5,
        icon: <ShapeIcon5 />,
        name: 'Oval',
    },
    {
        index: 6,
        icon: <ShapeIcon6 />,
        name: 'Princess',
    },
];

export const colorTabData: any = [
    {
        index: 0,
        label: "Color",
        path: ''
    },
    {
        index: 1,
        label: "Fancy color",
        path: ''
    },
];

export const fancyColorData = [
    {
        index: 0,
        img: '/crm/assets/img/color-1_40.png',
        name: 'Pink',
    },
    {
        index: 1,
        img: '/crm/assets/img/color-2_40.png',
        name: 'Very light yellow',
    },
    {
        index: 2,
        img: '/crm/assets/img/color-3_40.png',
        name: 'Light yellow',
    },
    {
        index: 3,
        img: '/crm/assets/img/color-4_40.png',
        name: 'NFYE',
    },
    {
        index: 4,
        img: '/crm/assets/img/color-5_40.png',
        name: 'Fancy yellow',
    },
    {
        index: 5,
        img: '/crm/assets/img/color-6_40.png',
        name: 'FBGY',
    },
];


export const assetsListingHead = [
    {
        label: 'Group name',
        key: 'groupName'
    },
    {
        label: 'Customer',
        key: 'customer'
    },
    {
        label: 'Commodity',
        key: 'commodity',
    },
    {
        label: 'Shape',
        key: 'shape',
    },
    {
        label: 'Color',
        key: 'color',
    },
    {
        label: 'Clarity',
        key: 'clarity'
    },
    {
        label: 'Chart base',
        key: 'chartBase'
    },
    {
        label: 'Sieve size',
        key: 'sieveSize'
    },
    {
        label: 'Lot code',
        key: 'lotCode'
    },
    {
        label: 'Per weight',
        key: 'perWeight'
    },
    {
        label: 'Inter. quality',
        key: 'interQuality',
    },
    {
        label: 'SP rate',
        key: 'spRate',
    },
    {
        label: 'Updated by',
        key: 'updatedBy',
    },
    {
        label: 'Updated on',
        key: 'updatedOn',
    },
    {
        label: 'Action',
        key: 'action',
        class: 'action'
    },

];

export const assetsListingData = [
    {
        groupName: 'DERM93XSQ-107397',
        customer: '14KWH',
        commodity: 'Diamond',
        shape: 'Round',
        color: 'H',
        clarity: 'VVS2',
        chartBase: 'Sieve size',
        sieveSize: '0000-2',
        lotCode: 'KR1002',
        perWeight: '0.02',
        interQuality: 'R12',
        spRate: '250.00',
        updatedByIcon: 'https://encrypted-tbn1.gstatic.com/images?q=tbn:ANd9GcTSsp8ZcUG7qn6RccmY8zBJN3aYRbo-zY12UJbvLfncoQM_baKp',
        updatedBy: 'Andrew Lewis',
        updatedOn: '23 Mar, 2023 - 10:00 PM',
    },
];

export const dataitem = [
    {
        value: "0",
        content: "Natural Diamonds"
    },
    {
        value: "1",
        content: "Loose Diamonds"
    },
];