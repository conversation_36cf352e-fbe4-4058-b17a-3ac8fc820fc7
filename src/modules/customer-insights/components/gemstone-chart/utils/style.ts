import { b2xs, bes } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const CustomerInsightsGemstoneChartstyle = styled.div`
    ${(props) =>
        css`
            .top_right_search{
                margin-bottom: 24px;
                display: flex;
                align-items: center;
                justify-content: flex-start;
                & > *{
                    margin-right: 24px;
                    &:last-child{
                        margin-right: 0;
                    }
                }
                .search-box{
                    width: 100%;
                    max-width: 1224px;
                }
                .inner_search{
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    .border-btn{
                        padding: 0 12px;
                        white-space: nowrap;
                    }
                    & > * {
                        margin-right: 16px;
                        &:last-child{
                            margin-right: 0;
                        }
                    }
                }
                & > .fill-btn{
                    min-width: 112px;
                }
            }
            .top_diamonds_wpr{
                margin-bottom: 40px;
                .btn_group_wpr{
                    margin-bottom: 40px;
                    max-width: 528px;
                    margin-left: auto;
                    margin-right: auto;
                }
                .color_wpr_main{
                    .shape_wpr{
                        & > p{
                            display: flex;
                            justify-content: flex-start;
                            align-items: center;
                            margin-bottom: 18px;
                            ${bes(props?.theme?.text?.high, 500)};
                            .info_wpr{
                                margin-left: 8px;
                            }
                        }
                        .swiper_main_wpr{
                            width: 100%;
                            position: relative;
                            .swiper-button{
                                width: 20px;
                                height: 20px;
                                &.next{
                                    position: absolute;
                                    top: 10px;
                                    right: -20px;
                                    &.disabled{
                                        display: none;
                                    }
                                }
                                &.prev{
                                    position: absolute;
                                    top: 10px;
                                    left: -20px;
                                    &.disabled{
                                        display: none;
                                    }
                                }
                            }
                        }
                    }
                    .color_wpr{
                         & > p{
                            display: flex;
                            justify-content: flex-start;
                            align-items: center;
                            margin-bottom: 18px;
                            ${bes(props?.theme?.text?.high, 500)};
                            .info_wpr{
                                margin-left: 8px;
                            }
                        }
                        .swiper_main_wpr{
                            width: 100%;
                            position: relative;
                            .swiper-button{
                                width: 20px;
                                height: 20px;
                                &.next{
                                    position: absolute;
                                    top: 10px;
                                    right: -29px;
                                    &.disabled{
                                        display: none;
                                    }
                                }
                                &.prev{
                                    position: absolute;
                                    top: 10px;
                                    left: -29px;
                                    &.disabled{
                                        display: none;
                                    }
                                }
                            }
                            .shape_box{
                                text-align: center;
                                img , svg {
                                    width: 40px;
                                    height: 40px;
                                    margin-left: auto;
                                    margin-right: auto;
                                    path{
                                        stroke: ${(props.theme.colors.brand_high)};
                                    }
                                }
                                .title{
                                    margin-top: 9px;
                                    ${b2xs(props?.theme?.text?.high, 400)};
                                    white-space: nowrap;
                                }
                            }
                        }
                    }
                }
            }
            .top_select_wpr{
                margin-bottom: 40px;
                display: flex;
                align-items: flex-end;
                justify-content: flex-start;
                .left_select_wpr{
                    width: calc(100% - 120px);
                    padding-right: 24px;
                }
                .search_btn_right{
                    min-width: 120px;
                    .fill-btn{
                        width: 100%;
                    }
                }
                .sub_item{
                    margin-top: 8px;
                    ${b2xs(props?.theme?.text?.high, 400)};
                }
            }
            .contacts_data_table{
                .icon_name_24_8_bes{
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;

                    img, svg{
                        width: 24px;
                        height: 24px;
                    }
                    p{
                        padding-left: 8px;
                        width: calc(100% - 24px);
                        ${bes(props?.theme?.text?.high, 400)};
                    }
                }
                .action_wpr{
                    a{
                        display: block;
                        ${bes(props?.theme?.text?.high, 500)};
                    }
                }
                .data_footer_info{
                    margin-top: 24px;
                }
            }
            
        `
    }

`