"use client"
import { FilterLineIcon } from "@/assets/icons/collection/FilterLineIcon";
import { DataTable, DataTableExpand, FilterDropdown, TableFooter, TableHeader } from "@dilpesh/kgk-ui-library";
import { useGetSalesList, useGetTranslation, useModal, usePagination, usePayloadFilter } from "@magneto-it-solutions/kgk-common-library";
import { useParams } from "next/navigation";
import { useRef, useState } from "react";
import CustomerInsightsSalesTrackingModal from "../modal/sales-tracking-modal/sales-tracking-modal";
import { stoneCardHead } from "./utils/constant";
import { CustomerInsightsSalesstyle } from "./utils/style";
import { coloumnDefStoneInner, getSalesColumnDef } from "./utils/utility";

const CustomerInsightsSales = () => {
  const { translation } = useGetTranslation();
  const params = useParams();
  const id: string = String(params?.id);

  const {
    selectedFilter,
    search,
    changeSearch,
    appliedFilters,
    clearFilters,
    applyFilters,
    handleFilterChange,
  } = usePayloadFilter("sales-list");

  const { page, limit, view, changeLimit, changePage } =
    usePagination("sales-list");

  const { salesList, isLoading } = useGetSalesList(id);
  // const { data: memoFilter } = useGetMemoFiltersQuery({ customer_code: id });

  const [filterStatus, setFilterStatus] = useState<boolean>(false);
  const [cipiModalOpen, setCipiModalOpen] = useState(false);

  const ref: any = useRef();
  const addressRef = useRef<any>(null);

  const { open: addSaleModalOpen } = useModal(null, "custom-modal");

  const onPrintInvoive = async (id: any) => {
    // const editData: any = await getAddress(id);
    // setCipiModalOpen(true);

    addSaleModalOpen({
      title: "Track order",
      width: 576,
      wrapClassName: "detail_modal",
      approveBtnText: "Save",
      body: <CustomerInsightsSalesTrackingModal />,
      approveBtnEvent: () => {
        if (addressRef.current) {
          addressRef.current.submit();
        }
      }

    })

  }

  const coloumnDefListing = getSalesColumnDef({ isLoading, onPrintInvoive });
  const coloumnDefDetailsListing = coloumnDefStoneInner({ isLoading });

  return (
    <>
      <CustomerInsightsSalesstyle>
        <div className="container first_view_content">
          <div className="table_main_wpr">
            <TableHeader
              isSearch={true}
              skeleton={isLoading}
              searchPlaceholder="Search sales"
              borderBtnText={translation?.filter}
              borderBtnIcon={<FilterLineIcon />}
              searchText={search}
              onSearchChange={(e: any) =>
                changeSearch(e.target.value.toLowerCase())
              }
              borderBtnClick={() => setFilterStatus(!filterStatus)}
            />
            <FilterDropdown
              open={filterStatus}
              close={() => setFilterStatus(!filterStatus)}
              // data={memoFilter?.filters}
              onChangeFilter={(filter, value) => {
                handleFilterChange(filter, value);
              }}
              selectedFilter={selectedFilter}
              onApplyFilter={applyFilters}
              onClearFilter={clearFilters}
              fetchOptions={(_, searchText) => { }}
              clearAllBtnText={translation.ClearAll}
              applyBtntext={translation.Apply}
              filterText={translation.Filter}
            />
            <DataTableExpand
              isMultiselect={false}
              column={stoneCardHead}
              coloumnDef={coloumnDefListing}
              data={salesList?.data}
              skeleton={isLoading}
              collapsibleData={(item) => {
                return (
                  <td colSpan={stoneCardHead.length + 2} className='stone_card_expanded_td'>
                    <DataTable
                      isMultiselect={false}
                      coloumnDef={coloumnDefDetailsListing}
                      data={Array(item)}
                      skeleton={isLoading}
                    />
                  </td>
                )
              }}
              expandIconPosition=""
              ref={ref}
              fixedPosition="right"
            />
            <TableFooter
              skeleton={isLoading}
              currentPageSize={limit}
              currentPage={page}
              recordsTotal={salesList?.filteredRecords}
              setCurrentPage={changePage}
              setCurrentPageSize={changeLimit}
              showingText={translation.Showing}
              showLabel={translation.Show}
              OfText={translation.Of}
            />
          </div>
        </div>
      </CustomerInsightsSalesstyle>
      {/* <CustomerInsightsSalesTrackingModal
                onHide={() => { setCipiModalOpen(false) }}
                show={cipiModalOpen}
                approveBtnEvent={() => { }}
                rejectBtnEvent={() => { setCipiModalOpen(false) }}
            /> */}
    </>
  )
}

export default CustomerInsightsSales
