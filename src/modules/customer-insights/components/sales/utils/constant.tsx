import { CheckIcon } from "@/assets/icons/collection/CheckIcon";

export const stoneCardHead = [
    {
        label: 'Invoice number',
        key: 'invoiceNumber'
    },
    {
        label: 'Reference number',
        key: 'referenceNumber'
    },
    {
        label: 'Invoice date',
        key: 'date'
    },
    {
        label: 'Customer name',
        key: 'customerId'
    },
    {
        label: 'SO number',
        key: 'voucherStatus'
    },
    {
        label: 'Quantity',
        key: 'quantity',
        class: 'center',
    },
    {
        label: 'Amount',
        key: 'Amount'
    },
    {
        label: 'Transaction type',
        key: 'transactionType'
    },
    {
        label: 'Settlement status',
        key: 'settlementStatus'
    },
    {
        label: 'Action',
        key: 'action',
        class: 'action center'
    },
];

export const stoneCardData = [
    {
        invoiceImg: <CheckIcon />,
        invoiceNumber: 'DSC572271',
        referenceNumber: 'Vegas Proposal/AG',
        date: '23 Mar, 2023 - 10:00 PM',
        customerId: '01EC2487',
        voucherStatus: 'ACC_DONE',
        quantity: '5',
        Amount: '22,322.00',
        transactionType: 'Memo',
        settlementStatus: 'Fully settled',
        action: 'Print'
    },
    {
        invoiceImg: <CheckIcon />,
        invoiceNumber: 'DSC572271',
        referenceNumber: 'Vegas Proposal/AG',
        date: '23 Mar, 2023 - 10:00 PM',
        customerId: '01EC2487',
        voucherStatus: 'ACC_DONE',
        quantity: '5',
        Amount: '22,322.00',
        transactionType: 'Memo',
        settlementStatus: 'Fully settled',
        action: 'Print'
    },
    {
        invoiceImg: <CheckIcon />,
        invoiceNumber: 'DSC572271',
        referenceNumber: 'Vegas Proposal/AG',
        date: '23 Mar, 2023 - 10:00 PM',
        customerId: '01EC2487',
        voucherStatus: 'ACC_DONE',
        quantity: '5',
        Amount: '22,322.00',
        transactionType: 'Memo',
        settlementStatus: 'Fully settled',
        action: 'Print'
    },
    {
        invoiceImg: <CheckIcon />,
        invoiceNumber: 'DSC572271',
        referenceNumber: 'Vegas Proposal/AG',
        date: '23 Mar, 2023 - 10:00 PM',
        customerId: '01EC2487',
        voucherStatus: 'ACC_DONE',
        quantity: '5',
        Amount: '22,322.00',
        transactionType: 'Memo',
        settlementStatus: 'Fully settled',
        action: 'Print'
    },
    {
        invoiceImg: <CheckIcon />,
        invoiceNumber: 'DSC572271',
        referenceNumber: 'Vegas Proposal/AG',
        date: '23 Mar, 2023 - 10:00 PM',
        customerId: '01EC2487',
        voucherStatus: 'ACC_DONE',
        quantity: '5',
        Amount: '22,322.00',
        transactionType: 'Memo',
        settlementStatus: 'Fully settled',
        action: 'Print'
    },
    {
        invoiceImg: <CheckIcon />,
        invoiceNumber: 'DSC572271',
        referenceNumber: 'Vegas Proposal/AG',
        date: '23 Mar, 2023 - 10:00 PM',
        customerId: '01EC2487',
        voucherStatus: 'ACC_DONE',
        quantity: '5',
        Amount: '22,322.00',
        transactionType: 'Memo',
        settlementStatus: 'Fully settled',
        action: 'Print'
    },

];


export const stoneInnerTableHead = [
    {
        label: 'Contract no',
        key: 'contractNo'
    },
    {
        label: 'Style no',
        key: 'styleNo'
    },
    {
        label: 'Description',
        key: 'description'
    },
    {
        label: 'Dia/CS Stamping',
        key: 'diaStamping'
    },
    {
        label: 'Dia Quality',
        key: 'diaQuality'
    },
    {
        label: 'Quantity',
        key: 'quantity'
    },
    {
        label: 'Rate R',
        key: 'rateR'
    },
    {
        label: 'Amount R',
        key: 'amountR'
    },
];

export const stoneInnerData = [
    {
        contractNo: 'MF/098522',
        styleNo: 'DWBSP1Q-1.00-107524',
        description: '',
        diaStamping: '',
        diaQuality: 'null, CUSDIA, NA',
        quantity: '1',
        rateR: '$2,300.00',
        amountR: '$1,943.00',
    },

]
