import { CopyIcon, PrintIcon, TrackOrderIcon } from "@/assets/icons";
import { Skeleton } from "@dilpesh/kgk-ui-library";
import Link from "next/link";

export const getSalesColumnDef = ({ isLoading, onPrintInvoive }) => {
  return {
    action: {
      renderChildren: (item: any) => (
        <div className="action_wpr">
          <div onClick={() => onPrintInvoive(item)}>
            <TrackOrderIcon />
          </div>
          <Link href="#">
            <CopyIcon />
          </Link>
          <div>
            <PrintIcon />
          </div>
        </div>
      ),
    },
    invoiceNumber: {
      renderChildren: (item: any) => (
        <div className="icon_16_bes">
          {isLoading ? (
            <Skeleton width="140px" />
          ) : (
            <>
              <p>{item.invoice_no || "-"}</p>
              {item.invoiceImg}
            </>
          )}
        </div>
      ),
    },
    referenceNumber: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : <p>{item.reference_number || "-"}</p>,
    },
    date: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : <p>{item.invoice_date || "-"}</p>,
    },
    customerId: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : <p>{item.customer_code || "-"}</p>,
    },
    voucherStatus: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : <p>{item.voucherStatus || "-"}</p>,
    },
    quantity: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : <p>{item.qty || "-"}</p>,
    },
    Amount: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : <p>{item.total_amount || "-"}</p>,
    },
    transactionType: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : <p>{item.transactionType || "-"}</p>,
    },
    settlementStatus: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : <p>{item.settlement_status || "-"}</p>,
    },
  };
};

export const coloumnDefStoneInner = ({ isLoading }) => {
  return {
    styleNo: {
      renderChildren: (item: any) => (
        <div className="icon_name_24_bes">
          {isLoading ? <Skeleton width="100px" /> : <p>{item.style_no || "-"}</p>}
        </div>
      ),
    },
    contractNo: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : <p>{item?.contract_no_sku_no || "-"}</p>,
    },
    description: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="120px" /> : <p>{item?.description || "-"}</p>,
    },
    diaStamping: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : <p>{item?.diaStamping || "-"}</p>,
    },
    diaQuality: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : <p>{item?.diaQuality || "-"}</p>,
    },
    quantity: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="80px" /> : <p>{item?.qty || "-"}</p>,
    },
    rateR: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="80px" /> : <p>{item?.rateR || "-"}</p>,
    },
    amountR: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="80px" /> : <p>{item?.amountR || "-"}</p>,
    },
  };
};
