"use client"
import { Btn, CheckboxLabel, InputGroups, Label, SelectGroups } from "@dilpesh/kgk-ui-library";
import { Col, Row } from "antd";
import { CustomerInsightsOtherstyle } from "./utils/style";
import { Controller, useForm } from "react-hook-form";

interface OtherInfoData {
    Number_of_stores: string;
    Google_name: string;
    JBT_number: string;
    Source: string;
    Website: string;
    EDI_partner_id: string;
    Badge: string;
    Buyer_group: string;
    Special_requirements: string;
}


const CustomerInsightsOther = () => {

    const {
        handleSubmit,
        formState: { errors },
        reset,
        setValue,
        control,
    } = useForm<OtherInfoData>({

        mode: "onSubmit",
        reValidateMode: "onChange",
    });

    const onSubmit = async (data: any) => {

        let res: OtherInfoData = {
            Number_of_stores: data?.Number_of_stores,
            Google_name: data?.Google_name,
            JBT_number: data?.JBT_number,
            Source: data?.Source,
            Website: data?.Website,
            EDI_partner_id: data?.EDI_partner_id,
            Badge: data?.Badge,
            Buyer_group: data?.Buyer_group,
            Special_requirements: data?.Special_requirements,
        }

        console.log(res)


        reset({
            Number_of_stores: '',
            Google_name: '',
            JBT_number: '',
            Source: '',
            Website: '',
            EDI_partner_id: '',
            Badge: '',
            Buyer_group: '',
            Special_requirements: '',
        })

    }
    return (
        <CustomerInsightsOtherstyle>
            <form onSubmit={handleSubmit(onSubmit)}>
                <div className="container first_view_content">
                    <div className="other_data_wpr">
                        <div className="multiple_inp_wpr">
                            <Row gutter={{ md: 24 }} className="field_gap_24">
                                <Col md={12} lg={8} xl={6}>
                                    <Controller
                                        control={control}
                                        name="Number_of_stores"
                                        rules={{
                                            required: { value: true, message: "Number is Required" },
                                            pattern: { value: /^[0-9]+$/, message: "Please enter only numbers." }
                                        }}
                                        render={({ field: { value, onChange } }) => {
                                            return <InputGroups
                                                label="Number of stores"
                                                value={value}
                                                onChange={onChange}
                                                error={errors && errors.Number_of_stores?.message}

                                            />
                                        }}
                                    />
                                </Col>
                                <Col md={12} lg={8} xl={6}>
                                    <Controller
                                        control={control}
                                        name="Google_name"
                                        rules={{
                                            required: { value: true, message: "Google name is Required" },
                                        }}
                                        render={({ field: { value, onChange } }) => {
                                            return <InputGroups
                                                label="Google name"
                                                value={value}
                                                onChange={onChange}
                                                error={errors.Google_name && errors.Google_name.message}
                                            />
                                        }}
                                    />

                                </Col>
                                <Col md={12} lg={8} xl={6}>
                                    <Controller
                                        control={control}
                                        name="JBT_number"
                                        rules={{
                                            required: { value: true, message: "  number is required" },
                                        }}
                                        render={({ field: { value, onChange } }) => {
                                            return <InputGroups
                                                label="JBT number"
                                                value={value}
                                                onChange={onChange}
                                                error={errors.JBT_number && errors.JBT_number.message}
                                            />
                                        }}
                                    />

                                </Col>
                                <Col md={12} lg={8} xl={6}>
                                    <Controller
                                        control={control}
                                        name="Source"
                                        rules={{
                                            required: { value: true, message: "Source is required" }
                                        }}
                                        render={({ field: { value, onChange } }) => {
                                            return <InputGroups
                                                label="Source"
                                                value={value}
                                                onChange={onChange}
                                                error={errors.Source && errors.Source.message}
                                            />
                                        }}
                                    />
                                </Col>
                                <Col md={12} lg={8} xl={6}>
                                    <Controller
                                        control={control}
                                        name="Website"
                                        rules={{
                                            required: { value: true, message: "Website is required" }
                                        }}
                                        render={({ field: { value, onChange } }) => {
                                            return <InputGroups
                                                label="Website"
                                                value={value}
                                                onChange={onChange}
                                                error={errors.Website && errors.Website.message}

                                            />
                                        }}
                                    />

                                </Col>
                                <Col md={12} lg={8} xl={6}>
                                    <Controller
                                        control={control}
                                        name="Badge"
                                        rules={{
                                            required: { value: true, message: "Badge is required" }
                                        }}
                                        render={({ field: { value, onChange } }) => {
                                            return <SelectGroups
                                                label="Badge"
                                                value={value}
                                                onChange={onChange}
                                                options={[
                                                    { label: 'Emerald', value: 'Emerald' },
                                                    { label: 'Sapphire', value: 'Sapphire' },
                                                    { label: 'Diamond', value: 'Diamond' },
                                                    { label: 'Ruby', value: 'Ruby' },
                                                ]}
                                                error={errors.Badge && errors.Badge.message}
                                            />
                                        }}
                                    />

                                </Col>
                                <Col md={12} lg={8} xl={6}>
                                    <Controller
                                        control={control}
                                        name="EDI_partner_id"
                                        rules={{
                                            required: { value: true, message: "EDI partner id is required" }
                                        }}
                                        render={({ field: { value, onChange } }) => {
                                            return <InputGroups
                                                label="EDI partner id"
                                                value={value}
                                                onChange={onChange}
                                                error={errors.EDI_partner_id && errors.EDI_partner_id.message}
                                            />
                                        }}
                                    />
                                </Col>
                                <Col md={12} lg={8} xl={6}>
                                    <Controller
                                        control={control}
                                        name="Buyer_group"
                                        rules={{
                                            required: { value: true, message: "Buyer group is required" }
                                        }}
                                        render={({ field: { value, onChange } }) => {
                                            return <SelectGroups
                                                label="Buyer group"
                                                value={value}
                                                onChange={onChange}
                                                options={[
                                                    { label: 'AAA', value: 'AAA' },
                                                    { label: 'BBB', value: 'BBB' },
                                                    { label: 'CCC', value: 'CCC' },

                                                ]}
                                                error={errors.Buyer_group && errors.Buyer_group.message}
                                            />
                                        }}
                                    />
                                </Col>
                                <Col md={12} lg={8} xl={6}>
                                    <Controller
                                        control={control}
                                        name="Special_requirements"
                                        rules={{
                                            required: { value: true, message: "Special requirements is required" }
                                        }}
                                        render={({ field: { value, onChange } }) => {
                                            return <InputGroups
                                                label="Special requirements"
                                                value={value}
                                                onChange={onChange}
                                                error={errors.Special_requirements && errors.Special_requirements.message}
                                            />
                                        }}
                                    />
                                </Col>
                            </Row>
                        </div>
                        <div className="multiple_check_wpr">
                            <div className="row_gap_16">
                                <CheckboxLabel>Co-op applicable</CheckboxLabel>
                                <CheckboxLabel>Authorized dealer</CheckboxLabel>
                                <CheckboxLabel>Spiff applicable</CheckboxLabel>
                                <CheckboxLabel>BIG-API applicable</CheckboxLabel>
                                <CheckboxLabel>Is target</CheckboxLabel>
                                <CheckboxLabel>Forevermark applicable</CheckboxLabel>
                            </div>
                        </div>
                        <div className="this_btn_wpr">
                            <Btn bg="text" size="large" type="reset">Cancel</Btn>
                            <Btn bg="fill" size="large" type="submit">Save</Btn>
                        </div>
                    </div>
                </div>
            </form>
        </CustomerInsightsOtherstyle>
    )
}

export default CustomerInsightsOther
