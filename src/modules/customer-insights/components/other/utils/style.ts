import { Media } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const CustomerInsightsOtherstyle = styled.div`
    ${(props) =>
        css`
            
            .other_data_wpr{
                .multiple_inp_wpr{
                    
                }
                .multiple_check_wpr{
                    margin-top: 24px;
                    overflow: hidden;
                    .row_gap_16{
                        display: flex;
                        flex-wrap: wrap;
                        margin-left: -20px;
                        margin-right: -20px;
                        margin-bottom: -16px;
                        @media ${Media.tablet}{
                            margin-left: -12px;
                            margin-right: -12px;
                        }
                        & > *{
                            margin-left: 20px;
                            margin-right: 20px;
                            margin-bottom: 16px;
                            @media ${Media.tablet}{
                                margin-left: 12px;
                                margin-right: 12px;
                            }
                        }
                    }
                }
                .this_btn_wpr{
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                    margin-top: 32px;
                    border-top: 1px solid ${(props) => props?.theme?.line?.light};
                    padding-top: 32px;
                    /* @media ${Media.tablet}{
                        margin-top: 24px;
                        padding-top: 24px;
                    } */
                    & > * {
                        min-width: 120px;
                        margin-right: 16px;
                        &:last-child{
                            margin-right: 0;
                        }
                    }
                }
            }
            
        `
    }

`