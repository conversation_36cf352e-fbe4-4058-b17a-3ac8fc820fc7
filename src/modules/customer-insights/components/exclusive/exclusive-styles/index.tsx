import { DataTable, TableFooter } from "@dilpesh/kgk-ui-library";
import { useState } from "react";
import { assetsListingData, assetsListingHead } from "./utils/constant";
import { CustomerInsightsExclusiveStylesstyle } from "./utils/style";


const CustomerInsightsExclusiveStyles = () => {


    const [currentPage, setCurrentPage] = useState(1);
    const [currentPageSize, setCurrentPageSize] = useState(25);
    const [recordsTotal, setRecordsTotal] = useState(500);

    //**customer-insights-data-table-expand**/

    
    const coloumnDefAssetsListing: Object = {
        customer: {
            renderChildren: (item: any) => {
                return (
                    <div className='icon_name_24_bes'>
                        <img src={item.customerIcon} alt="" />
                        <p>{item.customer}</p>
                    </div>
                );
            },
        },
        styleNumber: {
            renderChildren: (item: any) => {
                return (
                    <div className='bes_500_wpr'>
                        <p>{item.styleNumber}</p>
                    </div>
                );
            },
        },
    };

    
    return (
        <CustomerInsightsExclusiveStylesstyle>
            <DataTable
                isMultiselect={false}
                column={assetsListingHead}
                coloumnDef={coloumnDefAssetsListing}
                data={assetsListingData}
                fixedPosition="left"
            />
            <TableFooter
                currentPageSize={currentPageSize}
                currentPage={currentPage}
                recordsTotal={recordsTotal}
                setCurrentPage={setCurrentPage}
                setCurrentPageSize={setCurrentPageSize}
                setRecordsTotal={setRecordsTotal}
            />
        </CustomerInsightsExclusiveStylesstyle>
    )
}

export default CustomerInsightsExclusiveStyles
