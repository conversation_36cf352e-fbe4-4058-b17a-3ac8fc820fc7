"use client"
import { DownIcon } from "@/assets/icons/collection/DownIcon";
import { FilterLineIcon } from "@/assets/icons/collection/FilterLineIcon";
import { TableHeader } from "@dilpesh/kgk-ui-library";
import { useGetTranslation } from "@magneto-it-solutions/kgk-common-library";
import { Col, Dropdown, Row } from "antd";
import Link from "next/link";
import { useState } from "react";
import CustomerInsightsExclusiveConcepts from "./exclusive-concepts";
import CustomerInsightsExclusiveDesigns from "./exclusive-designs";
import CustomerInsightsExclusiveStyles from "./exclusive-styles";
import { tabLinks } from "./utils/constant";
import { CustomerInsightsExclusivestyle } from "./utils/style";



const CustomerInsightsExclusive = () => {
  const { translation } = useGetTranslation();

  const [activeTab, setActiveTab] = useState(tabLinks[0].link)

  const viewRender = () => {
    switch (activeTab) {
      case 'concept':
        return <CustomerInsightsExclusiveConcepts />

      case 'designs':
        return <CustomerInsightsExclusiveDesigns />

      case 'styles':
        return <CustomerInsightsExclusiveStyles />
    }
  }
  return (
    <CustomerInsightsExclusivestyle>
      <div className="container first_view_content">
        <div className="top_main_menu_wpr" id="top_main_menu_wpr">
          <Dropdown
            trigger={['click']}
            placement="bottomRight"
            getPopupContainer={() => document?.getElementById('top_main_menu_wpr') as HTMLElement}
            dropdownRender={() => (
              <div className="ant-dropdown-menu">
                <ul>
                  <li className="list_item active"><Link href={'#'}>{translation?.concept}</Link></li>
                  <li className="list_item"><Link href={'#'}>{translation?.designs}</Link></li>
                  <li className="list_item"><Link href={'#'}>{translation?.styles}</Link></li>
                </ul>
              </div>
            )}
          >
            <div>
              <span>{translation?.concept}</span>
              <div className="down_icon"><DownIcon /></div>
            </div>
          </Dropdown>
        </div>
        <Row gutter={{ lg: 24 }}>
          <Col sm={24} lg={5} xl={4} className='hide_in_tablet'>
            <div className='exclusive_sidebar'>
              <ul>
                {
                  tabLinks?.map((item: any, index: number) => (
                    <li
                      key={index}
                      className={`list_item ${activeTab == item.link ? 'active' : ''}`}
                    >
                      <a onClick={() => setActiveTab(item.link)}>{item.name}</a>
                    </li>
                  ))
                }
              </ul>
            </div>
          </Col>
          <Col sm={24} lg={19} xl={20}>
            <div className="table_main_wpr">
              <TableHeader
                searchPlaceholder={translation?.search_customer_code}
                borderBtnText={translation?.filter}
                borderBtnIcon={<FilterLineIcon />}
                isSearch={true}
              />
              {viewRender()}
            </div>
          </Col>
        </Row>
      </div>
    </CustomerInsightsExclusivestyle >
  )
}

export default CustomerInsightsExclusive
