"use client"
import { TableHeader } from "@dilpesh/kgk-ui-library";
import { Col, Dropdown, Row } from "antd";
import Link from "next/link";
import { useState } from "react";
import { CustomerInsightsExclusivestyle } from "./utils/style";
import { FilterLineIcon } from "@/assets/icons/collection/FilterLineIcon";
import { DownIcon } from "@/assets/icons/collection/DownIcon";
import { tabLinks } from "./utils/constant";
import CustomerInsightsExclusiveConcepts from "./exclusive-concepts";
import CustomerInsightsExclusiveDesigns from "./exclusive-designs";
import CustomerInsightsExclusiveStyles from "./exclusive-styles";



const CustomerInsightsExclusive = () => {



    const [activeTab, setActiveTab] = useState(tabLinks[0].link)

    const viewRender = () => {
        switch (activeTab) {
            case 'concept':
                return <CustomerInsightsExclusiveConcepts />

            case 'designs':
                return <CustomerInsightsExclusiveDesigns />

            case 'styles':
                return <CustomerInsightsExclusiveStyles />
        }
    }
    return (
        <CustomerInsightsExclusivestyle>
            <div className="container first_view_content">
                <div className="top_main_menu_wpr" id="top_main_menu_wpr">
                    <Dropdown
                        trigger={['click']}
                        placement="bottomRight"
                        getPopupContainer={() => document?.getElementById('top_main_menu_wpr') as HTMLElement}
                        dropdownRender={() => (
                            <div className="ant-dropdown-menu">
                                <ul>
                                    <li className="list_item active"><Link href={'#'}>Concept</Link></li>
                                    <li className="list_item"><Link href={'#'}>Designs</Link></li>
                                    <li className="list_item"><Link href={'#'}>Styles</Link></li>
                                </ul>
                            </div>
                        )}
                    >
                        <div>
                            <span>Concept</span>
                            <div className="down_icon"><DownIcon /></div>
                        </div>
                    </Dropdown>
                </div>
                <Row gutter={{ lg: 24 }}>
                    <Col sm={24} lg={5} xl={4} className='hide_in_tablet'>
                        <div className='exclusive_sidebar'>
                            <ul>
                                {
                                    tabLinks?.map((item: any, index: number) => (
                                        <li
                                            key={index}
                                            className={`list_item ${activeTab == item.link ? 'active' : ''}`}
                                        >
                                            <a onClick={() => setActiveTab(item.link)}>{item.name}</a>
                                        </li>
                                    ))
                                }
                            </ul>
                        </div>
                    </Col>
                    <Col sm={24} lg={19} xl={20}>
                        <div className="table_main_wpr">
                            <TableHeader
                                searchPlaceholder="Search customer code"
                                borderBtnText="Filter"
                                borderBtnIcon={<FilterLineIcon />}
                                isSearch={true}
                            />
                            {viewRender()}
                        </div>
                    </Col>
                </Row>
            </div>
        </CustomerInsightsExclusivestyle >
    )
}

export default CustomerInsightsExclusive
