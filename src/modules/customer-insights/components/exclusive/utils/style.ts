import { Media, bs } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const CustomerInsightsExclusivestyle = styled.div`
    ${(props) =>
        css`
            .hide_in_tablet{
                @media ${Media.tablet}{
                    display: none;
                }
            }
            .top_main_menu_wpr{
                @media ${Media.tablet_above}{
                    display: none;
                }
                margin-bottom: 24px;
                .ant-dropdown-trigger{
                    display: flex;
                    justify-content: flex-start;
                    align-items: center;
                    padding: 12px;
                    padding-left: 16px;
                    background-color: ${(props.theme.colors.brand_low)};
                    border-radius: 4px;
                    span{
                        display: block;
                        ${bs(props?.theme?.text?.high, 500)};
                    }
                    .down_icon{
                        height: 24px;
                        height: 24px;
                        margin-left: auto;
                        svg{
                            height: 24px;
                            height: 24px;
                            transform: rotate(0);
                            transition: transform 200ms ease-in;
                            path{
                                stroke: ${(props?.theme?.colors?.brand_high)};
                            }
                        }
                    }
                    &.ant-dropdown-open{
                        .down_icon{
                            svg{
                                transform: rotate(-180deg);
                                transition: transform 200ms ease-in;
                            }
                        }
                    }
                }
                .ant-dropdown{
                    .ant-dropdown-menu{
                        padding: 16px;
                        max-height: calc(100vh - 300px);
                        overflow-y: auto;
                        ul {
                            display: block;
                            li {
                                display: block;
                                margin-bottom: 16px;
                                &:last-child {
                                    margin-bottom: 0;
                                }
                                a {
                                    display: block;
                                    ${bs(props?.theme?.text?.mid, 400)};
                                    transition: all 200ms ease-in;
                                }
                                
                                &:hover,
                                &.active {
                                    a {
                                        color: ${(props) => props?.theme?.text?.high};
                                        transition: all 200ms ease-in;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            .exclusive_sidebar{
                padding: 24px;
                background-color: ${(props) => props?.theme?.colors?.brand_low};
                ul {
                    li {
                        ${bs(props?.theme?.text?.mid, 500)};
                        margin-bottom: 12px;
                        transition: all 300ms ease-in;
                        position: relative;
                        &::after{
                            content: "";
                            background-color: ${(props) => props?.theme?.colors?.brand_high};
                            width: 4px;
                            height: 24px;
                            border-top-right-radius:8px;
                            border-bottom-right-radius:8px;
                            position: absolute;
                            left: -24px;
                            top: 50%;
                            transform: translateY(-50%);
                            opacity: 0;
                            transition: all 300ms ease-in;
                        }
                        &:hover{
                            &::after{
                                opacity: 1;
                            }
                            a {
                                ${bs(props?.theme?.text?.high, 500)};
                                transition: all 300ms ease-in;
                            }
                        }
                        &.active {
                            &::after{
                                opacity: 1;
                            }
                            a {
                                ${bs(props?.theme?.text?.high, 500)};
                                transition: all 300ms ease-in;
                            }
                        }
                        a {
                            display: block;
                            ${bs(props?.theme?.text?.mid, 500)};
                        }
                        &:last-child {
                            margin-bottom: 0;
                        }
                    }
                } 
            }
            .table_main_wpr{
                padding-left: 8px;
                @media ${Media.tablet}{
                    padding-left: 0;
                }
                .table_header_info{
                    margin-bottom: 24px;
                }
            }
            
        `
    }

`