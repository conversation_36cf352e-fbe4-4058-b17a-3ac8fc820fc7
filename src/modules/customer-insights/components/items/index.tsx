"use client"
import { DataTable, TableFooter, TableHeader } from "@dilpesh/kgk-ui-library";
import { useState } from "react";
import { CustomerInsightsItemsstyle } from "./utils/style";
import { FilterLineIcon } from "@/assets/icons/collection/FilterLineIcon";
import { assetsListingData, assetsListingHead } from "./utils/constant";


const CustomerInsightsItems = () => {


    
    const coloumnDefAssetsListing: Object = {
        image: {
            renderChildren: (item: any) => {
                return (
                    <div className='img_32'>
                        {item.image}
                    </div>
                );
            },
        }
    };

    
    const [currentPage, setCurrentPage] = useState(1);
    const [currentPageSize, setCurrentPageSize] = useState(25);
    const [recordsTotal, setRecordsTotal] = useState(500);

    return (
        <CustomerInsightsItemsstyle>
            <div className="container first_view_content">
                <div className="table_main_wpr">
                    <TableHeader
                        searchPlaceholder="Search items"
                        borderBtnText="Filter"
                        borderBtnIcon={<FilterLineIcon />}
                        isSearch={true}
                    />
                    <DataTable
                        isMultiselect={false}
                        column={assetsListingHead}
                        coloumnDef={coloumnDefAssetsListing}
                        data={assetsListingData}
                        bodyClass="body_pt_12"
                    />
                    <TableFooter
                        currentPageSize={currentPageSize}
                        currentPage={currentPage}
                        recordsTotal={recordsTotal}
                        setCurrentPage={setCurrentPage}
                        setCurrentPageSize={setCurrentPageSize}
                        setRecordsTotal={setRecordsTotal}
                    />
                </div>
            </div>
        </CustomerInsightsItemsstyle>
    )
}

export default CustomerInsightsItems
