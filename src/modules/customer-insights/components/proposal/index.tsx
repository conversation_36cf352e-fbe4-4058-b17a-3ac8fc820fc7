"use client";
import { FilterLineIcon } from "@/assets/icons/collection/FilterLineIcon";
import {
  DataTable,
  FilterDropdown,
  TableFooter,
  TableHeader,
} from "@dilpesh/kgk-ui-library";
import {
  useGetProposalFiltersQuery,
  useGetProposalList,
  useGetTranslation,
  usePagination,
  usePayloadFilter
} from "@magneto-it-solutions/kgk-common-library";
import { useParams } from "next/navigation";
import { useState } from "react";
import CustomerInsightsProposalOrderModel from "../modal/proposal-order-model/proposal-order-modal";
import { assetsListingHead } from "./utils/constant";
import { CustomerInsightsProposalstyle } from "./utils/style";
import { getProposalColumnDef } from "./utils/utility";

const CustomerInsightsProposal = () => {
  const { translation } = useGetTranslation();
  const params = useParams();
  const id: string = String(params?.id);

  const {
    selectedFilter,
    search,
    changeSearch,
    appliedFilters,
    clearFilters,
    applyFilters,
    handleFilterChange,
  } = usePayloadFilter("proposal-list");

  const { page, limit, view, changeLimit, changePage } =
    usePagination("proposal-list");

  const { proposals, isLoading } = useGetProposalList(id);
  const { data: proposalFilter } = useGetProposalFiltersQuery({ customer_code: id });

  const [filterStatus, setFilterStatus] = useState<boolean>(false);
  const [cipoModalOpen, setCipoModalOpen] = useState<{ open: boolean, id: string }>({ open: false, id: "" });

  const coloumnDefListing = getProposalColumnDef({ isLoading, setCipoModalOpen });

  return (
    <>
      <CustomerInsightsProposalstyle>
        <div className="container first_view_content">
          <div className="table_main_wpr">
            <TableHeader
              isSearch={true}
              skeleton={isLoading}
              searchPlaceholder={translation?.search_proposal}
              borderBtnText={translation?.filter}
              borderBtnIcon={<FilterLineIcon />}
              searchText={search}
              onSearchChange={(e: any) =>
                changeSearch(e.target.value.toLowerCase())
              }
              borderBtnClick={() => setFilterStatus(!filterStatus)}
            />
            <FilterDropdown
              open={filterStatus}
              close={() => setFilterStatus(!filterStatus)}
              data={proposalFilter?.filters}
              onChangeFilter={(filter, value) => {
                handleFilterChange(filter, value);
              }}
              selectedFilter={selectedFilter}
              onApplyFilter={applyFilters}
              onClearFilter={clearFilters}
              fetchOptions={(_, searchText) => { }}
              clearAllBtnText={translation.ClearAll}
              applyBtntext={translation.Apply}
              filterText={translation.Filter}
            />
            <DataTable
              isMultiselect={false}
              column={assetsListingHead}
              coloumnDef={coloumnDefListing}
              data={proposals?.data}
              skeleton={isLoading}
            />
            <TableFooter
              skeleton={isLoading}
              currentPageSize={limit}
              currentPage={page}
              recordsTotal={proposals?.filteredRecords}
              setCurrentPage={changePage}
              setCurrentPageSize={changeLimit}
              showingText={translation.Showing}
              showLabel={translation.Show}
              OfText={translation.Of}
            />
          </div>
        </div>
      </CustomerInsightsProposalstyle>
      <CustomerInsightsProposalOrderModel
        onHide={() => {
          setCipoModalOpen({ open: false, id: "" });
        }}
        show={cipoModalOpen}
        approveBtnEvent={() => { }}
        rejectBtnEvent={() => {
          setCipoModalOpen({ open: false, id: "" });
        }}
      />
      ,
    </>
  );
};

export default CustomerInsightsProposal;
