import { ViewIcon } from "@/assets/icons";
import { Skeleton } from "@dilpesh/kgk-ui-library";
import { getSessionItem, SESSION_KEYS } from "@magneto-it-solutions/kgk-common-library";
import { format } from "date-fns";

export const getProposalColumnDef = ({ isLoading, setCipoModalOpen }) => {
  return {
    proposalNumber: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="120px" /> : (
          <div className="bes_500_wpr">
            <p>{item?.proposal_number || "-"}</p>
          </div>
        ),
    },
    customer: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : item?.customer_code || "-",
    },
    quantity: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="80px" /> : item?.total_quantity || "-",
    },
    amount: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : item?.total_amount || "-",
    },
    shippingDate: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="150px" /> : (
          item?.shipping_date
            ? format(
              new Date("01-01-2001"),
              getSessionItem(SESSION_KEYS.DATE_FORMAT) || "dd MMM yyyy, h:mm a"
            )
            : "-"
        ),
    },
    status: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : item?.status || "-",
    },
    sONumber: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : item?.so_number || "-",
    },
    customerPONumber: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="120px" /> : item?.customer_po_number || "-",
    },
    createdOn: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="150px" /> : (
          item?.created_on
            ? format(
              new Date(item.created_at),
              getSessionItem(SESSION_KEYS.DATE_FORMAT) || "dd MMM yyyy, h:mm a"
            )
            : "-"
        ),
    },
    createdBy: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="120px" /> : item.created_by || "-",
    },
    view: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="32px" /> : (
          <div className="action_wpr">
            <a onClick={() => setCipoModalOpen({ open: true, id: item?._id })}>
              <ViewIcon />
            </a>
          </div>
        ),
    },
  };
};
