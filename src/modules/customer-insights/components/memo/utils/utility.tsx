import { PrintIcon } from "@/assets/icons";
import { Skeleton } from "@dilpesh/kgk-ui-library";

export const getMemoColumnDef = ({ isLoading, onPrintInvoive }) => {
  return {
    action: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="24px" />
        ) : (
          <div className="action_wpr">
            <a href="javascript:void(0)" onClick={() => onPrintInvoive(item)}>
              <PrintIcon />
            </a>
          </div>
        ),
    },
    invoiceNumber: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="140px" />
        ) : (
          <div className="icon_16_bes">
            <p>{item?.invoice_number || "-"}</p>
            {item?.invoiceImg}
          </div>
        ),
    },
    referenceNumber: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="100px" />
        ) : (
          <p>{item?.reference_number || "-"}</p>
        ),
    },
    customerId: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="100px" />
        ) : (
          <p>{item?.customer_code || "-"}</p>
        ),
    },
    quantity: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="80px" />
        ) : (
          <div className="align_center">
            <p>{item?.quantity || "-"}</p>
          </div>
        ),
    },
    grossAmount: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="100px" />
        ) : (
          <p>{item?.gross_amount || "-"}</p>
        ),
    },
    transactionType: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="100px" />
        ) : (
          <p>{item?.transaction_type || "-"}</p>
        ),
    },
  };
};

export const getMemoDetailColumnDef = ({ isLoading }) => {
  const renderCell = (value: any) =>
    isLoading ? <Skeleton width="120px" /> : <p>{value || "-"}</p>;

  return {
    jobNumber: {
      renderChildren: (item: any) => (
        <div className="icon_name_24_bes">
          {renderCell(item?.jobNumber)}
        </div>
      ),
    },
    styleNo: {
      renderChildren: (item: any) => (
        <div className="icon_name_24_bes">
          {renderCell(item?.StyleNo)}
        </div>
      ),
    },
    description: {
      renderChildren: (item: any) => (
        <div className="icon_name_24_bes">
          {renderCell(item?.description)}
        </div>
      ),
    },
    jid: {
      renderChildren: (item: any) => (
        <div className="icon_name_24_bes">
          {renderCell(item?.jid)}
        </div>
      ),
    },
    quantity: {
      renderChildren: (item: any) => (
        <div className="icon_name_24_bes">
          {renderCell(item?.Qty)}
        </div>
      ),
    },
    rateR: {
      renderChildren: (item: any) => (
        <div className="icon_name_24_bes">
          {renderCell(item?.Rate)}
        </div>
      ),
    },
    amountR: {
      renderChildren: (item: any) => (
        <div className="icon_name_24_bes">
          {renderCell(item?.Amount)}
        </div>
      ),
    },
  };
};
