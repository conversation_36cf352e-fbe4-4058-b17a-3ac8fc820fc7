import { CheckIcon } from "@/assets/icons/collection/CheckIcon";

export const stoneCardHead = [
  {
    label: 'Invoice number',
    key: 'invoiceNumber'
  },
  {
    label: 'Reference number',
    key: 'referenceNumber'
  },
  {
    label: 'Date',
    key: 'date'
  },
  {
    label: 'Customer id',
    key: 'customerId'
  },
  {
    label: 'Quantity',
    key: 'quantity',
    class: 'center'
  },
  {
    label: 'Gross amount',
    key: 'grossAmount',
    class: 'right'
  },
  {
    label: 'Transaction type',
    key: 'transactionType'
  },
  {
    label: 'Action',
    key: 'action',
    class: 'action center'
  },
];

export const stoneCardData = [
  {
    invoiceImg: <CheckIcon />,
    invoiceNumber: 'DSC572271',
    referenceNumber: 'Vegas Proposal/AG',
    date: '23 Mar, 2023 - 10:00 PM',
    customerId: '01EC2487',
    quantity: '5',
    grossAmount: '22,322.00',
    transactionType: 'Memo',
    action: 'Print'
  },
  {
    invoiceImg: <CheckIcon />,
    invoiceNumber: 'DSC572271',
    referenceNumber: 'Vegas Proposal/AG',
    date: '23 Mar, 2023 - 10:00 PM',
    customerId: '01EC2487',
    quantity: '5',
    grossAmount: '22,322.00',
    transactionType: 'Memo',
    action: 'Print'
  },
  {
    invoiceImg: <CheckIcon />,
    invoiceNumber: 'DSC572271',
    referenceNumber: 'Vegas Proposal/AG',
    date: '23 Mar, 2023 - 10:00 PM',
    customerId: '01EC2487',
    quantity: '5',
    grossAmount: '22,322.00',
    transactionType: 'Memo',
    action: 'Print'
  },
  {
    invoiceImg: <CheckIcon />,
    invoiceNumber: 'DSC572271',
    referenceNumber: 'Vegas Proposal/AG',
    date: '23 Mar, 2023 - 10:00 PM',
    customerId: '01EC2487',
    quantity: '5',
    grossAmount: '22,322.00',
    transactionType: 'Memo',
    action: 'Print'
  },
  {
    invoiceImg: <CheckIcon />,
    invoiceNumber: 'DSC572271',
    referenceNumber: 'Vegas Proposal/AG',
    date: '23 Mar, 2023 - 10:00 PM',
    customerId: '01EC2487',
    quantity: '5',
    grossAmount: '22,322.00',
    transactionType: 'Memo',
    action: 'Print'
  },
  {
    invoiceImg: <CheckIcon />,
    invoiceNumber: 'DSC572271',
    referenceNumber: 'Vegas Proposal/AG',
    date: '23 Mar, 2023 - 10:00 PM',
    customerId: '01EC2487',
    quantity: '5',
    grossAmount: '22,322.00',
    transactionType: 'Memo',
    action: 'Print'
  },

];

export const stoneInnerTableHead = [
  {
    label: 'Job number',
    key: 'jobNumber'
  },
  {
    label: 'Style no',
    key: 'styleNo'
  },
  {
    label: 'JID',
    key: 'jid'
  },
  {
    label: 'Quantity',
    key: 'quantity'
  },
  {
    label: 'Rate R',
    key: 'rateR'
  },
  {
    label: 'Amount R',
    key: 'amountR'
  },
];

export const stoneInnerData = [
  {
    jobNumber: 'MF/098522',
    styleNo: 'DWBSP1Q-1.00-107524',
    description: ' ',
    jid: '23K0441L',
    quantity: '1',
    rateR: '$2,300.00',
    amountR: '$1,943.00',
  },
  {
    jobNumber: 'MF/098522',
    styleNo: 'DWBSP1Q-1.00-107524',
    description: ' ',
    jid: '23K0441L',
    quantity: '1',
    rateR: '$2,300.00',
    amountR: '$1,943.00',
  },
  {
    jobNumber: 'MF/098522',
    styleNo: 'DWBSP1Q-1.00-107524',
    description: ' ',
    jid: '23K0441L',
    quantity: '1',
    rateR: '$2,300.00',
    amountR: '$1,943.00',
  },

]
