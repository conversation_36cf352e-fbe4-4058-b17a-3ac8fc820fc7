"use client";

import { FilterLineIcon } from "@/assets/icons/collection/FilterLineIcon";
import {
  DataTable,
  DataTableExpand,
  FilterDropdown,
  TableFooter,
  TableHeader,
} from "@dilpesh/kgk-ui-library";
import {
  useGetMemoDetails,
  useGetMemoFiltersQuery,
  useGetMemoList,
  useGetTranslation,
  useModal,
  usePagination,
  usePayloadFilter,
} from "@magneto-it-solutions/kgk-common-library";
import { useParams } from "next/navigation";
import { useRef, useState } from "react";
import CustomerInsightsPrintInvoiceModal from "../modal/print-invoice-modal/print-invoice-modal";
import {
  stoneCardHead,
  stoneInnerTableHead
} from "./utils/constant";
import { CustomerInsightsMemostyle } from "./utils/style";
import { getMemoColumnDef, getMemoDetailColumnDef } from "./utils/utility";

const CustomerInsightsMemo = () => {
  const { translation } = useGetTranslation();
  const params = useParams();
  const id: string = String(params?.id);

  const {
    selectedFilter,
    search,
    changeSearch,
    appliedFilters,
    clearFilters,
    applyFilters,
    handleFilterChange,
  } = usePayloadFilter("memo-list");

  const { page, limit, view, changeLimit, changePage } =
    usePagination("memo-list");

  const { memos, isLoading } = useGetMemoList(id);
  const { data: memoFilter } = useGetMemoFiltersQuery({ customer_code: id });

  const [filterStatus, setFilterStatus] = useState<boolean>(false);

  const ref: any = useRef();
  const invoiceRef = useRef<any>(null);

  const { open: addMemoModalOpen } = useModal(null, "custom-modal");

  const onPrintInvoive = async (id: any) => {
    addMemoModalOpen({
      title: "Invoice print",
      width: 464,
      wrapClassName: "detail_modal",
      approveBtnText: "Save",
      body: <CustomerInsightsPrintInvoiceModal ref={invoiceRef} />,
      approveBtnEvent: () => {
        if (invoiceRef.current) {
          invoiceRef.current.submit();
        }
      },
    });
  };

  const coloumnDefListing = getMemoColumnDef({ isLoading, onPrintInvoive });

  return (
    <>
      <CustomerInsightsMemostyle>
        <div className="container first_view_content">
          <div className="table_main_wpr">
            <TableHeader
              isSearch={true}
              skeleton={isLoading}
              searchPlaceholder="Search memo"
              borderBtnText={translation?.filter}
              borderBtnIcon={<FilterLineIcon />}
              searchText={search}
              onSearchChange={(e: any) =>
                changeSearch(e.target.value.toLowerCase())
              }
              borderBtnClick={() => setFilterStatus(!filterStatus)}
            />
            <FilterDropdown
              open={filterStatus}
              close={() => setFilterStatus(!filterStatus)}
              data={memoFilter?.filters}
              onChangeFilter={(filter, value) => {
                handleFilterChange(filter, value);
              }}
              selectedFilter={selectedFilter}
              onApplyFilter={applyFilters}
              fetchOptions={(_, searchText) => { }}
              clearAllBtnText={translation.ClearAll}
              applyBtntext={translation.Apply}
              filterText={translation.Filter}
            />
            <DataTableExpand
              isMultiselect={false}
              column={stoneCardHead}
              coloumnDef={coloumnDefListing}
              data={memos?.data}
              skeleton={isLoading}
              collapsibleData={(item) => <MemoDetailsRow invoiceNumber={item?.invoice_number} />}
              ref={ref}
              fixedPosition="right"
            />
            <TableFooter
              skeleton={isLoading}
              currentPageSize={limit}
              currentPage={page}
              recordsTotal={memos?.filteredRecords}
              setCurrentPage={changePage}
              setCurrentPageSize={changeLimit}
              showingText={translation.Showing}
              showLabel={translation.Show}
              OfText={translation.Of}
            />
          </div>
        </div>
      </CustomerInsightsMemostyle>
    </>
  );
};

export default CustomerInsightsMemo;

export const MemoDetailsRow = ({ invoiceNumber }: { invoiceNumber: string }) => {
  const { memoDetails, isLoading } = useGetMemoDetails(invoiceNumber);

  const coloumnDefListing = getMemoDetailColumnDef({ isLoading });

  return (
    <td colSpan={stoneCardHead.length + 2} className="stone_card_expanded_td">
      <DataTable
        isMultiselect={false}
        column={stoneInnerTableHead}
        coloumnDef={coloumnDefListing}
        data={memoDetails?.detail_data}
        skeleton={isLoading}
      />
    </td>
  );
};
