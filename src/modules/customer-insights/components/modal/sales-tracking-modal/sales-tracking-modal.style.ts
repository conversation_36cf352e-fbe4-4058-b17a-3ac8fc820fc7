import { Media, b2xs, bes, br } from "@dilpesh/kgk-ui-library";
import { Modal } from "antd";
import styled, { css } from "styled-components";

export const CustomerInsightsSalesTrackingModalstyle = styled.div`
    ${(props) =>
        css`
            .ant-modal-content{             
                .ant-modal-body{
                    .body{
                        padding-bottom: 32px;
                        @media ${Media.ultra_mobile} {
                            padding-bottom: 24px;
                        }
                        .order_wpr{
                            margin-bottom: 32px;
                            background-color:${(props) => props?.theme?.colors?.brand_low};
                            padding: 16px;
                            @media ${Media.ultra_mobile}{
                                margin-bottom: 24px;
                            }
                            & > p{
                                ${b2xs(props?.theme?.text?.mid, 400)}
                                margin-bottom: 8px;
                            }
                            .product_inner_detail{
                                display: flex;
                                justify-content: space-between;
                                align-items: flex-start;
                                @media ${Media.mobile}{
                                    display: block;
                                }
                                .this_product_wpr{
                                    display: flex;
                                    justify-content: flex-start;
                                    align-items: flex-start;
                                    .this_img{
                                        width: 48px;
                                        height: 48px;
                                    }
                                    .this_content_wpr{
                                        width: calc(100% - 48px);
                                        padding-left: 12px;
                                        @media ${Media.mobile}{
                                            padding-left: 8px;
                                        }
                                        .name_wpr{
                                            p{
                                                display: inline-block;
                                                ${bes(props?.theme?.text?.high, 400)}
                                                span{
                                                    ${bes(props?.theme?.text?.mid, 400)}
                                                }
                                            }
                                        }
                                        .trail_wpr{
                                            margin-top: 4px;
                                            span{
                                                ${b2xs(props?.theme?.text?.mid, 400)}
                                                padding-right:16px;
                                                position: relative;
                                                &::after{
                                                    content: '';
                                                    position: absolute;
                                                    right: 8px;
                                                    top: 50%;
                                                    transform: translateY(-50%);
                                                    width: 1px;
                                                    height: 8px;
                                                    background-color:#E8E8E8;
                                                }
                                                &:last-child{
                                                    padding-right: 0;
                                                    &::after{
                                                        display: none;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                .status_div{
                                    margin-left: 16px;
                                    @media ${Media.mobile}{
                                        margin-top: 8px;
                                        margin-left: 56px;
                                    }
                                }
                            }
                        }
                        .track_order_wpr{
                            .inner_track{
                                padding-left: 32px;
                                position: relative;
                                padding-bottom: 44px;
                                @media ${Media.ultra_mobile}{
                                    padding-left: 24px;
                                    padding-bottom: 24px;
                                }
                                &:last-child{
                                    padding-bottom: 0;
                                }
                                &::after{
                                    content: '';
                                    position: absolute;
                                    width: 16px;
                                    height: 16px;
                                    top: 6px;
                                    left: 0;
                                    background:url(/crm/assets/img/pending-24.svg);
                                    background-position: center;
                                    background-repeat: no-repeat;
                                    background-size: cover;
                                    @media ${Media.ultra_mobile}{
                                        width: 12px;
                                        height: 12px;
                                        top: 8px;
                                    }
                                }
                                &.success{
                                    &::after{
                                        content: '';
                                        position: absolute;
                                        width: 16px;
                                        height: 16px;
                                        top: 6px;
                                        left: 0;
                                        border-radius: 30px;
                                        background:${(props) => props?.theme?.status?.success};
                                        @media ${Media.ultra_mobile}{
                                            width: 12px;
                                            height: 12px;
                                            top: 8px;
                                        }
                                    }
                                    &:before{
                                        content: '';
                                        position: absolute;
                                        width: 0;
                                        height: calc(100% - 32px);
                                        top: 30px;
                                        left: 8px;
                                        border-left: 1px dashed ${(props) => props?.theme?.status?.success};
                                        @media ${Media.ultra_mobile}{
                                            left: 6px;
                                        }
                                    }
                                }
                                p{
                                    ${br(props?.theme?.text?.high, 500)}
                                    margin-right: 8px;
                                    display: inline-block;
                                }
                                span{
                                    ${br(props?.theme?.text?.mid, 400)}
                                }
                            }
                        }
                    }
                }
                .ant-modal-footer{
                    border-top: 0 !important;
                    margin-top: 0 !important;
                    padding-bottom:32px !important;
                    padding-top: 0 !important;
                }
            }   
        `
    }
`