import { TableStatus } from "@dilpesh/kgk-ui-library";
import { CustomerInsightsSalesTrackingModalstyle } from "./sales-tracking-modal.style";
import { useModal } from "@magneto-it-solutions/kgk-common-library";

export default function CustomerInsightsSalesTrackingModal({
    show,
    onHide,
    rejectBtnEvent,
}: any) {

    const { open: open, close, setIsLoading, isLoading } = useModal(null, "custom-modal");

    return (
        <CustomerInsightsSalesTrackingModalstyle>
            <form className="body">
                <div className="order_wpr">
                    <p>Order #FASIL-230096</p>
                    <div className="product_inner_detail">
                        <div className="this_product_wpr">
                            <div className="this_img">
                                <picture>
                                    <source media='(min-width: 1200)' srcSet='/crm/assets/img/ring-48.png' />
                                    <img src='/crm/assets/img/ring-48.png' alt='' />
                                </picture>
                            </div>
                            <div className="this_content_wpr">
                                <div className="name_wpr">
                                    <p>Diamond Vine Ring in 18k Gold  &#160;<span>x15</span></p>
                                </div>
                                <div className='trail_wpr'>
                                    <span>Martin Flyer</span>
                                    <span>DERS01XXSRR</span>
                                </div>
                            </div>
                        </div>
                        <TableStatus className="inprogress">In Shipping</TableStatus>
                    </div>
                </div>
                <div className="track_order_wpr">
                    <div className="inner_track success">
                        <p>Order Placed, </p>
                        <span>23 Mar 2023</span>
                    </div>
                    <div className="inner_track success">
                        <p>Product ready to dispatch,</p>
                        <span>23 Mar 2023</span>
                    </div>
                    <div className="inner_track success">
                        <p>Arrived at Mumbai facility,</p>
                        <span>23 Mar 2023</span>
                    </div>
                    <div className="inner_track">
                        <p>Out for delivery,</p>
                        <span>23 Mar 2023</span>
                    </div>
                </div>
            </form>
         </CustomerInsightsSalesTrackingModalstyle >
    )
}
