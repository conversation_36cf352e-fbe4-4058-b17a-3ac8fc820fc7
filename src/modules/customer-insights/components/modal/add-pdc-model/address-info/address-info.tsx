import { CheckboxLabel, InputGroups, SelectGroups } from "@dilpesh/kgk-ui-library"
import { Col, Row } from "antd"
import { AddressInfostyle } from "./address-info.style"
import { useForm } from "react-hook-form";
import * as Yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { useEffect } from "react";

interface AddressInfo {
    PDC_number: string;
    Status: string;
    Reference_number: string;
    Bank: string;
    PDC_writer_name: string;
}

const AddressInfo = ({ setValidateStep }) => {

    const validationSchema = Yup.object().shape({
        PDC_number: Yup.string()
            .required("PDC number is required"),
        Status: Yup.string()
            .required("Status is required"),
        Reference_number: Yup.string()
            .required('Reference number is required'),
        Bank: Yup.string()
            .required("Bank name is required"),
        PDC_writer_name: Yup.string()
            .required('Write name is required'),
    })

    const { register, handleSubmit, setValue, control, formState: { errors }, reset } = useForm({
        defaultValues: {
        },
        mode: "onSubmit",
        resolver: yupResolver(validationSchema),
    });
    const onSubmit = (data: any) => {
        const Data: AddressInfo = {
            PDC_number: data?.PDC_number,
            Status: data?.Status,
            Reference_number: data?.Reference_number,
            Bank: data?.Bank,
            PDC_writer_name: data?.PDC_writer_name,
        }
    }

    const validateForm = () => {
        return new Promise((resolve) => {
            handleSubmit(
                (data) => resolve({ valid: true, data }),
                () => resolve({ valid: false })
            )();
        });
    };

    useEffect(() => {
        setValidateStep(() => validateForm);
    }, []);

    return (
        <AddressInfostyle>
            <form onSubmit={handleSubmit(onSubmit)}>
                <div className="info_wrapper">
                    <div className="pdc_info_wpr">
                        <p>PDC information</p>
                        <Row gutter={{ sm: 24 }} className="field_gap_24">
                            <Col sm={12}>
                                <InputGroups
                                    label="PDC number"
                                    {...register('PDC_number')}
                                    defaultValue={""}
                                    error={errors.PDC_number && errors.PDC_number.message}
                                />
                            </Col>
                            <Col sm={12}>
                                <SelectGroups
                                    label="Status"
                                    {...register('Status')}
                                    defaultValue={''}
                                    options={[
                                        { label: 'AAA', value: 'AAA' },
                                        { label: 'BBB', value: 'BBB' },
                                        { label: 'XXX', value: 'XXX' },
                                    ]}
                                    onChange={(e: any) => { setValue('Status', e) }}
                                    error={errors.Status && errors.Status.message}
                                />
                            </Col>
                            <Col sm={12}>
                                <InputGroups
                                    label="Reference number"
                                    {...register('Reference_number')}
                                    defaultValue={''}
                                    error={errors.Reference_number && errors.Reference_number.message}
                                />
                            </Col>
                            <Col sm={12}>
                                <SelectGroups
                                    label="Bank"
                                    {...register('Bank')}
                                    defaultValue={''}
                                    options={[
                                        { label: 'AAA', value: 'AAA' },
                                        { label: 'BBB', value: 'BBB' },
                                        { label: 'XXX', value: 'XXX' },
                                    ]}
                                    onChange={(e: any) => { setValue('Bank', e) }}
                                    error={errors.Bank && errors.Bank.message}
                                />
                            </Col>
                            <Col sm={12}>
                                <SelectGroups
                                    label="PDC writer name"
                                    {...register('PDC_writer_name')}
                                    defaultValue={''}
                                    options={[
                                        { label: 'AAA', value: 'AAA' },
                                        { label: 'BBB', value: 'BBB' },
                                        { label: 'XXX', value: 'XXX' },
                                    ]}
                                    onChange={(e: any) => { setValue('PDC_writer_name', e) }}
                                    error={errors.PDC_writer_name && errors.PDC_writer_name.message}
                                />
                            </Col>
                            <Col sm={24}>
                                <CheckboxLabel>On hold</CheckboxLabel>
                            </Col>
                        </Row>
                    </div>
                </div>
            </form>
        </AddressInfostyle>
    )
}

export default AddressInfo
