import { Datepicker, InputGroups, SelectGroups } from "@dilpesh/kgk-ui-library"
import { Col, Row } from "antd"
import { ContactInfostyle } from "./contact-info.style"
import * as Yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from "react-hook-form";
import { Dayjs } from "dayjs";
import { useEffect } from "react";

interface DateAmountInfo {
    Currency: string;
    Location: string;
    Exchange_rate: string;
    PN_amount: string;
    Receive_date: Dayjs;
    Maturity_date: Dayjs;
    Writing_date: Dayjs;
}

const ContactInfo = ({ setValidateStep }) => {

    const validationSchema = Yup.object().shape({
        Currency: Yup.string()
            .required('Currency is required'),
        Location: Yup.string()
            .required('Location is required'),
        Exchange_rate: Yup.string()
            .required('Rate is required'),
        PN_amount: Yup.string()
            .required('amount is required'),
        Receive_date: Yup.string()
            .required('Receive date is required'),
        Maturity_date: Yup.string()
            .required('Maturity date is required'),
        Writing_date: Yup.string()
            .required('Writing date is required')
    })

    const { register, handleSubmit, setValue, control, formState: { errors }, reset } = useForm({
        defaultValues: {
        },
        mode: "onSubmit",
        resolver: yupResolver(validationSchema),
    });

    const onSubmit = (data: any) => {
        const Data: DateAmountInfo = {
            Currency: data?.Currency,
            Location: data?.Location,
            Exchange_rate: data?.Exchange_rate,
            PN_amount: data?.PN_amount,
            Receive_date: data?.Receive_date,
            Maturity_date: data?.Maturity_date,
            Writing_date: data?.Writing_date,
        }
    }

    const validateForm = () => {
        return new Promise((resolve) => {
            handleSubmit(
                (data) => resolve({ valid: true, data }),
                () => resolve({ valid: false })
            )();
        });
    };

    useEffect(() => {
        setValidateStep(() => validateForm);
    }, []);

    return (
        <ContactInfostyle>
            <form onSubmit={handleSubmit(onSubmit)}>
                <div className="add_info_wrapper">
                    <div className="add_date_wpr">
                        <p>Date</p>
                        <Row gutter={{ sm: 24 }} className="field_gap_24">
                            <Col sm={12}>
                                <Datepicker
                                    label="GST date"
                                    {...register('Receive_date')}
                                    control={control}
                                    onChange={(date: any) => setValue("Receive_date", date, { shouldValidate: true })}
                                    defaultValue={""}
                                    error={errors.Receive_date && errors.Receive_date.message}
                                />
                            </Col>
                            <Col sm={12}>
                                <Datepicker
                                    label="Maturity date"
                                    {...register('Maturity_date')}
                                    control={control}
                                    onChange={(date: any) => setValue("Maturity_date", date, { shouldValidate: true })}
                                    defaultValue={""}
                                    error={errors.Maturity_date && errors.Maturity_date.message}
                                />
                            </Col>
                            <Col sm={12}>
                                <Datepicker
                                    label="Writing date"
                                    {...register('Writing_date')}
                                    control={control}
                                    onChange={(date: any) => setValue("Writing_date", date, { shouldValidate: true })}
                                    defaultValue={""}
                                    error={errors.Writing_date && errors.Writing_date.message}
                                />
                            </Col>
                        </Row>
                    </div>
                    <div className="amount_wpr">
                        <p>Amount</p>
                        <Row gutter={{ sm: 24 }} className="field_gap_24">
                            <Col sm={12}>
                                <SelectGroups
                                    label="Currency"
                                    {...register('Currency')}
                                    defaultValue={''}
                                    options={[
                                        { label: 'RUPY', value: 'RUPY' },
                                        { label: 'Dollar', value: 'Dollar' },
                                        { label: 'pound', value: 'pound' },
                                    ]}
                                    onChange={(e: any) => { setValue('Currency', e) }}
                                    error={errors.Currency && errors.Currency.message}
                                />
                            </Col>
                            <Col sm={12}>
                                <SelectGroups
                                    label="Location"
                                    {...register('Location')}
                                    defaultValue={''}
                                    options={[
                                        { label: 'India', value: 'India' },
                                        { label: 'Chaina', value: 'Chaina' },
                                        { label: 'USA', value: 'USA' },
                                    ]}
                                    onChange={(e: any) => { setValue('Location', e) }}
                                    error={errors.Location && errors.Location.message}
                                />
                            </Col>
                            <Col sm={12}>
                                <InputGroups
                                    label="Exchange rate"
                                    {...register('Exchange_rate')}
                                    defaultValue={''}
                                    error={errors.Exchange_rate && errors.Exchange_rate.message}
                                />
                            </Col>
                            <Col sm={12}>
                                <InputGroups
                                    label="PN amount"
                                    {...register('PN_amount')}
                                    defaultValue={''}
                                    error={errors.PN_amount && errors.PN_amount.message}
                                />
                            </Col>
                        </Row>
                    </div>
                </div>
            </form>
        </ContactInfostyle>
    )
}

export default ContactInfo
