import { br } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const ContactInfostyle = styled.div`
    ${(props) =>
        css`
            .add_info_wrapper{
                .add_date_wpr{
                    margin-bottom: 32px;
                    p{
                        ${br(props?.theme?.text?.high, 500)};
                        margin-bottom: 16px;
                    }
                }
                .amount_wpr{
                    p{
                        ${br(props?.theme?.text?.high, 500)};
                        margin-bottom: 16px;
                    }
                }
            }
        `
    }

`