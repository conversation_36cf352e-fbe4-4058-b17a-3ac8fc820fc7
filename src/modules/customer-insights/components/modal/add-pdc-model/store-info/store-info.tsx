import { CheckboxLabel, TextareaGroups } from "@dilpesh/kgk-ui-library"
import { Col, Row } from "antd"
import { StoreInfostyle } from "./store-info.style"
import { useForm } from "react-hook-form";
import * as Yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { useEffect } from "react";

interface OtherInfo {
    Remarks: string;
}

const StoreInfo = ({ setValidateStep }) => {

    const validationSchema = Yup.object().shape({
        Remarks: Yup.string()
            .required('Remarks is required'),
    })

    const { register, handleSubmit, setValue, control, formState: { errors }, reset } = useForm({
        defaultValues: {
        },
        mode: "onSubmit",
        resolver: yupResolver(validationSchema),
    });

    const onSubmit = (data: any) => {
        const Data: OtherInfo = {
            Remarks: data?.Remarks,
        }
    }
    const validateForm = () => {
        return new Promise((resolve) => {
            handleSubmit(
                (data) => resolve({ valid: true, data }),
                () => resolve({ valid: false })
            )();
        });
    };


    useEffect(() => {
        setValidateStep(() => validateForm);
    }, []);

    return (
        <StoreInfostyle>
            <form onSubmit={handleSubmit(onSubmit)}>
                <div className="other_info_wpr">
                    <p>Applicable for</p>
                    <Row className="field_gap_24">
                        <Col xs={24} sm={24}>
                            <div className="multiple_check_pdc">
                                <CheckboxLabel>Endorsement</CheckboxLabel>
                                <CheckboxLabel>Discount applicable</CheckboxLabel>
                            </div>
                        </Col>
                        <Col xs={24} sm={24}>
                            <TextareaGroups
                                label="Remarks"
                                rows={3}
                                {...register('Remarks')}
                                defaultValue={''}
                                error={errors.Remarks && errors.Remarks.message}
                            />
                        </Col>
                    </Row>
                </div>
            </form>
        </StoreInfostyle>
    )
}

export default StoreInfo
