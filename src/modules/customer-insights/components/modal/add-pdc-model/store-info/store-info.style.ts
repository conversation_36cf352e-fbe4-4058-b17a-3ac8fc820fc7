import { br } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const StoreInfostyle = styled.div`
    ${(props) =>
        css`
            .other_info_wpr{
                & > p{
                    ${br(props?.theme?.text?.high, 500)};
                    margin-bottom: 16px;
                }
                .multiple_check_pdc{
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    .ck{
                        margin-right: 40px;
                        &:last-child{
                            margin-right: 0;
                        }
                    }
                } 
            }
        `
    }

`