import { bes, bl } from "@dilpesh/kgk-ui-library";
import { Modal } from "antd";
import styled, { css } from "styled-components";

export const CustomerInsightsAddPdcModelStyle = styled(Modal)`
    ${(props) =>
        css`
            .ant-modal-header{
                border-bottom: 1px solid ${(props) => props?.theme?.line?.light};
                .ant-modal-title {
                    .header_step {
                        display: flex;
                        align-items: start;
                        justify-content: flex-start;
                        .steps {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            padding-right: 120px;
                            position: relative;
                            &:last-child{
                                padding-right: 0;
                            }
                            .number {
                                background-color: ${(props) => props?.theme?.text?.mid};
                                border-radius: 100%;
                                height: 24px;
                                width: 24px;
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                ${bes(props?.theme?.text?.dark_high, 500)};
                            }
                            svg{
                                height: 24px;
                                width: 24px;
                                display: none;
                            }
                            p {
                                width: calc(100% - 24px);
                                padding-left: 8px;
                                ${bes(props?.theme?.text?.mid, 500)};
                            }
                            &::after {
                                position: absolute;
                                content: "";
                                right: 16px;
                                width: 88px;
                                border-top: 2px dashed ${(props) => props?.theme?.line?.light};
                                top: 50%;
                                transform: translateY(-50%);
                            }
                            &.active {
                                .number {
                                    background-color:${(props) => props?.theme?.colors?.brand_high};
                                    ${bes(props?.theme?.text?.dark_high, 500)};
                                }
                                p {
                                    ${bes(props?.theme?.text?.high, 500)};
                                }
                            }
                            &.completed {
                                .number{
                                    display: none;
                                }
                                svg{
                                    display: block;
                                }
                                p {
                                    ${bes(props?.theme?.text?.high, 500)};
                                }
                                &::after {
                                    border-color: ${(props) => props?.theme?.line?.high};
                                }
                            }
                            &:last-child {
                                &::after {
                                    display: none;
                                }
                            }
                        }
                    }
                }
            }
            .ant-modal-body{
                padding-top: 32px;
                min-height: 596px;
                .body{
                    & > h2{
                        ${bl(props?.theme?.text?.high, 500)};
                        margin-bottom: 32px;
                    }
                }
            }
        `
    }
`