import { DataTable } from "@dilpesh/kgk-ui-library";
import {
  CloseIcon,
  getSessionItem,
  SESSION_KEYS,
  useGetProposalDetails
} from "@magneto-it-solutions/kgk-common-library";
import { skipToken } from "@reduxjs/toolkit/query";
import { Col, Row } from "antd";
import { format } from "date-fns";
import { assetsListingHead } from "./utils/constant";
import { CustomerInsightsProposalOrderModelStyle } from "./utils/proposal-order-modal-style";
import { getProposalColumnDef } from "./utils/utility";

export default function CustomerInsightsProposalOrderModel({
  show,
  onHide,
  isEdit,
  rejectBtnEvent,
}: any) {

  const shouldFetch = show?.id && show;
  const { proposal, isLoading, refetch } = useGetProposalDetails(shouldFetch ? show?.id : skipToken);

  const coloumnDefListing = getProposalColumnDef({ isLoading });

  return (
    <CustomerInsightsProposalOrderModelStyle
      title={"Proposal order"}
      // centere
      closeIcon={<CloseIcon />}
      open={show.open}
      onOk={onHide}
      onCancel={onHide}
      width={1400}
      wrapClassName="detail_modal"
      footer={null}
    >
      <form className="body">
        <div className="upper_data_wpr">
          <Row gutter={{ lg: 24 }}>
            <Col md={24} lg={12}>
              <div className="inner_data_wpr">
                <span>Proposal number</span>
                <p>
                  {proposal?.proposal_number ? proposal?.proposal_number : "-"}
                </p>
              </div>
              <div className="inner_data_wpr">
                <span>Customer</span>
                <p>
                  {proposal?.proposal_number ? proposal?.customer_code : "-"}
                </p>
              </div>
              <div className="inner_data_wpr">
                <span>Billing address</span>
                <p>
                  {proposal?.proposal_number ? proposal?.billing_address : "-"}
                </p>
              </div>
              <div className="inner_data_wpr">
                <span>Shipping address</span>
                <p>
                  {proposal?.proposal_number ? proposal?.shipping_address : "-"}
                </p>
              </div>
              <div className="inner_data_wpr">
                <span>Payment terms</span>
                <p>
                  {proposal?.proposal_number ? proposal?.payment_terms : "-"}
                </p>
              </div>
            </Col>
            <Col md={24} lg={12}>
              <div className="inner_data_wpr">
                <span>Shipping date</span>
                <p>{proposal?.shipping_date ? format(
                  new Date(proposal?.shipping_date),
                  getSessionItem(SESSION_KEYS.DATE_FORMAT) ||
                  "dd MMM yyyy, h:mm a"
                ) : "-"}</p>
              </div>
              <div className="inner_data_wpr">
                <span>Remarks</span>
                <p>{proposal?.remarks ? proposal?.remarks : "-"}</p>
              </div>
              <div className="inner_data_wpr">
                <span>Total quantity</span>
                <p>
                  {proposal?.total_quantity ? proposal?.total_quantity : "-"}
                </p>
              </div>
              <div className="inner_data_wpr">
                <span>Total amount</span>
                <p>{proposal?.total_amount ? proposal?.total_amount : "-"}</p>
              </div>
            </Col>
          </Row>
        </div>
        <div className="proposal_details_wpr">
          <h2>Proposal details</h2>
          <DataTable
            isMultiselect={false}
            column={assetsListingHead}
            coloumnDef={coloumnDefListing}
            data={proposal?.proposal_details}
            bodyClass="body_pt_8"
          />
        </div>
      </form>
    </CustomerInsightsProposalOrderModelStyle>
  );
}
