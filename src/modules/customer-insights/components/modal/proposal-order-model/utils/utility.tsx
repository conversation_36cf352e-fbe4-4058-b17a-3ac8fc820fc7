import { Skeleton } from "@dilpesh/kgk-ui-library";

export const getProposalColumnDef = ({ isLoading }) => {
  return {
    image: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="56px" />
        ) : (
          <div className="img_icon_56">
            <img src={item.Image} alt="proposal-img" />
          </div>
        ),
    },
    styleNumber: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="100px" />
        ) : (
          <p>{item?.StyleNo || "-"}</p>
        ),
    },
    productDetails: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="140px" />
        ) : (
          <p>{item?.ProductDetail || "-"}</p>
        ),
    },
    productSize: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="80px" />
        ) : (
          <p>{item?.ProductSize || "-"}</p>
        ),
    },
    productionRemarks: {
      renderChildren: () =>
        isLoading ? (
          <Skeleton width="60px" />
        ) : (
          <p>-</p>
        ),
    },
    sPRate: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="80px" />
        ) : (
          <p>{item?.SPRate || "-"}</p>
        ),
    },
    quantity: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="60px" />
        ) : (
          <p>{item?.Qty || "-"}</p>
        ),
    },
    amount: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="100px" />
        ) : (
          <p>{item?.Amount || "-"}</p>
        ),
    },
  };
};
