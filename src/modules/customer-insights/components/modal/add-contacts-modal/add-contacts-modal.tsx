import { CheckboxLabel, InputGroups, PhoneGroups, SelectGroups } from "@dilpesh/kgk-ui-library";
import { Col, Row } from "antd";
import { CustomerInsightsAddContactsModalStyle } from "./add-contacts-modal.style";
import { Controller, useForm } from "react-hook-form";
import { MinusIcon, useModal } from "@magneto-it-solutions/kgk-common-library";
import { yupResolver } from '@hookform/resolvers/yup';
import * as Yup from 'yup';
import { forwardRef, useImperativeHandle } from "react";


export interface ContactFormData {
    Contact_name: String,
    Designation: String,
    Internal_remarks: String,
    Fax: String,
    Email: String,
    Phone: String,
    Mobile: String,
    Website: String,
    Street: String,
    Area: String,
    City: String,
    Pin_code: String,
}

const CustomerInsightsAddContactsModal = forwardRef((props, ref) => {
    const { open: addAddressModalOpen, close, setIsLoading, isLoading } = useModal(null, "custom-modal");

    const validationSchema = Yup.object().shape({
        Contact_name: Yup.string()
            .required('Contact name is required')
            .max(50, 'Contact name cannot exceed 50 characters'),
        Designation: Yup.string()
            .required('Please Select Designation'),
        Internal_remarks: Yup.string()
            .required("Please Enter Remarks")
            .max(200, 'Remarks cannot exceed 200 characters'),
        Fax: Yup.string()
            .required("Fax number is required")
            .matches(/^\+?[0-9]{6,15}$/, 'Invalid fax number'),
        Email: Yup.string()
            .required("Email is required"),
        Phone: Yup.string()
            .required("Phone number must be valid.")
            .matches(/^(\+?\d{1,3}[- ]?)?\d{10}$/, 'Phone number must be 10 digits.'),
        Mobile: Yup.string()
            .required("Mobile number is required"),
        Website: Yup.string()
            .required("Website is required")
            .matches(
                /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w.-]*)*\/?$/,
                'Invalid website URL'
            ),
        Street: Yup.string()
            .required("Street is required")
            .max(100, 'Street name cannot exceed 100 characters'),
        Area: Yup.string()
            .required('Area is required')
            .max(100, 'Area name cannot exceed 100 characters'),
        City: Yup.string()
            .required("City is required")
            .max(50, 'City name cannot exceed 50 characters'),
        Pin_code: Yup.string()
            .required("Pin code is required")
            .matches(/^[0-9]{4,10}$/, 'Invalid pin code'),

    })

    const { register, handleSubmit, setValue, control, formState: { errors }, reset } = useForm({
        defaultValues: {
            Contact_name: '',
            Designation: '',
            Internal_remarks: '',
            Fax: '',
            Email: '',
            Phone: '',
            Mobile: '',
            Website: '',
            Street: '',
            Area: '',
            City: '',
            Pin_code: '',
        },
        mode: "onSubmit",
        resolver: yupResolver(validationSchema),
    });

    const onsubmit = (data: any) => {
        const Data: ContactFormData = {
            Contact_name: data?.Contact_name,
            Designation: data?.Designation,
            Internal_remarks: data?.Internal_remarks,
            Fax: data?.Fax,
            Email: data?.Email,
            Phone: data?.Phone,
            Mobile: data?.Mobile,
            Website: data?.Website,
            Street: data?.Street,
            Area: data?.Area,
            City: data?.City,
            Pin_code: data?.Pin_code,
        }
    }

    useImperativeHandle(ref, () => ({
        submit() {
            handleSubmit(onsubmit)();
        },
    }));


    return (
        <CustomerInsightsAddContactsModalStyle>
            <form className="body" onSubmit={handleSubmit(onsubmit)}>
                <div className="contact_info_wpr">
                    <p>Contact information</p>
                    <Row gutter={{ sm: 24 }} className="field_gap_24">
                        <Col sm={12}>
                            <InputGroups
                                label="Contact Name"
                                {...register('Contact_name')}
                                defaultValue={""}
                                error={errors.Contact_name && errors.Contact_name.message}
                            />
                        </Col>
                        <Col sm={12}>
                            <SelectGroups
                                label="Designation"
                                {...register('Designation')}
                                options={[
                                    { label: 'Customer', value: 'customer' },
                                    { label: 'Salesman', value: 'Salesman' },

                                ]}
                                defaultValue={""}
                                control={control}
                                onChange={(e: any) => setValue('Designation', e)}
                                error={errors.Designation && errors.Designation.message}
                            />
                        </Col>
                        <Col sm={12}>
                            <InputGroups
                                label="Internal remarks"
                                {...register('Internal_remarks')}
                                defaultValue={""}
                                error={errors.Internal_remarks && errors.Internal_remarks.message}
                            />
                        </Col>
                        <Col sm={12}>
                            <InputGroups
                                label="Fax"
                                {...register('Fax')}
                                defaultValue={""}
                                error={errors.Fax && errors.Fax.message}
                            />
                        </Col>
                        <Col sm={12}>
                            <InputGroups
                                label="Email"
                                {...register('Email')}
                                defaultValue={""}
                                error={errors.Email && errors.Email.message}
                            />
                        </Col>
                        <Col sm={12}>
                            <Controller
                                name="Phone"
                                control={control}
                                rules={{
                                    required: 'Phone number is required',
                                    pattern: {
                                        value: /^\+?[0-9]{7,15}$/,
                                        message: 'Invalid phone number',
                                    },
                                }}
                                render={({ field }) => (
                                    <PhoneGroups
                                        label="Phone"
                                        value={field.value}
                                        onChange={(e: any) => field.onChange(e)}
                                        onBlur={field.onBlur}
                                        removeIcon={<MinusIcon />}
                                        error={errors.Phone?.message}
                                    />
                                )}
                            />
                        </Col>
                        <Col sm={12}>
                            <Controller
                                name="Mobile"
                                control={control}
                                rules={{
                                    required: 'Mobile number is required',
                                    pattern: {
                                        value: /^\+?[0-9]{7,15}$/,
                                        message: 'Invalid mobile number',
                                    },
                                }}
                                render={({ field }) => (
                                    <PhoneGroups
                                        label="Mobile"
                                        value={field.value}
                                        onChange={field.onChange}
                                        onBlur={field.onBlur}
                                        removeIcon={<MinusIcon />}
                                        error={errors.Mobile?.message}
                                    />
                                )}
                            />
                        </Col>
                        <Col sm={12}>
                            <InputGroups
                                label="Website"
                                {...register('Website')}
                                defaultValue={""}
                                error={errors.Website && errors.Website.message}
                            />
                        </Col>
                    </Row>
                </div>
                <div className="address_wpr">
                    <p>Address</p>
                    <Row gutter={{ sm: 24 }} className="field_gap_24">
                        <Col sm={12}>
                            <InputGroups
                                label="Street"
                                {...register('Street')}
                                defaultValue={""}
                                error={errors.Street && errors.Street.message}
                            />
                        </Col>
                        <Col sm={12}>
                            <InputGroups
                                label="Area"
                                {...register('Area')}
                                defaultValue={""}
                                error={errors.Area && errors.Area.message}
                            />
                        </Col>
                        <Col sm={12}>
                            <InputGroups
                                label="City"
                                {...register('City')}
                                defaultValue={""}
                                error={errors.City && errors.City.message}
                            />
                        </Col>
                        <Col sm={12}>
                            <InputGroups
                                label="Pin code"
                                {...register('Pin_code')}
                                defaultValue={""}
                                error={errors.Pin_code && errors.Pin_code.message}
                            />
                        </Col>
                        <Col sm={24}>
                            <CheckboxLabel>This is main contact</CheckboxLabel>
                        </Col>
                    </Row>
                </div>
            </form>
        </CustomerInsightsAddContactsModalStyle>


    )
});

export default CustomerInsightsAddContactsModal;