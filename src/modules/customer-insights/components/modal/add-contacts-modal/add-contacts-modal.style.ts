import { bl, br } from "@dilpesh/kgk-ui-library";
import { Modal } from "antd";
import styled, { css } from "styled-components";

export const CustomerInsightsAddContactsModalStyle = styled.div`
    ${(props) =>
        css`    
            .ant-modal-header{
                .ant-modal-title{
                    ${bl(props?.theme?.text?.high, 500)};            
                }
            }
            .contact_info_wpr{
                margin-bottom: 32px;
                & > p {
                    ${br(props?.theme?.text?.high, 500)};
                    margin-bottom: 16px;
                }
            }
            .address_wpr{
                & > p {
                    ${br(props?.theme?.text?.high, 500)};
                    margin-bottom: 16px;
                }
            }
            .ant-modal-footer{
                margin-top: 0 !important;
            }
        `
    }
`