import { CloseIcon } from "@/assets/icons/collection/CloseIcon";
import { Btn, RadioLabel, SelectGroups } from "@dilpesh/kgk-ui-library";
import { Col, Row } from "antd";
import { CustomerInsightsPrintInvoiceModalstyle } from "./print-invoice-modal.style";
import { useModal } from "@magneto-it-solutions/kgk-common-library";
import { Controller, useForm } from "react-hook-form";
import * as Yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { forwardRef, useImperativeHandle } from "react";

interface MemoInfoData {
    Report_format: string;
    File_type: string;
}
const fileTypes = ['PDF', 'Excel', 'CSV', 'HTML'];

const CustomerInsightsPrintInvoiceModal = forwardRef((props, ref) => {

    const { open: open, close, setIsLoading, isLoading } = useModal(null, "custom-modal");

    const validationSchema = Yup.object().shape({
        Report_format: Yup.string()
            .required("Format is required"),
        File_type: Yup.string()
            .required("File type is required")
    })

    const { register, handleSubmit, setValue, control, formState: { errors }, reset } = useForm({
        defaultValues: {


        },
        mode: "onSubmit",
        resolver: yupResolver(validationSchema),
    });


    const selectData = [
        {
            value: 'jewellery',
            label: 'Jewellery',
        },
        {
            value: 'ring',
            label: 'Ring',
        },
    ]

    const onSubmit = (data: any) => {
        const Data: MemoInfoData = {
            Report_format: data?.Report_format,
            File_type: data?.File_type,
        }
    }

    useImperativeHandle(ref, () => ({
        submit() {
            handleSubmit(onSubmit)();
        },
    }));
    return (
        <CustomerInsightsPrintInvoiceModalstyle>
            <form className="body" onSubmit={handleSubmit(onSubmit)}>
                <Row className="field_gap_24">
                    <Col xs={24} sm={24}>
                        <SelectGroups
                            label="Report format"
                            options={selectData}
                            defaultValue={''}
                            onChange={(e: any) => setValue('Report_format', e)}
                            error={errors.Report_format && errors.Report_format.message}
                        />
                    </Col>
                    <Col xs={24} sm={24}>
                        <div className="print_invoice_multi_radio">
                            <p>File type</p>
                            <div className="file_type_wpr">
                                {/* Dynamically render Radio buttons */}
                                {fileTypes.map((type) => (
                                    <Controller
                                        key={type}
                                        name="File_type"
                                        control={control}
                                        render={({ field }) => (
                                            <RadioLabel {...field} value={type}>
                                                {type}
                                            </RadioLabel>
                                        )}
                                    />
                                ))}
                            </div>
                            {errors.File_type && (
                                <div className="error">{errors.File_type.message}</div>
                            )}
                        </div>
                    </Col>
                </Row>
            </form>
        </CustomerInsightsPrintInvoiceModalstyle >
    )
})

export default CustomerInsightsPrintInvoiceModal;
