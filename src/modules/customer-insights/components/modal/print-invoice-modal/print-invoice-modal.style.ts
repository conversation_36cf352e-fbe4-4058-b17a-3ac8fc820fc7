import { b2xs } from "@dilpesh/kgk-ui-library";
import { Modal } from "antd";
import styled, { css } from "styled-components";

export const CustomerInsightsPrintInvoiceModalstyle = styled.div`
    ${(props) =>
        css`
            .ant-modal-content{              
                .ant-modal-body{
                    .body{
                        .print_invoice_multi_radio{
                            & > p{
                                margin-bottom: 16px;
                                ${b2xs(props?.theme?.text?.high, 400, false)};            
                            }
                            .file_type_wpr{
                                display: flex;
                                & > *{
                                    margin-right: 24px;
                                    &:last-child{
                                        margin-right: 0;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        `
    }
`