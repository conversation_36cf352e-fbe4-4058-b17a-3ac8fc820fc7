import { InputGroups, PhoneGroups } from "@dilpesh/kgk-ui-library"
import { Col, Row } from "antd"
import { Controller, useForm } from "react-hook-form";
import { ContactInfostyle } from "./contact-info.style"
import * as Yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { MinusIcon } from "@magneto-it-solutions/kgk-common-library";
import { useEffect } from "react";

interface ContactInfoData {
    Contact_name: string;
    Phone: String;
    Mobile: String;
    Email: string
    Website: string
}

const ContactInfo = ({ setValidateStep }) => {

    const validationSchema = Yup.object().shape({
        Contact_name: Yup.string()
            .required('Name is required'),
        Phone: Yup.string()
            .required('Phone number is required'),
        Mobile: Yup.string()
            .required('Mobile number is required'),
        Email: Yup.string()
            .required('Email is required')
            .matches(
                /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                'Invalid email format'
            ),
        Website: Yup.string()
            .required("Website is required")
            .matches(
                /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w.-]*)*\/?$/,
                'Invalid website URL'
            ),
    })
    const { register, handleSubmit, setValue, control, formState: { errors }, reset } = useForm({
        defaultValues: {


        },
        mode: "onSubmit",
        resolver: yupResolver(validationSchema),
    });

    const validateForm = () => {
        return new Promise((resolve) => {
            handleSubmit(
                (data) => resolve({ valid: true, data }),
                () => resolve({ valid: false })
            )();
        });
    };


    useEffect(() => {
        setValidateStep(() => validateForm);
    }, []);

    const onSubmit = (data: any) => {
        const Data: ContactInfoData = {
            Contact_name: data?.Contact_name,
            Phone: data?.Phone,
            Mobile: data?.Mobile,
            Email: data?.Email,
            Website: data?.Website,
        }
    }
    return (
        <ContactInfostyle>
            <form onSubmit={handleSubmit(onSubmit)}>
                <div className="contact_info_wpr">
                    <p>Contact information</p>
                    <Row gutter={{ sm: 24 }} className="field_gap_24">
                        <Col sm={12}>
                            <InputGroups
                                label="Contact name"
                                {...register('Contact_name')}
                                defaultValue={''}
                                error={errors.Contact_name && errors.Contact_name.message}
                            />
                        </Col>
                        <Col sm={12}>
                            <Controller
                                name="Phone"
                                control={control}
                                render={({ field }) => (
                                    <PhoneGroups
                                        label="Phone"
                                        value={field.value}
                                        onChange={(e: any) => field.onChange(e)}
                                        onBlur={field.onBlur}
                                        removeIcon={<MinusIcon />}
                                        error={errors.Phone?.message}
                                    />
                                )}
                            />
                        </Col>
                        <Col sm={12}>
                            <Controller
                                name="Mobile"
                                control={control}
                                render={({ field }) => (
                                    <PhoneGroups
                                        label="Mobile"
                                        value={field.value}
                                        onChange={(e: any) => field.onChange(e)}
                                        onBlur={field.onBlur}
                                        removeIcon={<MinusIcon />}
                                        error={errors.Phone?.message}
                                    />
                                )}
                            />
                        </Col>
                        <Col sm={12}>
                            <InputGroups
                                label="Email"
                                {...register('Email')}
                                defaultValue={''}
                                error={errors.Email && errors.Email.message}
                            />
                        </Col>
                        <Col sm={12}>
                            <InputGroups
                                label="Website"
                                {...register('Website')}
                                defaultValue={''}
                                error={errors.Website && errors.Website.message}
                            />
                        </Col>
                    </Row>
                </div>
            </form>
        </ContactInfostyle>
    )
}

export default ContactInfo
