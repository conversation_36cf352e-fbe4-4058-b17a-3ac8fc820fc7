import { br } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const AddressInfostyle = styled.div`
    ${(props) =>
        css`
            .address_info_wrapper{
                .add_address_wpr{
                    margin-bottom: 32px;
                    p{
                        ${br(props?.theme?.text?.high, 500)};
                        margin-bottom: 16px;
                    }
                }
                .taxation_wpr{
                    p{
                        ${br(props?.theme?.text?.high, 500)};
                        margin-bottom: 16px;
                    }
                }
            }
        `
    }

`