import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, InputGroups, SelectGroups } from "@dilpesh/kgk-ui-library"
import { Col, Row } from "antd"
import { AddressInfostyle } from "./address-info.style"
import { useForm } from "react-hook-form";
import * as Yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { useEffect } from "react";
import { Dayjs } from "dayjs";

interface AddressInfoData {
    Company_name: string;
    Address_type: string;
    Street: string;
    Area: string;
    City: string
    State: string;
    Country: string;
    Pin_code: string;
    GST_number: string;
    PAN_number: String;
    TIN_number: string;
    GST_date: Dayjs;

}

const AddressInfo = ({ setValidateStep }) => {

    const validationSchema = Yup.object().shape({
        Company_name: Yup.string()
            .required("Company Name is required"),
        Address_type: Yup.string()
            .required("Address Type is required"),
        Street: Yup.string()
            .required("Street is required "),
        Area: Yup.string()
            .required("Area is required "),
        City: Yup.string()
            .required("City is required "),
        State: Yup.string()
            .required("State is required "),
        Country: Yup.string()
            .required("Country is required "),
        Pin_code: Yup.string()
            .required("Pin code is required ")
            .matches(/^[0-9]{6}$/, "Invalid pin code format"),
        GST_number: Yup.string()
            .required("GST number is required")
            .matches(
                /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[0-9]{1}[A-Z]{1}[0-9]{1}$/,
                "Invalid GST number format"
            ),
        PAN_number: Yup.string()
            .required("PAN number is required ")
            .matches(/[A-Z]{5}[0-9]{4}[A-Z]{1}/, "Invalid PAN number format"),
        TIN_number: Yup.string()
            .required("TIN number is required ")
            .matches(/^[0-9]{11}$/, "Invalid TIN number format"),
        GST_date: Yup.string()
            .required("GST date is required "),


    })

    const { register, handleSubmit, setValue, control, formState: { errors }, reset } = useForm({
        defaultValues: {
            Company_name: '',
            Address_type: '',
            Street: '',
            Area: '',
            City: '',
            State: '',
            Country: '',
            Pin_code: '',
            GST_number: '',
            PAN_number: '',
            TIN_number: '',
            GST_date: '',

        },
        mode: "onSubmit",
        resolver: yupResolver(validationSchema),
    });

    const validateForm = () => {
        return new Promise((resolve) => {
            handleSubmit(
                (data) => resolve({ valid: true, data }),
                () => resolve({ valid: false })
            )();
        });
    };


    useEffect(() => {
        setValidateStep(() => validateForm);
    }, []);

    const onSubmit = (data: any) => {
        const Data: AddressInfoData = {
            Company_name: data?.Company_name,
            Address_type: data?.Address_type,
            Street: data?.Street,
            Area: data?.Area,
            City: data?.City,
            State: data?.State,
            Country: data?.Country,
            Pin_code: data?.Pin_code,
            GST_number: data?.GST_number,
            PAN_number: data?.PAN_number,
            TIN_number: data?.TIN_number,
            GST_date: data?.GST_date,
        }
    }
    return (
        <AddressInfostyle>
            <form onSubmit={handleSubmit(onSubmit)}>
                <div className="address_info_wrapper">
                    <div className="add_address_wpr">
                        <p>Address information</p>
                        <Row gutter={{ sm: 24 }} className="field_gap_24">
                            <Col sm={12}>
                                <InputGroups
                                    label="Company Name"
                                    {...register('Company_name')}
                                    defaultValue={""}
                                    error={errors.Company_name && errors.Company_name.message}
                                />
                            </Col>
                            <Col sm={12}>
                                <SelectGroups
                                    label="Address type"
                                    {...register('Address_type')}
                                    options={[
                                        { label: 'Home', value: 'Home' },
                                        { label: 'Office', value: 'Office' },

                                    ]}
                                    defaultValue={""}
                                    onChange={(e: any) => setValue('Address_type', e)}
                                    error={errors.Address_type && errors.Address_type.message}
                                />
                            </Col>
                            <Col sm={12}>
                                <InputGroups
                                    label="Street"
                                    {...register('Street')}
                                    defaultValue={""}
                                    error={errors.Street && errors.Street.message}
                                />
                            </Col>
                            <Col sm={12}>
                                <InputGroups
                                    label="Area"
                                    {...register('Area')}
                                    defaultValue={""}
                                    error={errors.Area && errors.Area.message}
                                />
                            </Col>
                            <Col sm={12}>
                                <SelectGroups
                                    label="City"
                                    {...register('City')}
                                    options={[
                                        { label: 'AAA', value: 'AAA' },
                                        { label: 'BBB', value: 'BBB' },
                                    ]}
                                    onChange={(e: any) => setValue('City', e)}
                                    defaultValue={""}
                                    error={errors.City && errors.City.message}
                                />
                            </Col>
                            <Col sm={12}>
                                <InputGroups
                                    label="State"
                                    {...register('State')}
                                    defaultValue={""}
                                    error={errors.State && errors.State.message}
                                />
                            </Col>
                            <Col sm={12}>
                                <InputGroups
                                    label="Country"
                                    {...register('Country')}
                                    defaultValue={""}
                                    error={errors.Country && errors.Country.message}
                                />
                            </Col>
                            <Col sm={12}>
                                <InputGroups
                                    label="Pin code"
                                    {...register('Pin_code')}
                                    defaultValue={""}
                                    error={errors.Pin_code && errors.Pin_code.message}
                                />
                            </Col>
                            <Col sm={24}>
                                <CheckboxLabel>This is default address</CheckboxLabel>
                            </Col>
                        </Row>
                    </div>
                    <div className="taxation_wpr">
                        <p>Taxation</p>
                        <Row gutter={{ sm: 24 }} className="field_gap_24">
                            <Col sm={12}>
                                <InputGroups
                                    label="PAN number"
                                    {...register('PAN_number')}
                                    defaultValue={""}
                                    error={errors.PAN_number && errors.PAN_number.message}
                                />
                            </Col>
                            <Col sm={12}>
                                <InputGroups
                                    label="TIN number"
                                    {...register("TIN_number")}
                                    defaultValue={""}
                                    error={errors.TIN_number && errors.TIN_number.message}
                                />
                            </Col>
                            <Col sm={12}>
                                <InputGroups
                                    label="GST number"
                                    {...register('GST_number')}
                                    defaultValue={""}
                                    error={errors.GST_number && errors.GST_number.message}
                                />
                            </Col>
                            <Col sm={12}>
                                <Datepicker
                                    label="GST date"
                                    {...register('GST_date')}
                                    control={control}
                                    onChange={(date) => setValue("GST_date", date, { shouldValidate: true })}
                                    defaultValue={""}
                                    error={errors.GST_date && errors.GST_date.message}

                                />
                            </Col>
                        </Row>
                    </div>
                </div>
            </form>
        </AddressInfostyle>
    )
}

export default AddressInfo
