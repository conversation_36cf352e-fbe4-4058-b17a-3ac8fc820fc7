import { Btn } from "@dilpesh/kgk-ui-library";
import React, { useState } from "react";
import { CustomerInsightsAddAddressModelStyle } from "./add-address-model.style";
import AddressInfo from "./address-info/address-info";
import ContactInfo from "./contact-info/contact-info";
import StoreInfo from "./store-info/store-info";
import { SignIcon } from "@/assets/icons/collection/SignIcon";
import { CloseIcon } from "@/assets/icons/collection/CloseIcon";

export default function CustomerInsightsAddAddressModel({
    show,
    onHide,
    rejectBtnEvent,
}: any) {
    const [modalTab, setModalTab] = useState("basic-info");
    const [currentStep, setCurrentStep] = useState(1);
    const [validateStep, setValidateStep] = useState(() => async () => ({ valid: true }));

    const changeToNextStep = async () => {
        const validation = await validateStep();
        if (validation.valid) {
            setCurrentStep((prevStep) => prevStep + 1);
        }
    };

    const changeToPrevStep = () => {
        setCurrentStep((prevStep) => prevStep - 1);
    };

    const SaveTOInformation = async () => {
        const validation = await validateStep();
        if (validation.valid) {
            console.log("Submit data succesfully")
        }
    }

    return (
        <CustomerInsightsAddAddressModelStyle
            title={
                <div className="header_step">
                    <div className={`steps ${currentStep == 1 ? "active" : ''} ${currentStep > 1 ? "completed" : ''}`}>
                        <div className="number">1</div>
                        <SignIcon />
                        <p>Address information</p>
                    </div>
                    <div className={`steps ${currentStep == 2 ? "active" : ''} ${currentStep > 2 ? "completed" : ''}`}>
                        <div className="number">2</div>
                        <SignIcon />
                        <p>Contact information</p>
                    </div>
                    <div className={`steps ${currentStep == 3 ? "active" : ''}`}>
                        <div className="number">3</div>
                        <p>Store information</p>
                    </div>
                </div>
            }
            centered
            closeIcon={<CloseIcon />}
            open={show}
            onOk={onHide}
            onCancel={onHide}
            width={792}
            wrapClassName="detail_modal"
            footer={[
                <React.Fragment>
                    {currentStep === 1 ? (
                        <Btn bg="fill" onClick={changeToNextStep} size="large">
                            Continue
                        </Btn>
                    ) : currentStep === 2 ? (
                        <>
                            <Btn bg="text" onClick={changeToPrevStep} size="large">
                                Previous
                            </Btn>
                            <Btn bg="fill" onClick={changeToNextStep} size="large">
                                Continue
                            </Btn>
                        </>
                    ) : (
                        <>
                            <Btn bg="text" onClick={changeToPrevStep} size="large">
                                Previous
                            </Btn>
                            <Btn bg="fill" onClick={SaveTOInformation} size="large">
                                Save
                            </Btn>
                        </>
                    )}
                </React.Fragment>
            ]}
        >
            <form className="body">
                <h2>Add address</h2>
                {currentStep === 1 ? (
                    <AddressInfo setValidateStep={setValidateStep} />
                ) : currentStep === 2 ? (
                    <ContactInfo setValidateStep={setValidateStep} />
                ) : (
                    <StoreInfo setValidateStep={setValidateStep} />
                )}
            </form>
        </CustomerInsightsAddAddressModelStyle>
    );
}
