import { InputGroups } from "@dilpesh/kgk-ui-library"
import { Col, Row } from "antd"
import { StoreInfostyle } from "./store-info.style"
import { useForm } from "react-hook-form";
import * as Yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { useEffect } from "react";

interface StoreInfoData {
    Store_code: string;
    Store_abbr: string;
    Store_name: string;
    Dept_code: string;
    Remarks: string;
}

const StoreInfo = ({ setValidateStep }) => {

    const validationSchema = Yup.object().shape({
        Store_code: Yup.string()
            .required('Code is required'),
        Store_abbr: Yup.string()
            .required('Store abbr is required'),
        Store_name: Yup.string()
            .required('Store name is required'),
        Dept_code: Yup.string()
            .required('Dept code is required'),
        Remarks: Yup.string()
            .required('Remarks is required'),
    })

    const { register, handleSubmit, setValue, control, formState: { errors }, reset } = useForm({
        defaultValues: {

        },
        mode: "onSubmit",
        resolver: yupResolver(validationSchema),
    });

    const onSubmit = (data: any) => {
        const Data: StoreInfoData = {
            Store_code: data?.Store_code,
            Store_abbr: data?.Store_abbr,
            Store_name: data?.Store_name,
            Dept_code: data?.Dept_code,
            Remarks: data?.Remarks,
        }

    }
    const validateForm = () => {
        return new Promise((resolve) => {
            handleSubmit(
                (data) => resolve({ valid: true, data }),
                () => resolve({ valid: false })
            )();
        });
    };


    useEffect(() => {
        setValidateStep(() => validateForm);
    }, []);
    return (
        <StoreInfostyle>
            <form onSubmit={handleSubmit(onSubmit)}>
                <div className="store_info_wpr">
                    <p>Store information</p>
                    <Row gutter={{ sm: 24 }} className="field_gap_24">
                        <Col sm={12}>
                            <InputGroups
                                label="Store code"
                                {...register('Store_code')}
                                defaultValue={''}
                                error={errors.Store_code && errors.Store_code.message}
                            />
                        </Col>
                        <Col sm={12}>
                            <InputGroups
                                label="Store abbr."
                                {...register('Store_abbr')}
                                defaultValue={''}
                                error={errors.Store_abbr && errors.Store_abbr.message}
                            />
                        </Col>
                        <Col sm={12}>
                            <InputGroups
                                label="Store name"
                                {...register('Store_name')}
                                defaultValue={''}
                                error={errors.Store_name && errors.Store_name.message}
                            />
                        </Col>
                        <Col sm={12}>
                            <InputGroups
                                label="Dept. code"
                                {...register('Dept_code')}
                                defaultValue={''}
                                error={errors.Dept_code && errors.Dept_code.message}
                            />
                        </Col>
                        <Col sm={12}>
                            <InputGroups
                                label="Remarks"
                                {...register('Remarks')}
                                defaultValue={''}
                                error={errors.Remarks && errors.Remarks.message}
                            />
                        </Col>
                    </Row>
                </div>
            </form>
        </StoreInfostyle>
    )
}

export default StoreInfo
