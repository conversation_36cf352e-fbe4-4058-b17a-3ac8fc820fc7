import { SelectGroups, TextareaGroups } from "@dilpesh/kgk-ui-library";
import { yupResolver } from '@hookform/resolvers/yup';
import { Col, Row } from "antd";
import { forwardRef, useImperativeHandle } from "react";
import { useForm } from "react-hook-form";
import * as Yup from 'yup';
import { CustomerInsightsAddDiscussionsModalStyle } from "./add-discussions-modal-style";

interface DiscussionInfo {
  Discussion_type: string;
  Comments: string;
}

const CustomerInsightsAddDiscussionsModal = forwardRef((props, ref) => {

  const validationSchema = Yup.object().shape({
    Discussion_type: Yup.string()
      .required("Discussion Type is required"),
    Comments: Yup.string()
      .required("Comments Type is required"),
  })

  const { register, handleSubmit, setValue, control, formState: { errors }, reset } = useForm({
    defaultValues: {


    },
    mode: "onSubmit",
    resolver: yupResolver(validationSchema),
  });

  const onSubmit = (data: any) => {
    const Data: DiscussionInfo = {
      Discussion_type: data?.Discussion_type,
      Comments: data?.Comments,
    }
  }
  useImperativeHandle(ref, () => ({
    submit() {
      handleSubmit(onSubmit)();
    },
  }));

  return (
    <CustomerInsightsAddDiscussionsModalStyle>
      <form className="body" onSubmit={handleSubmit(onSubmit)}>
        <Row className="field_gap_24">
          <Col xs={24}>
            <SelectGroups
              label="Discussion type"
              {...register('Discussion_type')}
              options={[
                { label: 'Jewellery', value: 'Jewellery' },
                { label: 'Diamonds', value: 'Diamonds' },
                { label: 'Gemstone', value: 'Gemstone' },

              ]}
              defaultValue={""}
              control={control}
              onChange={(e: any) => setValue('Discussion_type', e)}
              error={errors.Discussion_type && errors.Discussion_type.message}
            />
          </Col>
          <Col xs={24}>
            <TextareaGroups
              label="Comments"
              rows={4}
              {...register('Comments')}
              defaultValue={''}
              error={errors.Comments && errors.Comments.message}
            />
          </Col>
        </Row>
      </form>
    </CustomerInsightsAddDiscussionsModalStyle>
  )
});

export default CustomerInsightsAddDiscussionsModal;
