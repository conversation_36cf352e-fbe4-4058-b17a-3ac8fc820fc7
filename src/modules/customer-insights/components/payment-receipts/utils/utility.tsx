import { PrintIcon } from "@/assets/icons";
import { Skeleton } from "@dilpesh/kgk-ui-library";
import { getSessionItem, SESSION_KEYS } from "@magneto-it-solutions/kgk-common-library";
import { format } from "date-fns";
import Link from "next/link";

export const getPaymentReceiptsColumnDef = ({ isLoading }) => {
  return {
    settlementNo: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="140px" />
        ) : (
          <div className="icon_16_bes">
            <p>{item?.settlement_number || "-"}</p>
            {item?.invoiceImg}
          </div>
        ),
    },
    referenceNumber: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="100px" /> : <p>{item?.settlement_number || "-"}</p>,
    },
    date: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="140px" />
        ) : (
          <p>
            {item?.received_date_time
              ? format(
                new Date(item.received_date_time),
                getSessionItem(SESSION_KEYS.DATE_FORMAT) || "dd MMM yyyy, h:mm a"
              )
              : "-"}
          </p>
        ),
    },
    customerName: {
      renderChildren: (item: any) =>
        isLoading ? <Skeleton width="120px" /> : <p>{item?.customer || "-"}</p>,
    },
    bankAcName: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="140px" />
        ) : (
          <div className="icon_name_24_bes">
            {item?.bankIcon}
            <p>{item?.bankAcName || "-"}</p>
          </div>
        ),
    },
    action: {
      renderChildren: (item: any) =>
        isLoading ? (
          <Skeleton width="24px" />
        ) : (
          <div className="action_wpr">
            <Link href="#">
              <PrintIcon />
            </Link>
          </div>
        ),
    },
  };
};
