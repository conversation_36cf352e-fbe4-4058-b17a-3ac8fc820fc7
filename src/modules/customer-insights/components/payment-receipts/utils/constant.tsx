import { BankIcon } from "@/assets/icons/collection/BankIcon";
import { CheckIcon } from "@/assets/icons/collection/CheckIcon";

export const stoneCardHead = [
    {
        label: 'Settlement no',
        key: 'settlementNo'
    },
    {
        label: 'Reference number',
        key: 'referenceNumber'
    },
    {
        label: 'Date',
        key: 'date'
    },
    {
        label: 'Customer name',
        key: 'customerName'
    },
    {
        label: 'Bank / Cash A/c Name',
        key: 'bankAcName'
    },
    {
        label: 'Cheque no',
        key: 'chequeNo'
    },
    {
        label: 'Amount',
        key: 'amount'
    },
    {
        label: 'Action',
        key: 'action',
        class: 'action'
    },
];

export const stoneCardData = [
    {
        invoiceImg: <CheckIcon />,
        settlementNo: 'FSIL-230096',
        referenceNumber: 'Vegas Proposal/AG',
        date: '23 Mar, 2023 - 10:00 PM',
        customerName: '01EC2487',
        bankIcon: <BankIcon />,
        bankAcName: 'Bank of America',
        chequeNo: '5',
        amount: '$1,049.00',
    },
    {
        invoiceImg: <CheckIcon />,
        settlementNo: 'FSIL-230096',
        referenceNumber: 'Vegas Proposal/AG',
        date: '23 Mar, 2023 - 10:00 PM',
        customerName: '01EC2487',
        bankIcon: <BankIcon />,
        bankAcName: 'Bank of America',
        chequeNo: '5',
        amount: '$1,049.00',
    },
    {
        invoiceImg: <CheckIcon />,
        settlementNo: 'FSIL-230096',
        referenceNumber: 'Vegas Proposal/AG',
        date: '23 Mar, 2023 - 10:00 PM',
        customerName: '01EC2487',
        bankIcon: <BankIcon />,
        bankAcName: 'Bank of America',
        chequeNo: '5',
        amount: '$1,049.00',
    },
    {
        invoiceImg: <CheckIcon />,
        settlementNo: 'FSIL-230096',
        referenceNumber: 'Vegas Proposal/AG',
        date: '23 Mar, 2023 - 10:00 PM',
        customerName: '01EC2487',
        bankIcon: <BankIcon />,
        bankAcName: 'Bank of America',
        chequeNo: '5',
        amount: '$1,049.00',
    },

];


export const stoneInnerTableHead = [
    {
        label: 'Invoice no',
        key: 'invoiceNo'
    },
    {
        label: 'Reference number',
        key: 'referenceNumber'
    },
    {
        label: 'Date',
        key: 'date'
    },
    {
        label: 'Invoice amount',
        key: 'invoiceAmount'
    },
    {
        label: 'Settled amount',
        key: 'settledAmount'
    },
    {
        label: 'Discount',
        key: 'discount'
    },
];

export const stoneInnerData = [
    {
        invoiceNo: 'MF/098522',
        referenceNumber: 'GR-TOWSEND',
        date: '23 Mar, 2023',
        invoiceAmount: '$1,943.00',
        settledAmount: '$1,943.00',
        discount: '$0.00',
    },
];
