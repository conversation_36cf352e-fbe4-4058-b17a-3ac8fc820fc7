"use client";

import { FilterLineIcon } from "@/assets/icons/collection/FilterLineIcon";
import {
  DataTable,
  DataTableExpand,
  FilterDropdown,
  TableFooter,
  TableHeader,
} from "@dilpesh/kgk-ui-library";
import {
  useGetPaymentReceiptDetails,
  useGetPaymentReceiptFiltersQuery,
  useGetPaymentReceiptList,
  useGetTranslation,
  usePagination,
  usePayloadFilter
} from "@magneto-it-solutions/kgk-common-library";
import { useParams } from "next/navigation";
import { useRef, useState } from "react";
import {
  stoneCardHead,
  stoneInnerData,
  stoneInnerTableHead
} from "./utils/constant";
import { CustomerInsightsPaymentReceiptsstyle } from "./utils/style";
import { getPaymentReceiptsColumnDef } from "./utils/utility";

const CustomerInsightsPaymentReceipts = () => {
  const { translation } = useGetTranslation();
  const params = useParams();
  const id: string = String(params?.id);

  const {
    selectedFilter,
    search,
    changeSearch,
    appliedFilters,
    clearFilters,
    applyFilters,
    handleFilterChange,
  } = usePayloadFilter("paymentreceipt-list");

  const { page, limit, view, changeLimit, changePage } = usePagination(
    "paymentreceipt-list"
  );

  const { receipts, isLoading } = useGetPaymentReceiptList(id);
  const { data: paymentreceiptFilter } = useGetPaymentReceiptFiltersQuery({ customer_code: id });

  const [filterStatus, setFilterStatus] = useState<boolean>(false);

  const ref: any = useRef();

  const coloumnDefListing = getPaymentReceiptsColumnDef({ isLoading });

  return (
    <CustomerInsightsPaymentReceiptsstyle>
      <div className="container first_view_content">
        <div className="table_main_wpr">
          <TableHeader
            isSearch={true}
            skeleton={isLoading}
            searchPlaceholder={translation?.search_receipts}
            borderBtnText={translation?.filter}
            borderBtnIcon={<FilterLineIcon />}
            searchText={search}
            onSearchChange={(e: any) =>
              changeSearch(e.target.value.toLowerCase())
            }
          />
          <FilterDropdown
            open={filterStatus}
            close={() => setFilterStatus(!filterStatus)}
            data={paymentreceiptFilter?.filters}
            onChangeFilter={(filter, value) => {
              handleFilterChange(filter, value);
            }}
            selectedFilter={selectedFilter}
            onApplyFilter={applyFilters}
            fetchOptions={(_, searchText) => { }}
            clearAllBtnText={translation.ClearAll}
            applyBtntext={translation.Apply}
            filterText={translation.Filter}
          />
          <DataTableExpand
            isMultiselect={false}
            column={stoneCardHead}
            coloumnDef={coloumnDefListing}
            data={receipts?.data}
            skeleton={isLoading}
            collapsibleData={(item) => <PaymentReceiptsDetailsRow settlement_number={item?.settlement_number} />}
            ref={ref}
            fixedPosition="right"
          />
          <TableFooter
            skeleton={isLoading}
            currentPageSize={limit}
            currentPage={page}
            recordsTotal={receipts?.filteredRecords}
            setCurrentPage={changePage}
            setCurrentPageSize={changeLimit}
            showingText={translation.Showing}
            showLabel={translation.Show}
            OfText={translation.Of}
          />
        </div>
      </div>
    </CustomerInsightsPaymentReceiptsstyle>
  );
};

export default CustomerInsightsPaymentReceipts;

export const PaymentReceiptsDetailsRow = ({ settlement_number }: { settlement_number: string }) => {
  const { paymentReceiptDetails, } = useGetPaymentReceiptDetails(settlement_number);

  const coloumnDefStoneInner: Object = {
    invoiceNo: {
      renderChildren: (item: any) => {
        return (
          <div className="only_name_24_bes">
            <p>{item.invoiceNo}</p>
          </div>
        );
      },
    },
  };
  return (
    <td colSpan={stoneCardHead.length + 2} className="stone_card_expanded_td">
      <DataTable
        isMultiselect={false}
        column={stoneInnerTableHead}
        coloumnDef={coloumnDefStoneInner}
        data={stoneInnerData}
      />
    </td>
  );
}