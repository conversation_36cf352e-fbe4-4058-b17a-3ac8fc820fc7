export const stoneCardHead = [
    {
        label: 'Order number',
        key: 'orderNumber'
    },
    {
        label: 'Customer PO number',
        key: 'customerPoNumber'
    },
    {
        label: 'Customer code',
        key: 'customerCode'
    },
    {
        label: 'Type',
        key: 'type'
    },
    {
        label: 'Order date',
        key: 'orderDate'
    },
    {
        label: 'Shipment date',
        key: 'shipmentDate'
    },
    {
        label: 'Delivery date',
        key: 'deliveryDate'
    },
    {
        label: 'Salesman',
        key: 'salesman'
    },
    {
        label: 'Status',
        key: 'status'
    },
    {
        label: 'Quantity',
        key: 'quantity'
    },
    {
        label: 'Amount',
        key: 'amount'
    },
    {
        label: 'Action',
        key: 'action',
        class: 'action'
    }
];

export const stoneCardData = [
    {
        orderNumber: 'DSC572271',
        customerPoNumber: '340447/Aurie/Caitlin',
        customerCode: '01EC2487',
        type: 'Special',
        orderDate: '27 Mar 2023, 10:00 PM ',
        shipmentDate: '28 Mar, 2023 ',
        deliveryDate: '29 Mar, 2024',
        salesmanIcon: 'https://encrypted-tbn1.gstatic.com/images?q=tbn:ANd9GcTSsp8ZcUG7qn6RccmY8zBJN3aYRbo-zY12UJbvLfncoQM_baKp',
        salesman: 'Andrew Lewis',
        status: 'Open',
        quantity: '5',
        amount: '11,500.00',
    },
    {
        orderNumber: 'DSC572271',
        customerPoNumber: '340447/Aurie/Caitlin',
        customerCode: '01EC2487',
        type: 'Special',
        orderDate: '27 Mar 2023, 10:00 PM ',
        shipmentDate: '28 Mar, 2023 ',
        deliveryDate: '29 Mar, 2024',
        salesmanIcon: 'https://encrypted-tbn1.gstatic.com/images?q=tbn:ANd9GcTSsp8ZcUG7qn6RccmY8zBJN3aYRbo-zY12UJbvLfncoQM_baKp',
        salesman: 'Andrew Lewis',
        status: 'Open',
        quantity: '5',
        amount: '11,500.00',
    },
    {
        orderNumber: 'DSC572271',
        customerPoNumber: '340447/Aurie/Caitlin',
        customerCode: '01EC2487',
        type: 'Special',
        orderDate: '27 Mar 2023, 10:00 PM ',
        shipmentDate: '28 Mar, 2023 ',
        deliveryDate: '29 Mar, 2024',
        salesmanIcon: 'https://encrypted-tbn1.gstatic.com/images?q=tbn:ANd9GcTSsp8ZcUG7qn6RccmY8zBJN3aYRbo-zY12UJbvLfncoQM_baKp',
        salesman: 'Andrew Lewis',
        status: 'Open',
        quantity: '5',
        amount: '11,500.00',
    },
    {
        orderNumber: 'DSC572271',
        customerPoNumber: '340447/Aurie/Caitlin',
        customerCode: '01EC2487',
        type: 'Special',
        orderDate: '27 Mar 2023, 10:00 PM ',
        shipmentDate: '28 Mar, 2023 ',
        deliveryDate: '29 Mar, 2024',
        salesmanIcon: 'https://encrypted-tbn1.gstatic.com/images?q=tbn:ANd9GcTSsp8ZcUG7qn6RccmY8zBJN3aYRbo-zY12UJbvLfncoQM_baKp',
        salesman: 'Andrew Lewis',
        status: 'Open',
        quantity: '5',
        amount: '11,500.00',
    },

];


export const items = {
    dropId: 'super-action'
};

export const actionitemList = [
    {
        key: 1,
        value: 'Edit',
        class: 'black',
    },
    {
        key: 2,
        value: 'Remove',
        class: 'red',
    },
];

export const stoneInnerTableHead = [
    {
        label: 'Item',
        key: 'item'
    },
    {
        label: 'JID',
        key: 'jid'
    },
    {
        label: 'Job number',
        key: 'jobNumber'
    },
    {
        label: 'SKU number',
        key: 'skuNumber'
    },
    {
        label: 'Design number',
        key: 'designNumber'
    },
    {
        label: 'Metal',
        key: 'metal'
    },
    {
        label: 'Color',
        key: 'color'
    },
    {
        label: 'Product size',
        key: 'productSize'
    },
    {
        label: 'Quantity',
        key: 'quantity'
    },
    {
        label: 'Total',
        key: 'total'
    },
];

export const stoneInnerData = [
    {
        item: 'Diamond Vine Ring in 18k Gold',
        jid: '23K0441L',
        jobNumber: 'DVR18KG2314',
        skuNumber: 'DERS01XXSRR',
        designNumber: 'DERS28MOVR',
        itemIcon: '/crm/assets/img/ring-56.png',
        metal: '18k Gold',
        color: 'Yellow',
        productSize: '6',
        brand: 'Martin Flyer',
        quantity: '1',
        total: '$2,300.00',
    },
    {
        item: 'Diamond Vine Ring in 18k Gold',
        jid: '23K0441L',
        jobNumber: 'DVR18KG2314',
        skuNumber: 'DERS01XXSRR',
        designNumber: 'DERS28MOVR',
        itemIcon: '/crm/assets/img/ring-56.png',
        metal: '18k Gold',
        color: 'Yellow',
        productSize: '6',
        brand: 'Martin Flyer',
        quantity: '1',
        total: '$2,300.00',
    },
    {
        item: 'Diamond Vine Ring in 18k Gold',
        jid: '23K0441L',
        jobNumber: 'DVR18KG2314',
        skuNumber: 'DERS01XXSRR',
        designNumber: 'DERS28MOVR',
        itemIcon: '/crm/assets/img/ring-56.png',
        metal: '18k Gold',
        color: 'Yellow',
        productSize: '6',
        brand: 'Martin Flyer',
        quantity: '1',
        total: '$2,300.00',
    },
];