"use client"
import { ActionDropdown, DataTable, DataTableExpand, TableFooter, TableHeader, TableStatus } from "@dilpesh/kgk-ui-library";
import { useRef, useState } from "react";
import { CustomerInsightsOrdersstyle } from "./utils/style";
import { actionitemList, items, stoneCardData, stoneCardHead, stoneInnerData, stoneInnerTableHead } from "./utils/constant";
import { ActionIcon } from "@/assets/icons/collection/ActionIcon";
import { FilterLineIcon } from "@/assets/icons/collection/FilterLineIcon";
import { PlusIcon } from "@/assets/icons";


const CustomerInsightsOrders = () => {

    const [currentPage, setCurrentPage] = useState(1);
    const [currentPageSize, setCurrentPageSize] = useState(25);
    const [recordsTotal, setRecordsTotal] = useState(500);


    //**customer-insights-data-table-expand**/


    const coloumnDefStoneCard: Object = {
        orderNumber: {
            renderChildren: (item: any) => {
                return (
                    <div className='bes_500_wpr'>
                        <p>{item.orderNumber}</p>
                    </div>
                );
            },
        },
        salesman: {
            renderChildren: (item: any) => {
                return (
                    <div className='icon_name_24_bes'>
                        <div className="img_wpr">
                            <img src={item.salesmanIcon} alt="" />
                        </div>
                        <p>{item.salesman}</p>
                    </div>
                );
            },
        },
        type: {
            renderChildren: (item: any) => {
                return (
                    <TableStatus className="active">{item.type}</TableStatus>
                );
            },
        },
        status: {
            renderChildren: (item: any) => {
                return (
                    <TableStatus className="inprogress">{item.status}</TableStatus>
                );
            },
        },
        action: {
            renderChildren: (item: any) => {
                return (
                    <div className='action_wpr'>
                        <ActionDropdown items={items} actionList={actionitemList} actionIcon={<ActionIcon />} className="mx-auto" />
                    </div>
                );
            },
        },
    };



    const ref: any = useRef();


    const collapsibleData = () => {
        return (
            <td colSpan={stoneCardHead.length + 2} className='stone_card_expanded_td'>
                <DataTable
                    isMultiselect={false}
                    column={stoneInnerTableHead}
                    coloumnDef={coloumnDefStoneInner}
                    data={stoneInnerData}
                />
            </td>
        )
    }



    const coloumnDefStoneInner: Object = {
        item: {
            renderChildren: (item: any) => {
                return (
                    <div className='icon_name_24_bes_56'>
                        <div className="img_wpr">
                            <img src={item.itemIcon} alt="" />
                        </div>
                        <p>{item.item}</p>
                    </div>
                );
            },
        },
    };

    return (
        <CustomerInsightsOrdersstyle>
            <div className="container first_view_content">
                <div className="table_main_wpr">
                    <TableHeader
                        searchPlaceholder="Search orders"
                        borderBtnText="Filter"
                        borderBtnIcon={<FilterLineIcon />}
                        fillBtnText="Add"
                        fillBtnIcon={<PlusIcon />}
                        isSearch={true}
                    />
                    <DataTableExpand
                        isMultiselect={false}
                        column={stoneCardHead}
                        coloumnDef={coloumnDefStoneCard}
                        data={stoneCardData}
                        collapsibleData={collapsibleData}
                        expandIconPosition=""
                        ref={ref}
                        bodyClass="body_pt_12"
                        fixedPosition="right"
                    />
                    <TableFooter
                        currentPageSize={currentPageSize}
                        currentPage={currentPage}
                        recordsTotal={recordsTotal}
                        setCurrentPage={setCurrentPage}
                        setCurrentPageSize={setCurrentPageSize}
                        setRecordsTotal={setRecordsTotal}
                    />
                </div>
            </div>
        </CustomerInsightsOrdersstyle>
    )
}

export default CustomerInsightsOrders
