"use client";

import {
  DataTable,
  FilterDropdown,
  TableFooter,
  TableHeader,
} from "@dilpesh/kgk-ui-library";
import { useState } from "react";
import { assetsListingHead } from "./utils/constant";
import { CustomerInsightsstyle } from "./utils/style";
import { FilterLineIcon } from "@/assets/icons/collection/FilterLineIcon";
import {
  fetchModuleData,
  useGetCustomersFiltersQuery,
  useGetCustomersList,
  useGetTranslation,
  usePagination,
  usePayloadFilter,
} from "@magneto-it-solutions/kgk-common-library";
import { getCustomersColumnDef } from "./utils/utility";

const CustomerInsights = () => {
  const { translation } = useGetTranslation();

  const {
    selectedFilter,
    search,
    changeSearch,
    appliedFilters,
    clearFilters,
    applyFilters,
    handleFilterChange,
  } = usePayloadFilter("customers-list");

  const { page, limit, view, changeLimit, changePage } =
    usePagination("customers-list");

  const { data: customersFilter } = useGetCustomersFiltersQuery("");

  const { isLoading, error, customersListData } = useGetCustomersList();

  const { fetchUserList } = fetchModuleData();

  const [filterStatus, setFilterStatus] = useState<boolean>(false);

  const coloumnDefListing = getCustomersColumnDef({ isLoading });

  return (
    <CustomerInsightsstyle>
      <div className="container first_view_content">
        <div className="table_main_wpr">
          <TableHeader
            isSearch={true}
            skeleton={isLoading}
            searchPlaceholder="Search customer"
            borderBtnText={translation?.filter}
            borderBtnIcon={<FilterLineIcon />}
            searchText={search}
            onSearchChange={(e: any) =>
              changeSearch(e.target.value.toLowerCase())
            }
            borderBtnClick={() => setFilterStatus(!filterStatus)}
          />
          <FilterDropdown
            open={filterStatus}
            close={() => setFilterStatus(!filterStatus)}
            data={customersFilter?.filters}
            onChangeFilter={(filter, value) => {
              handleFilterChange(filter, value);
            }}
            selectedFilter={selectedFilter}
            onApplyFilter={applyFilters}
            onClearFilter={clearFilters}
            fetchOptions={(_, searchText) => fetchUserList(searchText)}
            clearAllBtnText={translation.ClearAll}
            applyBtntext={translation.Apply}
            filterText={translation.Filter}
          />
          <DataTable
            isMultiselect={false}
            column={assetsListingHead}
            coloumnDef={coloumnDefListing}
            data={customersListData?.data}
            skeleton={isLoading}
            bodyClass="body_pt_12"
            fixedPosition="right"
          />
          <TableFooter
            skeleton={isLoading}
            currentPageSize={limit}
            currentPage={page}
            recordsTotal={customersListData?.filteredRecords}
            setCurrentPage={changePage}
            setCurrentPageSize={changeLimit}
            showingText={translation.Showing}
            showLabel={translation.Show}
            OfText={translation.Of}
          />
        </div>
      </div>
    </CustomerInsightsstyle>
  );
};

export default CustomerInsights;
