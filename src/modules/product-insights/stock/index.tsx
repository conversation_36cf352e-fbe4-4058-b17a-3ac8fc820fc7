"use client"
import { DataTable, TableFooter } from "@dilpesh/kgk-ui-library";
import { useState } from "react";
import { ProductInsightsStockstyle } from "./utils/style";
import { assetsListingData, assetsListingHead } from "./utils/constant";


const ProductInsightsStock = () => {


    const [currentPage, setCurrentPage] = useState(1);
    const [currentPageSize, setCurrentPageSize] = useState(25);
    const [recordsTotal, setRecordsTotal] = useState(500);


    const coloumnDefAssetsListing: Object = {
        csc: {
            renderChildren: (item: any) => {
                return (
                    <div className='icon_name_24_8_bes'>
                        {item.cscIcon}
                        <p>{item.csc}</p>
                    </div>
                );
            },
        },
        customer: {
            renderChildren: (item: any) => {
                return (
                    <div className='icon_name_24_8_bes'>
                        {item.customerIcon}
                        <p>{item.customer}</p>
                    </div>
                );
            },
        },
    };


    return (
        <ProductInsightsStockstyle>
            <div className="container first_view_content">
                <div className="data_table_wpr">
                    <h2>DWBM39XSQ-109599</h2>
                    <DataTable
                        isMultiselect={false}
                        column={assetsListingHead}
                        coloumnDef={coloumnDefAssetsListing}
                        data={assetsListingData}
                    />
                    <TableFooter
                        currentPageSize={currentPageSize}
                        currentPage={currentPage}
                        recordsTotal={recordsTotal}
                        setCurrentPage={setCurrentPage}
                        setCurrentPageSize={setCurrentPageSize}
                        setRecordsTotal={setRecordsTotal}
                    />
                </div>
            </div>
        </ProductInsightsStockstyle>
    )
}

export default ProductInsightsStock
