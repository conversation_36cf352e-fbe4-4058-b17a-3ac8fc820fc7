"use client";
import {
  BreadCrumbs,
  DataTable,
  ProductGrid,
  TableFooter,
  TableHeader,
  TableStatus,
} from "@dilpesh/kgk-ui-library";
import { Col, Row } from "antd";
import { useState } from "react";
import { ProductInsightsstyle } from "./utils/style";
import { FilterLineIcon } from "@/assets/icons";
import {
  assetsListingData,
  assetsListingHead,
  breadCrumbsValue2,
  mainActionList,
  productGridData,
  toggleBtnData,
} from "./utils/constant";
import { useRouter } from "next/navigation";

const ProductInsights = () => {
  const router = useRouter();

  const coloumnDefAssetsListing: Object = {
    styleNumber: {
      renderChildren: (item: any) => {
        return (
          <div
            className="icon_name_24_8_bes"
            onClick={() =>
              router.push(`/product-insights/summary/${item.styleNumber}`)
            }
          >
            <p>{item.styleNumber}</p>
          </div>
        );
      },
    },
    stockType: {
      renderChildren: (item: any) => {
        return <TableStatus className="active">{item.stockType}</TableStatus>;
      },
    },
    action: {
      renderChildren: (item: any) => {
        return <div className="action_wpr">{item.actionIcon}</div>;
      },
    },
  };

  const [currentPage, setCurrentPage] = useState(1);
  const [currentPageSize, setCurrentPageSize] = useState(25);
  const [recordsTotal, setRecordsTotal] = useState(500);

  const [toggleBtnActiveBtn, setToggleBtnActiveBtn] = useState(
    toggleBtnData[0].value
  );

  return (
    <ProductInsightsstyle>
      <div className="container first_view_content">
        {/* <BreadCrumbs value={breadCrumbsValue2} />
                <h1>Product insights</h1> */}
        <div className="table_main_wpr">
          <TableHeader
            searchPlaceholder="Search product"
            actionList={mainActionList}
            toggleBtnActiveBtn={toggleBtnActiveBtn}
            toggleBtnOnChange={(e: any) =>
              setToggleBtnActiveBtn(e.target.value)
            }
            borderBtnText="Filter"
            toggleBtnData={toggleBtnData}
            borderBtnIcon={<FilterLineIcon />}
            isSearch={true}
          />
          {toggleBtnActiveBtn == "listing" ? (
            <DataTable
              isMultiselect={true}
              column={assetsListingHead}
              coloumnDef={coloumnDefAssetsListing}
              data={assetsListingData}
              fixedPosition="arrow_multiselect left-two right"
              bodyClass="body_pt_12"
            />
          ) : (
            <div className="grid_data_wpr">
              <Row gutter={{ xs: 12, sm: 24 }}>
                {productGridData?.map((item: any, index: number) => (
                  <Col
                    xs={12}
                    md={8}
                    lg={6}
                    xl={6}
                    xxl={4}
                    className="product_grid_column"
                    key={index}
                  >
                    <ProductGrid
                      {...item}
                      selection={true}
                      onClick={() =>
                        router.push(`/product-insights/summary/${item.ownerId}`)
                      }
                    />
                  </Col>
                ))}
              </Row>
            </div>
          )}
          <TableFooter
            currentPageSize={currentPageSize}
            currentPage={currentPage}
            recordsTotal={recordsTotal}
            setCurrentPage={setCurrentPage}
            setCurrentPageSize={setCurrentPageSize}
            setRecordsTotal={setRecordsTotal}
          />
        </div>
      </div>
    </ProductInsightsstyle>
  );
};

export default ProductInsights;
