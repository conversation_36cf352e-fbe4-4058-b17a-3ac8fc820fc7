"use client"
import { DataTable, TableFooter, TableStatus } from "@dilpesh/kgk-ui-library";
import { useState } from "react";
import { ProductInsightsOrdersstyle } from "./utils/style";
import { assetsListingData, assetsListingHead } from "./utils/constant";


const ProductInsightsOrders = () => {


    const [currentPage, setCurrentPage] = useState(1);
    const [currentPageSize, setCurrentPageSize] = useState(25);
    const [recordsTotal, setRecordsTotal] = useState(500);


    const coloumnDefAssetsListing: Object = {
        ssc: {
            renderChildren: (item: any) => {
                return (
                    <div className='icon_name_24_8_bes'>
                        {item.sscIcon}
                        <p>{item.ssc}</p>
                    </div>
                );
            },
        },
        customer: {
            renderChildren: (item: any) => {
                return (
                    <div className='icon_name_24_8_bes'>
                        {item.customerIcon}
                        <p>{item.customer}</p>
                    </div>
                );
            },
        },
        status: {
            renderChildren: (item: any) => {
                return (
                    <TableStatus className="inprogress">{item.status}</TableStatus>
                );
            },
        },
    };


    return (
        <ProductInsightsOrdersstyle>
            <div className="container first_view_content">
                <div className="data_table_wpr">
                    <h2>DWBM39XSQ-109599</h2>
                    <DataTable
                        isMultiselect={false}
                        column={assetsListingHead}
                        coloumnDef={coloumnDefAssetsListing}
                        data={assetsListingData}
                        bodyClass="body_pt_12"
                    />
                    <TableFooter
                        currentPageSize={currentPageSize}
                        currentPage={currentPage}
                        recordsTotal={recordsTotal}
                        setCurrentPage={setCurrentPage}
                        setCurrentPageSize={setCurrentPageSize}
                        setRecordsTotal={setRecordsTotal}
                    />
                </div>
            </div>
        </ProductInsightsOrdersstyle>
    )
}

export default ProductInsightsOrders
