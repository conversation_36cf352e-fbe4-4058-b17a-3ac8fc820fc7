import { bes, h4 } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const ProductInsightsOrdersstyle = styled.div`
  ${(props) =>
    css`
      .data_table_wpr {
        & > h2 {
          ${h4(props?.theme?.text?.high, 400)};
          margin-bottom: 32px;
        }
        .icon_name_24_8_bes {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          svg,
          img {
            width: 24px;
            height: 24px;
          }
          p {
            padding-left: 8px;
            ${bes(props?.theme?.text?.high, 400)}
          }
        }
        .data_footer_info {
          margin-top: 24px;
        }
      }
    `}
`;
