import { Media, bes, h3 } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const ProductInsightsstyle = styled.div`
  ${(props) =>
    css`
      padding-top: 32px;
      padding-bottom: 64px;
      .bread_crumbs {
        margin-bottom: 24px;
      }
      h1 {
        ${h3(props?.theme?.text?.high, 400)};
        margin-bottom: 40px;
      }
      .table_main_wpr {
        .table_header_info {
          margin-bottom: 24px;
        }
        .data_table {
          .icon_name_24_8_bes {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            img,
            svg {
              width: 24px;
              height: 24px;
            }
            p {
              padding-left: 8px;
              width: calc(100% - 24px);
              ${bes(props?.theme?.text?.high, 400)};
              cursor: pointer;
            }
          }
          .action_wpr {
            svg {
              width: 24px;
              height: 24px;
              margin: auto;
              path {
                stroke: ${props.theme.colors.brand_high};
              }
            }
          }
        }
        .grid_data_wpr {
          margin-bottom: 48px;
          .ant-row {
            margin-bottom: -40px;
            @media ${Media.tablet} {
              margin-bottom: -32px;
            }
            & > * {
              margin-bottom: 40px;
              @media ${Media.tablet} {
                margin-bottom: 32px;
              }
            }
          }
        }
        .data_footer_info {
          margin-top: 24px;
        }
      }
    `}
`;
