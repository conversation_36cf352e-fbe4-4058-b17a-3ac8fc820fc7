"use client"
import { DataTable, TableFooter } from "@dilpesh/kgk-ui-library";
import { useState } from "react";
import { ProductInsightsSalesstyle } from "./utils/style";
import { assetsListingData, assetsListingHead } from "./utils/constant";


const ProductInsightsSales = () => {


    const [currentPage, setCurrentPage] = useState(1);
    const [currentPageSize, setCurrentPageSize] = useState(25);
    const [recordsTotal, setRecordsTotal] = useState(500);


    const coloumnDefAssetsListing: Object = {
        customer: {
            renderChildren: (item: any) => {
                return (
                    <div className='icon_name_24_8_bes'>
                        {item.customerIcon}
                        <p>{item.customer}</p>
                    </div>
                );
            },
        },
    };


    return (
        <ProductInsightsSalesstyle>
            <div className="container first_view_content">
                <div className="data_table_wpr">
                    <h2>DWBM39XSQ-109599</h2>
                    <DataTable
                        isMultiselect={false}
                        column={assetsListingHead}
                        coloumnDef={coloumnDefAssetsListing}
                        data={assetsListingData}
                    />
                    <TableFooter
                        currentPageSize={currentPageSize}
                        currentPage={currentPage}
                        recordsTotal={recordsTotal}
                        setCurrentPage={setCurrentPage}
                        setCurrentPageSize={setCurrentPageSize}
                        setRecordsTotal={setRecordsTotal}
                    />
                </div>
            </div>
        </ProductInsightsSalesstyle>
    )
}

export default ProductInsightsSales
