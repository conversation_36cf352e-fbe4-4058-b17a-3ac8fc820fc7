import { Btn, InputGroups, SelectGroups, TextareaGroups } from "@dilpesh/kgk-ui-library"
import { Col, Row } from "antd"
import { StyleDetailsstyle } from "./style-details-style"

const StyleDetails = () => {
    return (
        <StyleDetailsstyle>
            <h2 className="this_content_header_wpr">Style details</h2>
            <div className="this_content_body_wpr">
                <Row gutter={{ sm: 24 }} className="field_gap_24">
                    <Col md={12} xl={8}>
                        <InputGroups label="Style number" />
                    </Col>
                    <Col md={12} xl={8}>
                        <InputGroups label="Design number" />
                    </Col>
                    <Col md={12} xl={8}>
                        <SelectGroups label="Stock type" />
                    </Col>
                    <Col md={12} xl={8}>
                        <SelectGroups label="Product size" />
                    </Col>
                    <Col md={12} xl={8}>
                        <SelectGroups label="Metal" />
                    </Col>
                    <Col md={12} xl={8}>
                        <SelectGroups label="Diamond quality" />
                    </Col>
                    <Col md={12} xl={8}>
                        <SelectGroups label="Color" />
                    </Col>
                    <Col md={12} xl={8}>
                        <SelectGroups label="Clarity" />
                    </Col>
                    <Col md={12} xl={8}>
                        <InputGroups label="Sales Price" />
                    </Col>
                    <Col md={12} xl={8}>
                        <InputGroups label="MSRP" />
                    </Col>
                    <Col md={12} xl={8}>
                        <InputGroups label="Tag desc" />
                    </Col>
                    <Col md={12} xl={8}>
                        <InputGroups label="Style reference number" />
                    </Col>
                    <Col md={24} xl={12}>
                        <TextareaGroups label="SO description" rows={3} />
                    </Col>
                    <Col md={24} xl={12}>
                        <TextareaGroups label="SO description" rows={3} />
                    </Col>
                    <Col md={12} xl={8}>
                        <SelectGroups label="Stone card locked" />
                    </Col>
                    <Col md={12} xl={8}>
                        <InputGroups label="Stone card locked date" />
                    </Col>
                </Row>
            </div>
            <div className="this_content_footer_wpr">
                <Btn bg="fill" size="large">Next</Btn>
            </div>
        </StyleDetailsstyle>
    )
}

export default StyleDetails
