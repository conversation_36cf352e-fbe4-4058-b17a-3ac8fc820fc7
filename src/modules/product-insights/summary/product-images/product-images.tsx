import React from 'react'
import { Navigation } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import { shapeData } from '../utils/constant';
import { ProductImagesStyle } from './product-images-style';
import { LeftArrow } from '@/assets/icons/collection/LeftArrow';
import { RightArrow } from '@/assets/icons/collection/RightArrow';

const ProductImages = () => {
    return (
        <ProductImagesStyle>
            <div className='swiper_main_wpr'>
                <div className="swiper-button prev shape-prev">
                    <LeftArrow />
                </div>
                <div className="swiper-button next shape-next">
                    <RightArrow />
                </div>
                <Swiper
                    navigation={{
                        prevEl: ".shape-prev",
                        nextEl: ".shape-next",
                        disabledClass: "disabled"
                    }}
                    modules={[Navigation]}
                    grabCursor={true}
                    breakpoints={{
                        768: {
                            slidesPerView: 3,
                            spaceBetween: 24,
                        },
                        1200: {
                            slidesPerView: 4,
                            spaceBetween: 24,
                        },

                    }}
                    className="mySwiper">
                    {
                        shapeData?.map((item: any) => (
                            <SwiperSlide key={item.title + 'shape_box'}>
                                <div className="slider_img_wpr">
                                    <picture>
                                        <source media='(min-width: 1200)' srcSet={item.imgData} />
                                        <img src={item.imgData} alt='' />
                                    </picture>
                                </div>
                            </SwiperSlide>
                        ))
                    }
                </Swiper>
            </div>
        </ProductImagesStyle>
    )
}

export default ProductImages
