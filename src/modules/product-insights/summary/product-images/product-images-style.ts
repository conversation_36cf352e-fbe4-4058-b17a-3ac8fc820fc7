import { Media, bes, bl, br, h4 } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const ProductImagesStyle = styled.div`
  ${(props) =>
    css`
      .swiper_main_wpr {
        position: relative;
        margin-bottom: 48px;
        .swiper-button {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          height: 40px;
          width: 40px;
          cursor: pointer;
          z-index: 2;
          @media ${Media.below_1399} {
            height: 32px;
            width: 32px;
          }
          @media ${Media.tablet} {
            display: none;
          }
          svg {
            path {
              stroke: ${props.theme.colors.brand_high};
              transition: all 200ms ease-in;
            }
          }
          &.prev {
            left: 16px;
            @media ${Media.below_1399} {
              left: 12px;
            }
          }
          &.next {
            right: 16px;
            @media ${Media.below_1399} {
              right: 12px;
            }
          }
          &.disabled {
            path {
              display: none;
              stroke: ${(props) => props?.theme?.colors?.brand_mid};
              transition: all 200ms ease-in;
            }
          }
        }
        .slider_img_wpr {
          max-width: 408px;
          max-height: 408px;
        }
        .swiper {
          position: static;
          @media ${Media.tablet} {
            overflow: visible;
          }
        }
      }
    `}
`;
