import { Media, bes, bl, br, h4 } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const ProductInsightsSummarystyle = styled.div`
  ${(props) =>
    css`
      overflow: hidden;
      .details_wpr_main {
        & > h2 {
          ${h4(props?.theme?.text?.high, 400)};
          margin-bottom: 32px;
        }

        .tab_content_wpr {
          .tab_data_wpr {
            border-radius: 4px;
            overflow: hidden;
            ul {
              li {
                display: block;
                padding: 18px 32px;
                ${br(props.theme.text.high, 500)};
                background-color: ${(props) => props.theme.colors.brand_low};
                transition: all 200ms ease-in;
                @media ${Media.tablet} {
                  padding: 12px 16px;
                  font-size: 16px;
                  line-height: 24px;
                }
                &:hover,
                &.active {
                  background-color: ${(props) => props.theme.colors.brand_mid};
                  cursor: pointer;
                  transition: all 200ms ease-in;
                }
              }
            }
          }
          .content_data_wpr {
            padding-left: 24px;
            @media ${Media.below_1399} {
              padding-left: 12px;
            }
            @media ${Media.desktop} {
              padding-left: 0;
            }
            .this_content_header_wpr {
              ${bl(props.theme.text.high, 500)};
              padding-bottom: 24px;
              border-bottom: 1px solid ${(props) => props.theme.line.light};
            }
            .this_content_body_wpr {
              padding-top: 24px;
            }
            .this_content_footer_wpr {
              margin-top: 32px;
              border-top: 1px solid ${(props) => props.theme.line.light};
              padding-top: 24px;
              display: flex;
              justify-content: flex-end;
              align-items: center;
              @media ${Media.tablet} {
                margin-top: 24px;
              }
              & > * {
                width: 120px;
                margin-right: 24px;
                &:last-child {
                  margin-right: 0;
                }
              }
            }
          }
        }
      }
    `}
`;
