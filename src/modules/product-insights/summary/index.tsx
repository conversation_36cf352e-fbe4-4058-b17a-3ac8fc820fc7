"use client"
import { Col, Row } from "antd";
import { useState } from "react";

import { ProductInsightsSummarystyle } from "./utils/style";
import { tabLinks } from "./utils/constant";
import StyleDetails from "./style-details/style-details";
import DesignDetails from "./design-details/design-details";
import LabelDetails from "./label-details/label-details";
import UserInformation from "./user-information/user-information";
import ProductImages from "./product-images/product-images";



const ProductInsightsSummary = () => {


    const [activeTab, setActiveTab] = useState(tabLinks[0].link);

    const viewRender = () => {
        switch (activeTab) {
            case 'style_details':
                return <StyleDetails />

            case 'design_details':
                return <DesignDetails />

            case 'label_details':
                return <LabelDetails />
        }
    }

    return (
        <ProductInsightsSummarystyle>
            <div className="container first_view_content">
                <div className="details_wpr_main">
                    <h2>DWBM39XSQ-109599</h2>
                    <UserInformation />
                    <ProductImages />

                    <div className='tab_content_wpr'>
                        <Row gutter={{ md: 24 }}>
                            <Col md={8} lg={7} xl={6} xxl={6}>
                                <div className='tab_data_wpr'>
                                    <ul>
                                        {
                                            tabLinks?.map((item: any, index: number) => (
                                                <li
                                                    key={index}
                                                    className={`${activeTab == item.link ? 'active' : ''}`}
                                                    onClick={() => setActiveTab(item.link)}
                                                >{item.name}</li>
                                            ))
                                        }
                                    </ul>
                                </div>
                            </Col>
                            <Col md={16} lg={17} xl={18} xxl={18}>
                                <div className='content_data_wpr'>
                                    {viewRender()}
                                </div>
                            </Col>
                        </Row>
                    </div>
                </div>
            </div>
        </ProductInsightsSummarystyle>
    )
}

export default ProductInsightsSummary
