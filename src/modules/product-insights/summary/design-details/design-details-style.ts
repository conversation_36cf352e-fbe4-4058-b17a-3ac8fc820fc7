import { bes } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const DesignDetailsstyle = styled.div`
  ${(props) =>
    css`
      .inner_data {
        display: flex;
        align-items: flex-start;
        justify-content: flex-start;
        p {
          ${bes(props?.theme?.text?.mid, 400)};
          width: 140px;
        }
        span {
          padding-left: 24px;
          display: block;
          width: calc(100% - 140px);
          ${bes(props?.theme?.text?.high, 500)};
        }
      }
    `}
`;
