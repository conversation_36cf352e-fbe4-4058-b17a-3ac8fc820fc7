import { Btn } from "@dilpesh/kgk-ui-library"
import { Col, Row } from "antd"
import { DesignDetailsstyle } from "./design-details-style"

const DesignDetails = () => {
    return (
        <DesignDetailsstyle>
            <h2 className="this_content_header_wpr">Design details</h2>
            <div className="this_content_body_wpr">
                <Row gutter={{ sm: 24 }} className="field_gap_24">
                    <Col md={24} lg={12} xxl={8}>
                        <div className="inner_data">
                            <p>Head shape</p>
                            <span>Round</span>
                        </div>
                    </Col>
                    <Col md={24} lg={12} xxl={8}>
                        <div className="inner_data">
                            <p>Head size</p>
                            <span>6.5</span>
                        </div>
                    </Col>
                    <Col md={24} lg={12} xxl={8}>
                        <div className="inner_data">
                            <p>weight</p>
                            <span>0</span>
                        </div>
                    </Col>
                    <Col md={24} lg={12} xxl={8}>
                        <div className="inner_data">
                            <p>Product width</p>
                            <span>-</span>
                        </div>
                    </Col>
                    <Col md={24} lg={12} xxl={8}>
                        <div className="inner_data">
                            <p>Product height</p>
                            <span>-</span>
                        </div>
                    </Col>
                    <Col md={24} lg={12} xxl={8}>
                        <div className="inner_data">
                            <p>Height from finger</p>
                            <span>-</span>
                        </div>
                    </Col>
                    <Col md={24} lg={12} xxl={8}>
                        <div className="inner_data">
                            <p>Halo width</p>
                            <span>-</span>
                        </div>
                    </Col>
                    <Col md={24} lg={12} xxl={8}>
                        <div className="inner_data">
                            <p>Cad image</p>
                            <span>-</span>
                        </div>
                    </Col>
                    <Col md={24} lg={12} xxl={8}>
                        <div className="inner_data">
                            <p>Matching band</p>
                            <span>DWBS23XS</span>
                        </div>
                    </Col>
                    <Col md={24} lg={12} xxl={8}>
                        <div className="inner_data">
                            <p>Related</p>
                            <span>DWBS23XS</span>
                        </div>
                    </Col>
                    <Col md={24} lg={12} xxl={8}>
                        <div className="inner_data">
                            <p>Related 2</p>
                            <span>DERS37XSR</span>
                        </div>
                    </Col>
                    <Col md={24} lg={12} xxl={8}>
                        <div className="inner_data">
                            <p>Related 3</p>
                            <span>DERS36XS</span>
                        </div>
                    </Col>
                </Row>
            </div>
            <div className="this_content_footer_wpr">
                <Btn bg="fill" size="large">Next</Btn>
            </div>
        </DesignDetailsstyle>
    )
}

export default DesignDetails
