import React from 'react'
import { Col, <PERSON> } from "antd";
import { UserImformationStyle } from './user-information-style';

const UserInformation = () => {
    return (
        <UserImformationStyle>
            <div className='this_project_info_wpr'>
                <Row gutter={{ md: 24 }}>
                    <Col md={12} lg={8} xxl={6}>
                        <div className='title_value_wpr'>
                            <span>Created by</span>
                            <div className='it_24_besm_8'>
                                <div className='img_wpr'>
                                    <img src='https://encrypted-tbn1.gstatic.com/images?q=tbn:ANd9GcTSsp8ZcUG7qn6RccmY8zBJN3aYRbo-zY12UJbvLfncoQM_baKp' />
                                </div>
                                <p><PERSON></p>
                            </div>
                        </div>
                    </Col>
                    <Col md={12} lg={8} xxl={6}>
                        <div className='title_value_wpr'>
                            <span>Created on</span>
                            <p>24 Mar, 2023, 06:00 PM</p>
                        </div>
                    </Col>
                    <Col md={12} lg={8} xxl={6}>
                        <div className='title_value_wpr'>
                            <span>Updated by</span>
                            <div className='it_24_besm_8'>
                                <div className='img_wpr'>
                                    <img src='https://encrypted-tbn1.gstatic.com/images?q=tbn:ANd9GcTSsp8ZcUG7qn6RccmY8zBJN3aYRbo-zY12UJbvLfncoQM_baKp' />
                                </div>
                                <p>Jenny Wilson</p>
                            </div>
                        </div>
                    </Col>
                    <Col md={12} lg={8} xxl={6}>
                        <div className='title_value_wpr'>
                            <span>Updated on</span>
                            <p>29 Mar, 2023, 03:00 PM</p>
                        </div>
                    </Col>
                </Row>
            </div>
        </UserImformationStyle>
    )
}

export default UserInformation
