import { Media, bes, bl, br, h4 } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const UserImformationStyle = styled.div`
  ${(props) =>
    css`
      .this_project_info_wpr {
        background-color: ${(props) => props.theme.colors.brand_low};
        border-radius: 4px;
        padding: 32px;
        margin-bottom: 24px;
        @media ${Media.desktop} {
          padding: 24px;
        }
        .ant-row {
          margin-top: -16px;
          & > * {
            margin-top: 16px;
          }
        }
        .title_value_wpr {
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;
          span {
            display: block;
            ${bes(props.theme.text.mid, 400)};
            width: 128px;
            @media ${Media.desktop} {
              width: 100px;
            }
          }
          & > p {
            ${bes(props.theme.text.high, 500)};
            width: calc(100% - 128px);
            padding-left: 24px;
            @media ${Media.desktop} {
              width: calc(100% - 100px);
              padding-left: 16px;
            }
          }
          .it_24_besm_8 {
            width: calc(100% - 128px);
            padding-left: 24px;
            @media ${Media.desktop} {
              width: calc(100% - 100px);
              padding-left: 16px;
            }
          }
        }
      }
    `}
`;
