import { Btn } from "@dilpesh/kgk-ui-library"
import { Col, Row } from "antd"
import { LabelDetailsstyle } from "./label-details-style"

const LabelDetails = () => {

    return (
        <LabelDetailsstyle>
            <h2 className="this_content_header_wpr">Label details</h2>
            <div className="this_content_body_wpr">
                <Row gutter={{ sm: 24 }} className="field_gap_24">
                    <Col md={24} lg={12} xxl={8}>
                        <div className="inner_data">
                            <p>Label number</p>
                            <span>DERS37XSR</span>
                        </div>
                    </Col>
                    <Col md={24} lg={12} xxl={8}>
                        <div className="inner_data">
                            <p>Brand</p>
                            <span>Flyerfit</span>
                        </div>
                    </Col>
                    <Col md={24} lg={12} xxl={8}>
                        <div className="inner_data">
                            <p>Category 1</p>
                            <span>Get engaged</span>
                        </div>
                    </Col>
                    <Col md={24} lg={12} xxl={8}>
                        <div className="inner_data">
                            <p>Category 2</p>
                            <span>Choose your Style</span>
                        </div>
                    </Col>
                    <Col md={24} lg={12} xxl={8}>
                        <div className="inner_data">
                            <p>Category 3</p>
                            <span>Solitaire</span>
                        </div>
                    </Col>
                    <Col md={24} lg={12} xxl={8}>
                        <div className="inner_data">
                            <p>Collection 1</p>
                            <span>Solitaire</span>
                        </div>
                    </Col>
                    <Col md={24} lg={12} xxl={8}>
                        <div className="inner_data">
                            <p>Jewellery type</p>
                            <span>Engagement Ring</span>
                        </div>
                    </Col>
                    <Col md={24}>
                        <div className="inner_data">
                            <p>Long description</p>
                            <span>Stunningly simple, this FlyerFit half-round solitaire engagement ring with a Six Prong Head and </span>
                        </div>
                    </Col>
                </Row>
            </div>
            <div className="this_content_footer_wpr">
                <Btn bg="fill" size="large">Next</Btn>
            </div>
        </LabelDetailsstyle>
    )
}

export default LabelDetails
