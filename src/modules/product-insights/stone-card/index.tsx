"use client"
import { Btn, CustomeDropdown, DataTable, DataTableExpand, SQBtn } from "@dilpesh/kgk-ui-library";
import { useLayoutEffect, useRef, useState } from "react";
import { ProductInsightsStoneCardstyle } from "./utils/style";
import { DownIcon } from "@/assets/icons";
import { items, itemsForList, stoneCardData, stoneCardHead, stoneInnerData, stoneInnerTableHead } from "./utils/constant";
import { EditIcon } from "@/assets/icons/collection/EditIcon";


const ProductInsightsStoneCard = () => {



    const coloumnDefStoneCard: Object = {
        image: {
            renderChildren: (item: any) => {
                return (
                    <div className="only_img_wpr">
                        <img src={item.image} alt="" />
                    </div>
                );
            },
        },
        createdBy: {
            renderChildren: (item: any) => {
                return (
                    <div className='icon_name_24_bes'>
                        <div className="img_wpr">
                            <img src={item.createdByIcon} alt="" />
                        </div>
                        <p>{item.createdBy}</p>
                    </div>
                );
            },
        },
        modifiedBy: {
            renderChildren: (item: any) => {
                return (
                    <div className='icon_name_24_bes'>
                        <div className="img_wpr">
                            <img src={item.modifiedByIcon} alt="" />
                        </div>
                        <p>{item.modifiedBy}</p>
                    </div>
                );
            },
        },
    };

    const ref: any = useRef();

    const [width, setWidth] = useState(0);

    useLayoutEffect(() => {
        setWidth(ref.current.offsetWidth);
    }, []);

    const collapsibleData = () => {
        return (
            <td colSpan={stoneCardHead.length + 2} className='stone_card_expanded_td'>
                <DataTable
                    isMultiselect={true}
                    column={stoneInnerTableHead}
                    coloumnDef={coloumnDefStoneInner}
                    data={stoneInnerData}
                />
            </td>
        )
    }



    const coloumnDefStoneInner: Object = {
        image: {
            renderChildren: (item: any) => {
                return (
                    <div className="only_img_wpr">
                        <img src={item.image} alt="" />
                    </div>
                );
            },
        },
        shape: {
            renderChildren: (item: any) => {
                return (
                    <CustomeDropdown
                        items={itemsForList}
                        actionList={items}
                    >
                        Round
                        <DownIcon />
                    </CustomeDropdown>
                );
            },
        },
        color: {
            renderChildren: (item: any) => {
                return (
                    <CustomeDropdown
                        items={itemsForList}
                        actionList={items}
                    >
                        White
                        <DownIcon />
                    </CustomeDropdown>
                );
            },
        },
        cut: {
            renderChildren: (item: any) => {
                return (
                    <CustomeDropdown
                        items={itemsForList}
                        actionList={items}
                    >
                        Princess
                        <DownIcon />
                    </CustomeDropdown>
                );
            },
        },
    };

    return (
        <ProductInsightsStoneCardstyle>
            <div className="container first_view_content">
                <div className="data_table_wpr">
                    <h2>DWBM39XSQ-109599</h2>
                    <div className='this_table_header'>
                        <h2>Stone card</h2>
                        <div className='this_btn_wpr'>
                            <SQBtn size='large' bg="border"><EditIcon /></SQBtn>
                            <Btn size='large' bg="fill">Add</Btn>
                        </div>
                    </div>
                    <DataTableExpand
                        isMultiselect={true}
                        column={stoneCardHead}
                        coloumnDef={coloumnDefStoneCard}
                        data={stoneCardData}
                        collapsibleData={collapsibleData}
                        ref={ref}
                    />
                </div>
            </div>
        </ProductInsightsStoneCardstyle>
    )
}

export default ProductInsightsStoneCard
