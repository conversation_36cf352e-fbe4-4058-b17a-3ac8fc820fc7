import { StoneCardImg } from "@/assets/icons/collection/StoneCardImg";
import { OnlyCk } from "@dilpesh/kgk-ui-library";

export const stoneCardHead = [
    {
        label: 'Image',
        key: 'image'
    },
    {
        label: 'Stone card number',
        key: 'stoneCardNumber'
    },
    {
        label: 'Commodity',
        key: 'commodity'
    },
    {
        label: 'Suffix',
        key: 'suffix'
    },
    {
        label: 'Diamond Cts',
        key: 'diamondCts'
    },
    {
        label: 'Diamond  Pcs',
        key: 'diamondPcs'
    },
    {
        label: 'Stone Cts',
        key: 'stoneCts'
    },
    {
        label: 'Stone  Pcs',
        key: 'stonePcs'
    },
    {
        label: 'created by',
        key: 'createdBy'
    },
    {
        label: 'Created on',
        key: 'createdOn'
    },
    {
        label: 'Modified by',
        key: 'modifiedBy'
    },
    {
        label: 'Modified on',
        key: 'modifiedOn'
    },
];

export const stoneCardData = [
    {
        image: <StoneCardImg />,
        stoneCardNumber: 'DSC572271',
        commodity: 'Diamond',
        suffix: 'Suffix',
        diamondCts: '0.10',
        diamondPcs: '27',
        stoneCts: '0.10',
        stonePcs: '27',
        createdByIcon: 'https://encrypted-tbn1.gstatic.com/images?q=tbn:ANd9GcTSsp8ZcUG7qn6RccmY8zBJN3aYRbo-zY12UJbvLfncoQM_baKp',
        createdBy: 'Jenny Wilson',
        createdOn: '24 Mar, 2023 ',
        modifiedByIcon: 'https://encrypted-tbn1.gstatic.com/images?q=tbn:ANd9GcTSsp8ZcUG7qn6RccmY8zBJN3aYRbo-zY12UJbvLfncoQM_baKp',
        modifiedBy: 'Jenny Wilson',
        modifiedOn: '28 Mar, 2023 ',
    },
];

export const stoneInnerTableHead = [
    {
        label: 'RM',
        key: 'rm'
    },
    {
        label: 'Shape',
        key: 'shape'
    },
    {
        label: 'Color',
        key: 'color'
    },
    {
        label: 'Cut',
        key: 'cut'
    },
    {
        label: 'Sieve',
        key: 'sieve'
    },
    {
        label: 'MM',
        key: 'mm'
    },
    {
        label: 'PP wt.',
        key: 'ppWt'
    },
    {
        label: 'Pieces',
        key: 'pieces'
    },
    {
        label: 'Min wt.',
        key: 'minWt'
    },
    {
        label: 'Max wt.',
        key: 'maxWt'
    },
    {
        label: 'Setting',
        key: 'setting'
    },
    {
        label: 'Method',
        key: 'method'
    },
    {
        label: 'Center',
        key: 'center',
        class: 'center filter_none',
    },
    {
        label: 'Band',
        key: 'band',
        class: 'center filter_none',
    },
    {
        label: 'Client supply',
        key: 'clientSupply',
        class: 'center filter_none',
    },
];

export const stoneInnerData = [
    {
        rm: 'Dia',
        shape: 'Round',
        color: 'White',
        cut: 'Princess',
        sieve: '000-001',
        mm: '10.9 x 8.2',
        ppWt: '0.01500',
        pieces: '1',
        minWt: '0.072',
        maxWt: '0.150',
        setting: 'Micro Pave',
        method: 'Hand',
        center: <OnlyCk />,
        band: <OnlyCk />,
        clientSupply: <OnlyCk />,
    },
    {
        rm: 'Dia',
        shape: 'Round',
        color: 'White',
        cut: 'Princess',
        sieve: '000-001',
        mm: '10.9 x 8.2',
        ppWt: '0.01500',
        pieces: '1',
        minWt: '0.072',
        maxWt: '0.150',
        setting: 'Micro Pave',
        method: 'Hand',
        center: <OnlyCk />,
        band: <OnlyCk />,
        clientSupply: <OnlyCk />,
    },
];

export const items = [
    {
        value: 'Round',
        key: '0',
    },
    {
        value: 'Commenter',
        key: '1',
    },
    {
        value: 'Princess',
        key: '2',
    },
    {
        value: 'Emerald',
        key: '3',
    },
    {
        value: 'Cushion',
        key: '4',
    },
    {
        value: 'Heart',
        key: '5',
    },
    {
        value: 'Asscher',
        key: '6',
    },

];

export const itemsForList = {
    dropId: 'super-action'
};
