import { b2xs, bes, bl, h4 } from "@dilpesh/kgk-ui-library";
import styled, { css } from "styled-components";

export const ProductInsightsStoneCardstyle = styled.div`
  ${(props) =>
    css`
      .data_table_wpr {
        & > h2 {
          ${h4(props?.theme?.text?.high, 400)};
          margin-bottom: 32px;
        }
        .this_table_header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
          h2 {
            ${bl(props.theme.text.high, 500)};
          }
          .this_btn_wpr {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            & > * {
              margin-right: 16px;
              &:last-child {
                margin-right: 0;
              }
            }
            .fill-btn {
              width: 120px;
            }
          }
        }
        .data_table_expand {
          margin-top: 0;
          .only_img_wpr {
            height: 24px;
            width: 24px;
          }
          .project_number_wpr {
            cursor: pointer;
            display: inline-block;
            ${bes(props?.theme?.text?.high, 500)};
          }
          .design_wpr {
            height: 32px;
            width: 32px;
            border-radius: 100%;
            border: 1px solid ${(props) => props?.theme?.line?.light};
            display: flex;
            justify-content: center;
            align-items: center;
            ${bes(props?.theme?.text?.high, 500)}
            margin-left: auto;
            margin-right: auto;
          }
          .icon_name_24_bes {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            svg,
            .img_wpr {
              height: 24px;
              width: 24px;
            }
            p {
              padding-left: 8px;
              ${bes(props?.theme?.text?.high, 400)}
            }
          }
          .stone_card_expanded_td {
            padding-top: 0;
            padding-bottom: 0;
            .data_table {
              overflow-x: unset;
            }
            table {
              thead {
                tr {
                  th {
                    border-bottom: 1px solid
                      ${(props) => props?.theme?.line?.light};
                    p {
                      ${b2xs(props.theme.text.high, 500)}
                    }
                    &.multiselect {
                      width: 80px;
                      padding-left: 44px;
                    }
                  }
                }
              }
              tbody {
                tr {
                  td {
                    background-color: ${(props) =>
                      props?.theme?.colors.brand_low};
                    border-bottom: 1px solid
                      ${(props) => props?.theme?.line?.light};
                    &.multiselect {
                      width: 80px;
                      padding-left: 44px;
                    }
                    .ant-dropdown-trigger {
                      display: inline-flex;
                      align-items: center;
                      ${bes(props.theme.text.high, 400)}
                      cursor: pointer;
                      svg {
                        height: 16px;
                        width: 16px;
                        margin-left: 4px;
                      }
                    }
                    .only-ck {
                      margin: auto;
                    }
                  }
                  &:last-child {
                    td {
                      border-bottom: 0;
                    }
                  }
                }
              }
            }
          }
        }
      }
    `}
`;
