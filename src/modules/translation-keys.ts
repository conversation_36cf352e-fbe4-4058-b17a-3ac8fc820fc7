// Translation keys object for the CRM application
// This object contains all the translation keys that should be added to the translation system

export const translationKeys = {
  // Common UI elements
  filter_accessories: 'Filter accessories',
  search_customer_code: 'Search customer code',
  add: 'Add',
  edit: 'Edit',
  remove: 'Remove',
  filter: 'Filter',
  designs: 'Designs',
  concept: 'Concept',
  styles: 'Styles',
  
  // Table headers - Accessories
  group_name: 'Group name',
  customer: 'Customer',
  commodity: 'Commodity',
  color: 'Color',
  chart_base: 'Chart base',
  lot_code: 'Lot code',
  sp_rate: 'SP rate',
  updated_by: 'Updated by',
  updated_on: 'Updated on',
  action: 'Action',
  
  // Table headers - Address
  address_type: 'Address type',
  country: 'Country',
  address: 'Address',
  country_code: 'Country code',
  state_code: 'State code',
  default_address: 'Default address',
  actions: 'Actions',
  
  // Table headers - Contacts
  contact_name: 'Contact name',
  contact_type_designation: 'Contact type/Designation',
  phone: 'Phone',
  mobile: 'Mobile',
  email: 'Email',
  internal_remarks: 'Internal Remarks',
  main_contact: 'Main contact',
  
  // Table headers - Exclusive Designs
  image: 'Image',
  design_number: 'Design number',
  design_brief_number: 'Design brief number',
  jewellery_type: 'Jewellery type',
  business_category: 'Business category',
  market: 'Market',
  approx_model_wt: 'Approx. model wt.',
  kgk_collection: 'KGK collection',
  
  // Table headers - Exclusive Styles
  style_number: 'Style number',
  stone_card_locked: 'Stone card locked',
  exclusive: 'Exclusive',
  
  // Table headers - Exclusive Concepts
  concept_number: 'Concept number',
  concept_name: 'Concept name',
  concept_by: 'Concept by',
  name: 'Name',
  received_on: 'Received on',
  created_by: 'Created by',
  
  // Search placeholders
  search_address: 'Search address',
  search_receipts: 'Search receipts',
  search_memo: 'Search memo',
  
  // Navigation and breadcrumbs
  home: 'Home',
  crm_portal: 'CRM portal',
  product: 'Product',
  salesman_insights: 'Salesman insights',
  customer_insights: 'Customer insights',
  
  // Tab labels
  summary: 'Summary',
  stone_card: 'Stone card',
  stock: 'Stock',
  orders: 'Orders',
  sales: 'Sales',
  memo: 'Memo',
  payment_receipts: 'Payment - Receipts',
  proposal: 'Proposal',
  contacts: 'Contacts',
  
  // Action items
  compare: 'Compare',
  reserve: 'Reserve',
  
  // Product related
  jewellery_purity_you_can_trust: 'Jewellery Purity You Can Trust!',
  details: 'Details',
  finding: 'Finding',
  colorstone: 'Colorstone',
  
  // Stone card related
  stone_card_number: 'Stone card number',
  suffix: 'Suffix',
  diamond_cts: 'Diamond cts',
  diamond_pcs: 'Diamond pcs',
  stone_cts: 'Stone cts',
  stone_pcs: 'Stone pcs',
  created_on: 'Created on',
  modified_by: 'Modified by',
  modified_on: 'Modified on',
  
  // Memo related
  job_number: 'Job number',
  style_no: 'Style no',
  description: 'Description',
  jid: 'JID',
  quantity: 'Quantity',
  rate: 'Rate',
  amount: 'Amount',
  
  // Payment receipts related
  settlement_no: 'Settlement no',
  reference_number: 'Reference number',
  date: 'Date',
  customer_name: 'Customer name',
  bank_ac_name: 'Bank ac name',
  cheque_no: 'Cheque no',
  invoice_no: 'Invoice no',
  invoice_amount: 'Invoice amount',
  settled_amount: 'Settled amount',
  discount: 'Discount',
  
  // Gemstone chart related
  shape: 'Shape',
  clarity: 'Clarity',
  sieve_size: 'Sieve size',
  per_weight: 'Per weight',
  inter_quality: 'Inter quality',
  
  // Product insights related
  style_reference_number: 'Style reference number',
  label_number: 'Label number',
  stock_type: 'Stock type',
  metal: 'Metal',
  quality: 'Quality',
  tag_desc: 'Tag desc',
  sales_price: 'Sales price',
  msrp: 'MSRP',
  
  // Common terms
  natural_diamonds: 'Natural Diamonds',
  loose_diamonds: 'Loose Diamonds',
  engagement_ring: 'Engagement ring',
  diamond_classic: 'Diamond Classic',
  solitaire: 'Solitaire',
  classic_diamond: 'Classic Diamond',
  
  // Colors and properties
  pink: 'Pink',
  very_light_yellow: 'Very light yellow',
  light_yellow: 'Light yellow',
  nfye: 'NFYE',
  fancy_yellow: 'Fancy yellow',
  fbgy: 'FBGY',
  yellow: 'Yellow',
  
  // Shapes
  round: 'Round',
  commenter: 'Commenter',
  princess: 'Princess',
  emerald: 'Emerald',
  cushion: 'Cushion',
  heart: 'Heart',
  asscher: 'Asscher',
  
  // Metals
  '14k_yellow_gold': '14K yellow gold',
  '14kwh': '14KWH',
  
  // Chart bases
  per_gram: 'Per gram',
  sieve_size_base: 'Sieve size',
  
  // Commodities
  gold_finding: 'Gold finding',
  diamond: 'Diamond',
  
  // Address types
  house: 'House',
  
  // Countries
  united_states: 'United States',
  
  // Yes/No values
  yes: 'Yes',
  no: 'No',
  
  // Units and measurements
  cts: 'cts',
  pcs: 'pcs',
  gram: 'gram',
  
  // File and document types
  pdf: 'PDF',
  excel: 'Excel',
  csv: 'CSV',
  
  // Status values
  active: 'Active',
  inactive: 'Inactive',
  pending: 'Pending',
  completed: 'Completed',
  cancelled: 'Cancelled',
  
  // Time periods
  today: 'Today',
  yesterday: 'Yesterday',
  last_week: 'Last week',
  last_month: 'Last month',
  last_year: 'Last year',
  
  // Sorting and filtering
  sort_by: 'Sort by',
  filter_by: 'Filter by',
  clear_all: 'Clear all',
  apply: 'Apply',
  reset: 'Reset',
  
  // Pagination
  showing: 'Showing',
  of: 'Of',
  show: 'Show',
  entries: 'entries',
  
  // Messages
  no_data_found: 'No data found',
  loading: 'Loading...',
  error_occurred: 'An error occurred',
  success: 'Success',
  
  // Buttons
  save: 'Save',
  cancel: 'Cancel',
  delete: 'Delete',
  update: 'Update',
  create: 'Create',
  close: 'Close',
  back: 'Back',
  next: 'Next',
  previous: 'Previous',
  
  // Forms
  required_field: 'This field is required',
  invalid_email: 'Invalid email address',
  invalid_phone: 'Invalid phone number',
  password_mismatch: 'Passwords do not match',
  
  // Confirmation messages
  confirm_delete: 'Are you sure you want to delete this item?',
  confirm_update: 'Are you sure you want to update this item?',
  unsaved_changes: 'You have unsaved changes. Do you want to continue?',
};

// Export individual translation keys for easy access
export const {
  filter_accessories,
  search_customer_code,
  add,
  edit,
  remove,
  filter,
  designs,
  concept,
  styles,
  group_name,
  customer,
  commodity,
  color,
  chart_base,
  lot_code,
  sp_rate,
  updated_by,
  updated_on,
  action,
  jewellery_purity_you_can_trust,
  details,
  finding,
  colorstone,
} = translationKeys;
