import { IconProps } from "../type";


export const MapIcon = (props: IconProps) => {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12.6667 6.90909C12.6667 10.7273 8 14 8 14C8 14 3.33334 10.7273 3.33334 6.90909C3.33334 5.60712 3.825 4.35847 4.70017 3.43784C5.57534 2.51721 6.76233 2 8 2C9.23768 2 10.4247 2.51721 11.2998 3.43784C12.175 4.35847 12.6667 5.60712 12.6667 6.90909Z" stroke="#083458" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M8 8.54545C8.85911 8.54545 9.55556 7.81283 9.55556 6.90909C9.55556 6.00535 8.85911 5.27273 8 5.27273C7.14089 5.27273 6.44445 6.00535 6.44445 6.90909C6.44445 7.81283 7.14089 8.54545 8 8.54545Z" stroke="#083458" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>

  )
}
