import { IconProps } from "../../type";


export const <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> = (props: IconProps) => {
  return (
    <svg width="41" height="40" viewBox="0 0 41 40" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12.3556 4H12L8 8.26667V32L12 35.9111L12.1778 36H28.8L32.8889 31.8222V8L28.8889 4.08889L28.7111 4H12.3556ZM13.0667 4.8H28L26.2222 7.28889H14.8444L13.0667 4.8ZM28.7111 4.97778L31.9111 8.17778L29.0667 9.95556L26.7556 7.55556L28.7111 4.97778ZM12.3556 4.97778L14.2222 7.64444L11.8222 9.95556L8.97778 8.35556L12.3556 4.97778ZM16.3556 8H24.7111L20.5333 10.1333L16.3556 8ZM15.2 8.26667L19.2 10.2222L15.6444 10.5778L15.2 8.26667ZM25.8667 8.26667L25.3333 10.6667L21.7778 10.3111L25.8667 8.26667ZM14.4889 8.44444L14.8444 10.4889L12.7111 10.2222L14.4889 8.44444ZM26.5778 8.44444L28.2667 10.1333L26.2222 10.4889L26.5778 8.44444ZM32.1778 8.88889V30.9333L29.4222 29.3333V10.5778L32.1778 8.88889ZM8.71111 9.06667L11.4667 10.6667V29.4222L8.8 31.1111L8.71111 9.06667ZM28.4444 10.8444L26.4 17.7778L26.2222 11.2889L28.4444 10.8444ZM20.5333 10.8444L25.3333 11.2889L25.7778 20L24.8889 28.4444L20.4444 29.0667L16 28.4444L15.1111 20L15.6444 11.2889L20.5333 10.8444ZM12.4444 10.8444L14.9333 11.2889L14.4889 17.8667L12.4444 10.8444ZM28.6222 12.9778V26.9333L26.5778 20L28.6222 12.9778ZM12.2667 13.0667L14.3111 20L12.2667 27.0222V13.0667ZM26.3111 21.9556L28.4444 29.0667L25.6889 28.5333L26.3111 21.9556ZM14.5778 21.9556L15.2 28.5333L12.4444 29.1556L14.5778 21.9556ZM15.9111 29.2444L19.2 29.6889L15.2 31.6444L15.9111 29.2444ZM24.9778 29.2444L25.6 31.6444L21.6889 29.6889L24.9778 29.2444ZM25.8667 29.2444L28.2667 29.7778L26.4 31.5556L25.8667 29.2444ZM15.1111 29.2444L14.4 31.6444L12.6222 29.8667L15.1111 29.2444ZM20.4444 29.8667L24.5333 32H16.2667L20.4444 29.8667ZM29.0667 30.0444L31.9111 31.6444L28.5333 35.0222L26.6667 32.3556L29.0667 30.0444ZM11.8222 30.0444L14.1333 32.4444L12.1778 35.1111L8.97778 31.8222L11.8222 30.0444ZM14.8444 32.7111H25.9556L27.7333 35.2H13.0667L14.8444 32.7111Z" fill="#8C8C8C"/>
    </svg>
    

  )
}
