import { IconProps } from "../../type";


export const <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> = (props: IconProps) => {
  return (
    <svg width="41" height="40" viewBox="0 0 41 40" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fillRule="evenodd" clipRule="evenodd" d="M31.0136 19.9999C31.0136 28.8366 26.1974 35.9998 20.2571 35.9998C14.3162 35.9998 9.5 28.8366 9.5 19.9999C9.5 11.1632 14.3162 4 20.2571 4C26.1974 4 31.0136 11.1632 31.0136 19.9999Z" stroke="#8C8C8C" strokeLinejoin="round"/>
      <path fillRule="evenodd" clipRule="evenodd" d="M20.2586 4L24.1052 8.28637L20.2586 10.9815L16.1836 7.80825L20.2586 4Z" stroke="#8C8C8C" strokeLinejoin="round"/>
      <path d="M14.5825 6.40625L16.1836 7.80715L11.9894 9.7665V14.8461H10.0742" stroke="#8C8C8C" strokeLinejoin="round"/>
      <path d="M25.9296 6.40625L24.1035 8.28527L28.5252 9.7665V14.8461H30.4403" stroke="#8C8C8C" strokeLinejoin="round"/>
      <path d="M16.1811 7.80859L14.7012 19.4131L15.833 25.8458L23.7542 31.0004L20.2561 36.0002L16.1811 31.0004L24.1028 25.8458L25.408 19.4131L24.1028 8.28672" stroke="#8C8C8C" strokeLinejoin="round"/>
      <path fillRule="evenodd" clipRule="evenodd" d="M11.9878 14.8477L14.7021 19.4133L12.4388 23.7599L9.5 20.0004L11.9878 14.8477Z" stroke="#8C8C8C" strokeLinejoin="round"/>
      <path fillRule="evenodd" clipRule="evenodd" d="M28.5266 14.8477L31.0034 19.3038L27.9348 23.7599L25.4102 19.4133L28.5266 14.8477Z" stroke="#8C8C8C" strokeLinejoin="round"/>
      <path d="M28.5239 14.8466L24.7821 14.0787L20.2551 10.9805L15.381 14.0787L11.9863 14.8466" stroke="#8C8C8C" strokeLinejoin="round"/>
      <path d="M14.3714 33.3886L16.1822 30.9985L15.8346 25.8439L12.4395 23.7578" stroke="#8C8C8C" strokeLinejoin="round"/>
      <path d="M25.7562 33.3886L23.7539 30.9985L24.1025 25.8439L27.9323 23.7578" stroke="#8C8C8C" strokeLinejoin="round"/>
      <path d="M16.1817 30.999H12.4389V23.7578H9.80078" stroke="#8C8C8C" strokeLinejoin="round"/>
      <path d="M23.7539 30.999L27.9323 31.2022V23.7578H30.7108" stroke="#8C8C8C" strokeLinejoin="round"/>
    </svg>


  )
}
