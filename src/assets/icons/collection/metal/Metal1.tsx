import { IconProps } from "../../type";


export const Metal1 = (props: IconProps) => {
  return (
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="16" cy="16" r="16" fill="url(#paint0_linear_4796_333955)" />
      <defs>
        <linearGradient id="paint0_linear_4796_333955" x1="0" y1="0" x2="32.6667" y2="32" gradientUnits="userSpaceOnUse">
          <stop stop-color="#FEFEFE" />
          <stop offset="1" stopColor="#CECECE" />
        </linearGradient>
      </defs>
    </svg>
  )
}
