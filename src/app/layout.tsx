import MainLayout from "@/core/layout/main-layout";
import { ModuleThemeProvider } from "@/core/theme-provider/module-theme-provider";
import { getThemeFavicon } from "@/modules/customer-insights/utils/generateFavIcon";
import { Metadata } from "next";

export async function generateMetadata(): Promise<Metadata> {
  const faviconHref = await getThemeFavicon();

  return {
    icons: {
      icon: [
        { url: faviconHref },
      ],
    },
  };
}

export default async function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {

    const res = await fetch(`${process.env.NEXT_PUBLIC_CMS_BASE_URL}/api/theme`, {
        method: "GET",
        next: { revalidate: 100 }
    });


    const data = await res.json();
    const theme = data?.data?.attributes?.theme;


    return (
        <html lang="en" >
            <body>
                <ModuleThemeProvider theme={theme}>
                    <MainLayout theme={theme}>
                        {children}
                    </MainLayout>
                </ModuleThemeProvider>
            </body>
        </html>
    );
}