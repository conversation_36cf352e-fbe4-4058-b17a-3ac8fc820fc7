"use client";
import { BackIcon } from "@/assets/icons/collection/BackIcon";
import {
  breadCrumbsValue2,
  parentTabData,
} from "@/core/salesman-insights-layout/utils/constant";
import { SalesmanInsightsPagesStyle } from "@/core/salesman-insights-layout/utils/style";
import { breadCrumbsValue1 } from "@/modules/salesman-insights/utils/constant";
import { BreadCrumbs, Tab1 } from "@dilpesh/kgk-ui-library";
import Link from "next/link";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import React, { ReactNode, useEffect, useState } from "react";

interface SalesmanCommonLayoutProps {
  children: ReactNode;
}

const SalesmanCommonLayout: React.FC<SalesmanCommonLayoutProps> = ({
  children,
}) => {
  const router: any = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const id = pathname.split("/").filter(Boolean).at(-1);
  const customerName = searchParams.get('name') || 'Customer';

  const currentRoute: any = pathname
    .split("/")
    .filter(Boolean)
    .slice(-2, -1)[0];

  const [parentTab, setParentTab] = useState<any>();
  const [customBreadcrumbs, setCustomBreadcrumbs] = useState<any>(breadCrumbsValue2);

  useEffect(() => {
    const currentTab = parentTabData.find(
      (item: any) => item.path === "/salesman-insights/" + currentRoute
    );
    setParentTab(currentTab?.index);

    // Update breadcrumbs with customer name from query param
    if (shouldShowTabs()) {
      const updatedBreadcrumbs = [...breadCrumbsValue2];
      updatedBreadcrumbs[3] = { title: customerName };
      setCustomBreadcrumbs(updatedBreadcrumbs);
    }
  }, [currentRoute, customerName]);

  const tabChange = (value: any) => {
    // Preserve the name parameter when changing tabs
    router.push(`${value}/${id}?name=${encodeURIComponent(customerName)}`);
  };

  const parentTabHandleChange = (
    event: React.SyntheticEvent,
    newValue: number
  ) => {
    const currentPath = parentTabData.find(
      (item: any) => item.index === newValue
    );
    // Preserve the name parameter when changing tabs
    router.push(`${currentPath?.path}/${id}?name=${encodeURIComponent(customerName)}`);
    setParentTab(newValue);
  };

  const shouldShowTabs = () => {
    const validRoutes = parentTabData
      .map((tab: any) => tab.path.split("/").pop())
      .filter(Boolean);

    return validRoutes.includes(currentRoute);
  };

  return (
    <SalesmanInsightsPagesStyle>
      <div className="container first_view_content">
        {shouldShowTabs() ? (
          <BreadCrumbs value={customBreadcrumbs} />
        ) : (
          <BreadCrumbs value={breadCrumbsValue1} />
        )}

        <div className="back_btn">
          <Link href="/salesman-insights">
            <BackIcon />
            <p>Back</p>
          </Link>
        </div>
        <h1>Salesman insights</h1>
        <div className="tab_main_wpr">
          {shouldShowTabs() ? (
            <Tab1
              tabValue={parentTab}
              tabClick={tabChange}
              tabHandleChange={parentTabHandleChange}
              tabOptions={parentTabData}
            />
          ) : null}
        </div>
        <div className="content-area">{children}</div>
      </div>
    </SalesmanInsightsPagesStyle>
  );
};

export default SalesmanCommonLayout;
