"use client";
import { BreadCrumbs, Tab1 } from "@dilpesh/kgk-ui-library";
import Link from "next/link";
import React, { ReactNode, useEffect, useState } from "react";
import { ProductInsightsPagesStyle } from "@/core/product-insights-layout/utils/style";
import { paths } from "@/modules/constants/constants";
import { usePathname, useRouter } from "next/navigation";
import {
  breadCrumbsValue1,
  parentTabData,
} from "@/core/product-insights-layout/utils/constant";
import { breadCrumbsValue2 } from "@/modules/product-insights/utils/constant";
import { BackIcon } from "@/assets/icons/collection/BackIcon";

interface ProductCommonLayoutProps {
  children: ReactNode;
  // tabValue: number;
  // tabClick: (value: any) => void;
  // tabHandleChange: (event: React.SyntheticEvent, newValue: number) => void;
}

const ProductCommonLayout: React.FC<ProductCommonLayoutProps> = ({
  children,
}) => {
  const router: any = useRouter();
  const pathname = usePathname();
  const id = pathname.split("/").filter(Boolean).at(-1);

  const currentRoute: any = pathname
    .split("/")
    .filter(Boolean)
    .slice(-2, -1)[0];

  const [parentTab, setParentTab] = useState<any>();

  useEffect(() => {
    const currentTab = parentTabData.find(
      (item: any) => item.path === "/product-insights/" + currentRoute
    );
    setParentTab(currentTab?.index);
  }, [currentRoute]);

  const tabChange = (value: any) => {
    router.push(value);
  };

  const parentTabHandleChange = (
    event: React.SyntheticEvent,
    newValue: number
  ) => {
    const currentPath = parentTabData.find(
      (item: any) => item.index === newValue
    );
    router.push(`${currentPath?.path}/${id}`);
    setParentTab(newValue);
  };

  const shouldShowTabs = () => {
    const validRoutes = parentTabData
      .map((tab: any) => tab.path.split("/").pop())
      .filter(Boolean);

    return validRoutes.includes(currentRoute);
  };

  return (
    <ProductInsightsPagesStyle>
      <div className="container first_view_content">
        {shouldShowTabs() ? (
          <BreadCrumbs value={breadCrumbsValue1} />
        ) : (
          <BreadCrumbs value={breadCrumbsValue2} />
        )}

        <div className="back_btn">
          <Link href="/product-insights">
            <BackIcon />
            <p>Back</p>
          </Link>
        </div>
        <h1>Product insights</h1>

        <div className="tab_main_wpr">
          {shouldShowTabs() ? (
            <Tab1
              tabValue={parentTab}
              tabClick={tabChange}
              tabHandleChange={parentTabHandleChange}
              tabOptions={parentTabData}
            />
          ) : null}
        </div>
        <div className="content-area">{children}</div>
      </div>
    </ProductInsightsPagesStyle>
  );
};

export default ProductCommonLayout;
