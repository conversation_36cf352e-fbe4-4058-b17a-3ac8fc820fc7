"use client";
import React, { ReactNode, useEffect, useState } from "react";
import { BreadCrumbs, Tab1 } from "@dilpesh/kgk-ui-library";
import Link from "next/link";
import { BackIcon } from "@magneto-it-solutions/kgk-common-library";

import { usePathname, useRouter } from "next/navigation";
import CustomerInsightsSummary from "@/modules/customer-insights/components/summary";
import CustomerInsightsContacts from "@/modules/customer-insights/components/contacts";
import {
  breadCrumbsValue,
  parentTabData,
} from "@/modules/customer-insights/utils/constant";
import { CustomerInsightsPagesStyle } from "@/core/customer-insights-layout/utils/style";

interface CustomerCommonLayoutProps {
  children: ReactNode;
  // tabValue: number;
  // tabClick: (value: any) => void;
  // tabHandleChange: (event: React.SyntheticEvent, newValue: number) => void;
}

const CustomerCommonLayout: React.FC<CustomerCommonLayoutProps> = ({
  children,
}) => {
  const router: any = useRouter();
  const pathname = usePathname();
  const id = pathname.split("/").filter(Boolean).at(-1);

  const currentRoute: any = pathname
    .split("/")
    .filter(Boolean)
    .slice(-2, -1)[0];

  const [parentTab, setParentTab] = useState<any>();

  useEffect(() => {
    const currentTab = parentTabData.find(
      (item: any) => item.path === "/customer-insights/" + currentRoute
    );
    setParentTab(currentTab?.index);
  }, [currentRoute]);

  const tabChange = (value: any) => {
    router.push(value);
  };

  const parentTabHandleChange = (
    event: React.SyntheticEvent,
    newValue: number
  ) => {
    const currentPath = parentTabData.find(
      (item: any) => item.index === newValue
    );
    router.push(`${currentPath?.path}/${id}`);
    setParentTab(newValue);
  };

  const shouldShowTabs = () => {
    const validRoutes = parentTabData
      .map((tab: any) => tab.path.split("/").pop())
      .filter(Boolean);

    return validRoutes.includes(currentRoute);
  };

  return (
    <CustomerInsightsPagesStyle>
      <div className="container first_view_content">
        <BreadCrumbs value={breadCrumbsValue} />

        <div className="back_btn">
          <Link href="/customer-insights">
            <BackIcon />
            <p>Back</p>
          </Link>
        </div>
        <h1>Customer Insights</h1>
        <div className="tab_main_wpr">
          {shouldShowTabs() ? (
            <Tab1
              tabValue={parentTab}
              tabClick={tabChange}
              tabHandleChange={parentTabHandleChange}
              tabOptions={parentTabData}
            />
          ) : null}
        </div>
        <div className="content-area">{children}</div>
      </div>
    </CustomerInsightsPagesStyle>
  );
};

export default CustomerCommonLayout;
