{"name": "crm", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -p 3008", "lint": "next lint"}, "overrides": {"typescript": "^5"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@ant-design/nextjs-registry": "^1.0.0", "@dilpesh/kgk-ui-library": "^2.7.31", "@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@magneto-it-solutions/kgk-common-library": "1.5.80", "@mui/material": "^5.14.13", "@reduxjs/toolkit": "^2.2.4", "@types/react-toastify": "^4.0.2", "@types/styled-components": "^5.1.29", "chart.js": "^4.3.2", "date-fns": "^3.6.0", "immer": "^10.1.1", "js-cookie": "^3.0.5", "next": "14.2.3", "react": "^18", "react-chartjs-2": "^5.2.0", "react-dom": "^18", "react-hook-form": "^7.43.5", "react-qr-code": "^2.0.15", "react-redux": "^9.1.2", "react-share": "^5.1.0", "react-toastify": "^10.0.6", "redux": "^5.0.1", "styled-components": "^5.3.1", "swiper": "^11.1.3"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "faker": "^5.5.3", "typescript": "^5"}}